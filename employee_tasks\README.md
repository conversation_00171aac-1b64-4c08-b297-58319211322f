# نظام مهام الموظفين (Employee Tasks System)

## نظرة عامة
نظام مهام الموظفين هو تطبيق متكامل لإدارة مهام الموظفين في نظام الدولية. يوفر النظام واجهة احترافية لإدارة المهام الشخصية للموظفين، مع إمكانية تتبع خطوات المهام، وعرض المهام في تقويم، وتحليل أداء المهام.

## الميزات الرئيسية

### إدارة المهام
- إنشاء وتعديل وحذف المهام الشخصية
- تصنيف المهام حسب الأولوية والحالة والتصنيف
- تتبع نسبة إنجاز المهام
- إمكانية إسناد المهام إلى موظفين آخرين
- خصوصية المهام (المهام الخاصة لا يراها إلا المنشئ والمشرف)

### خطوات المهام
- إضافة خطوات تفصيلية لكل مهمة
- تتبع حالة إنجاز كل خطوة
- تحديث نسبة إنجاز المهمة تلقائيًا بناءً على الخطوات المكتملة

### التذكيرات
- إضافة تذكيرات للمهام
- تنبيهات للمهام المستحقة والمتأخرة

### التقويم
- عرض المهام في تقويم شهري/أسبوعي/يومي
- تمييز المهام حسب الحالة والأولوية
- إمكانية طباعة التقويم

### التحليلات
- إحصائيات عن المهام حسب الحالة والأولوية والتصنيف
- تحليل أداء المهام على مدار الوقت
- مقاييس الكفاءة ونسب الإنجاز

### التصنيفات
- إنشاء وتعديل تصنيفات المهام
- تخصيص ألوان وأيقونات لكل تصنيف

## نظام الصلاحيات
- تكامل مع نظام الصلاحيات الموجود في النظام
- صلاحيات على مستوى القسم والوحدة والعملية
- المستخدم العادي يرى فقط المهام التي أنشأها أو المسندة إليه
- المشرف يرى جميع المهام

## المتطلبات التقنية
- Django 3.2+
- Python 3.8+
- JavaScript (jQuery, FullCalendar.js, Chart.js)
- Bootstrap 5

## التكامل مع النظام
- تكامل مع نظام المستخدمين الحالي
- تكامل مع نظام التنبيهات
- تكامل مع نظام الصلاحيات

## الاستخدام
1. الوصول إلى لوحة تحكم مهام الموظفين من القائمة الجانبية
2. إنشاء مهمة جديدة من خلال زر "إنشاء مهمة جديدة"
3. إضافة خطوات للمهمة من صفحة تفاصيل المهمة
4. تحديث حالة المهمة ونسبة الإنجاز
5. عرض المهام في التقويم أو صفحة التحليلات

## المطورون
- تم تطوير النظام بواسطة فريق تطوير نظام الدولية
