# Generated by Django 5.0.14 on 2025-05-18 14:24

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('administrator', '0001_initial'),
        ('auth', '0012_alter_user_first_name_max_length'),
        ('contenttypes', '0002_remove_content_type_name'),
    ]

    operations = [
        migrations.AddField(
            model_name='permissionauditlog',
            name='error_message',
            field=models.TextField(blank=True, null=True, verbose_name='رسالة الخطأ'),
        ),
        migrations.AddField(
            model_name='permissionauditlog',
            name='ip_address',
            field=models.GenericIPAddressField(blank=True, null=True, verbose_name='عنوان IP'),
        ),
        migrations.AddField(
            model_name='permissionauditlog',
            name='success',
            field=models.BooleanField(default=True, verbose_name='نجاح'),
        ),
        migrations.AddField(
            model_name='permissionauditlog',
            name='user_agent',
            field=models.TextField(blank=True, null=True, verbose_name='وكيل المستخدم'),
        ),
        migrations.AlterField(
            model_name='permissionauditlog',
            name='action',
            field=models.CharField(choices=[('add', 'إضافة'), ('remove', 'إزالة'), ('modify', 'تعديل'), ('assign', 'تعيين'), ('revoke', 'سحب'), ('login', 'تسجيل دخول'), ('logout', 'تسجيل خروج'), ('view', 'عرض')], max_length=10, verbose_name='الإجراء'),
        ),
        migrations.AlterField(
            model_name='permissionauditlog',
            name='content_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype', verbose_name='نوع المحتوى'),
        ),
        migrations.AlterField(
            model_name='permissionauditlog',
            name='object_id',
            field=models.PositiveIntegerField(blank=True, null=True, verbose_name='معرف الكائن'),
        ),
        migrations.AlterField(
            model_name='permissionauditlog',
            name='object_type',
            field=models.CharField(choices=[('user', 'مستخدم'), ('group', 'مجموعة'), ('permission', 'صلاحية'), ('module', 'وحدة'), ('department', 'قسم'), ('template', 'قالب'), ('role', 'دور'), ('operation', 'عملية'), ('page', 'صفحة'), ('system', 'نظام')], max_length=20, verbose_name='نوع الكائن'),
        ),
        migrations.CreateModel(
            name='PermissionGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='اسم المجموعة')),
                ('description', models.TextField(blank=True, verbose_name='وصف المجموعة')),
                ('category', models.CharField(blank=True, max_length=50, verbose_name='الفئة')),
                ('is_system', models.BooleanField(default=False, help_text='مجموعات النظام لا يمكن حذفها', verbose_name='مجموعة نظام')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('permissions', models.ManyToManyField(blank=True, related_name='permission_groups', to='auth.permission', verbose_name='الصلاحيات')),
            ],
            options={
                'verbose_name': 'مجموعة صلاحيات',
                'verbose_name_plural': 'مجموعات الصلاحيات',
                'ordering': ['name'],
            },
        ),
    ]
