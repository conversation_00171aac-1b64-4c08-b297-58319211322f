{% extends 'Hr/base_hr.html' %}
{% load static %}

{% block title %}{{ title }} - نظام الدولية{% endblock %}

{% block page_title %}{{ title }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:list' %}">الموظفين</a></li>
<li class="breadcrumb-item active">{{ title }}</li>
{% endblock %}

{% block content %}
<div class="card shadow-sm">
    <div class="card-header bg-white d-flex justify-content-between align-items-center py-3">
        <h5 class="mb-0">
            <i class="fas fa-user-plus me-2"></i>
            {{ title }}
        </h5>
        <a href="{% url 'Hr:list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة للقائمة
        </a>
    </div>
    <div class="card-body">
        <form method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
            {% csrf_token %}
            
            {% if form.non_field_errors %}
            <div class="alert alert-danger" role="alert">
                {% for error in form.non_field_errors %}
                <p class="mb-0">{{ error }}</p>
                {% endfor %}
            </div>
            {% endif %}
            
            <!-- تنظيم الحقول في أقسام -->
            <div class="accordion mb-4" id="employeeFormAccordion">
                <!-- المعلومات الأساسية -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="heading1">
                        <button class="accordion-button" type="button" 
                                data-bs-toggle="collapse" data-bs-target="#collapse1" 
                                aria-expanded="true" aria-controls="collapse1">
                            المعلومات الأساسية
                        </button>
                    </h2>
                    <div id="collapse1" class="accordion-collapse collapse show" 
                         aria-labelledby="heading1" data-bs-parent="#employeeFormAccordion">
                        <div class="accordion-body">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.emp_id.id_for_label }}" class="form-label">{{ form.emp_id.label }}</label>
                                    {{ form.emp_id }}
                                    {% if form.emp_id.errors %}
                                    <div class="invalid-feedback d-block">{{ form.emp_id.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.emp_first_name.id_for_label }}" class="form-label">{{ form.emp_first_name.label }}</label>
                                    {{ form.emp_first_name }}
                                    {% if form.emp_first_name.errors %}
                                    <div class="invalid-feedback d-block">{{ form.emp_first_name.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.emp_second_name.id_for_label }}" class="form-label">{{ form.emp_second_name.label }}</label>
                                    {{ form.emp_second_name }}
                                    {% if form.emp_second_name.errors %}
                                    <div class="invalid-feedback d-block">{{ form.emp_second_name.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.emp_full_name.id_for_label }}" class="form-label">{{ form.emp_full_name.label }}</label>
                                    {{ form.emp_full_name }}
                                    {% if form.emp_full_name.errors %}
                                    <div class="invalid-feedback d-block">{{ form.emp_full_name.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.emp_name_english.id_for_label }}" class="form-label">{{ form.emp_name_english.label }}</label>
                                    {{ form.emp_name_english }}
                                    {% if form.emp_name_english.errors %}
                                    <div class="invalid-feedback d-block">{{ form.emp_name_english.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.mother_name.id_for_label }}" class="form-label">{{ form.mother_name.label }}</label>
                                    {{ form.mother_name }}
                                    {% if form.mother_name.errors %}
                                    <div class="invalid-feedback d-block">{{ form.mother_name.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- معلومات الهوية -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="heading2">
                        <button class="accordion-button collapsed" type="button" 
                                data-bs-toggle="collapse" data-bs-target="#collapse2" 
                                aria-expanded="false" aria-controls="collapse2">
                            معلومات الهوية
                        </button>
                    </h2>
                    <div id="collapse2" class="accordion-collapse collapse" 
                         aria-labelledby="heading2" data-bs-parent="#employeeFormAccordion">
                        <div class="accordion-body">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.national_id.id_for_label }}" class="form-label">{{ form.national_id.label }}</label>
                                    {{ form.national_id }}
                                    {% if form.national_id.errors %}
                                    <div class="invalid-feedback d-block">{{ form.national_id.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.date_birth.id_for_label }}" class="form-label">{{ form.date_birth.label }}</label>
                                    {{ form.date_birth }}
                                    {% if form.date_birth.errors %}
                                    <div class="invalid-feedback d-block">{{ form.date_birth.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.place_birth.id_for_label }}" class="form-label">{{ form.place_birth.label }}</label>
                                    {{ form.place_birth }}
                                    {% if form.place_birth.errors %}
                                    <div class="invalid-feedback d-block">{{ form.place_birth.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.emp_nationality.id_for_label }}" class="form-label">{{ form.emp_nationality.label }}</label>
                                    {{ form.emp_nationality }}
                                    {% if form.emp_nationality.errors %}
                                    <div class="invalid-feedback d-block">{{ form.emp_nationality.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.emp_marital_status.id_for_label }}" class="form-label">{{ form.emp_marital_status.label }}</label>
                                    {{ form.emp_marital_status }}
                                    {% if form.emp_marital_status.errors %}
                                    <div class="invalid-feedback d-block">{{ form.emp_marital_status.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.military_service_certificate.id_for_label }}" class="form-label">{{ form.military_service_certificate.label }}</label>
                                    {{ form.military_service_certificate }}
                                    {% if form.military_service_certificate.errors %}
                                    <div class="invalid-feedback d-block">{{ form.military_service_certificate.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="form-check">
                                        {{ form.people_with_special_needs }}
                                        <label class="form-check-label" for="{{ form.people_with_special_needs.id_for_label }}">
                                            {{ form.people_with_special_needs.label }}
                                        </label>
                                        {% if form.people_with_special_needs.errors %}
                                        <div class="invalid-feedback d-block">{{ form.people_with_special_needs.errors }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- بيانات الاتصال -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="heading3">
                        <button class="accordion-button collapsed" type="button" 
                                data-bs-toggle="collapse" data-bs-target="#collapse3" 
                                aria-expanded="false" aria-controls="collapse3">
                            بيانات الاتصال
                        </button>
                    </h2>
                    <div id="collapse3" class="accordion-collapse collapse" 
                         aria-labelledby="heading3" data-bs-parent="#employeeFormAccordion">
                        <div class="accordion-body">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.emp_phone1.id_for_label }}" class="form-label">{{ form.emp_phone1.label }}</label>
                                    {{ form.emp_phone1 }}
                                    {% if form.emp_phone1.errors %}
                                    <div class="invalid-feedback d-block">{{ form.emp_phone1.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.emp_phone2.id_for_label }}" class="form-label">{{ form.emp_phone2.label }}</label>
                                    {{ form.emp_phone2 }}
                                    {% if form.emp_phone2.errors %}
                                    <div class="invalid-feedback d-block">{{ form.emp_phone2.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.emp_address.id_for_label }}" class="form-label">{{ form.emp_address.label }}</label>
                                    {{ form.emp_address }}
                                    {% if form.emp_address.errors %}
                                    <div class="invalid-feedback d-block">{{ form.emp_address.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.governorate.id_for_label }}" class="form-label">{{ form.governorate.label }}</label>
                                    {{ form.governorate }}
                                    {% if form.governorate.errors %}
                                    <div class="invalid-feedback d-block">{{ form.governorate.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- معلومات العمل -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="heading4">
                        <button class="accordion-button collapsed" type="button" 
                                data-bs-toggle="collapse" data-bs-target="#collapse4" 
                                aria-expanded="false" aria-controls="collapse4">
                            معلومات العمل
                        </button>
                    </h2>
                    <div id="collapse4" class="accordion-collapse collapse" 
                         aria-labelledby="heading4" data-bs-parent="#employeeFormAccordion">
                        <div class="accordion-body">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.emp_type.id_for_label }}" class="form-label">{{ form.emp_type.label }}</label>
                                    {{ form.emp_type }}
                                    {% if form.emp_type.errors %}
                                    <div class="invalid-feedback d-block">{{ form.emp_type.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.working_condition.id_for_label }}" class="form-label">{{ form.working_condition.label }}</label>
                                    {{ form.working_condition }}
                                    {% if form.working_condition.errors %}
                                    <div class="invalid-feedback d-block">{{ form.working_condition.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.department.id_for_label }}" class="form-label">{{ form.department.label }}</label>
                                    {{ form.department }}
                                    {% if form.department.errors %}
                                    <div class="invalid-feedback d-block">{{ form.department.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.jop_code.id_for_label }}" class="form-label">{{ form.jop_code.label }}</label>
                                    {{ form.jop_code }}
                                    {% if form.jop_code.errors %}
                                    <div class="invalid-feedback d-block">{{ form.jop_code.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.jop_name.id_for_label }}" class="form-label">{{ form.jop_name.label }}</label>
                                    {{ form.jop_name }}
                                    {% if form.jop_name.errors %}
                                    <div class="invalid-feedback d-block">{{ form.jop_name.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.emp_date_hiring.id_for_label }}" class="form-label">{{ form.emp_date_hiring.label }}</label>
                                    {{ form.emp_date_hiring }}
                                    {% if form.emp_date_hiring.errors %}
                                    <div class="invalid-feedback d-block">{{ form.emp_date_hiring.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- معلومات السيارة -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="heading5">
                        <button class="accordion-button collapsed" type="button" 
                                data-bs-toggle="collapse" data-bs-target="#collapse5" 
                                aria-expanded="false" aria-controls="collapse5">
                            معلومات السيارة
                        </button>
                    </h2>
                    <div id="collapse5" class="accordion-collapse collapse" 
                         aria-labelledby="heading5" data-bs-parent="#employeeFormAccordion">
                        <div class="accordion-body">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.emp_car.id_for_label }}" class="form-label">{{ form.emp_car.label }}</label>
                                    {{ form.emp_car }}
                                    {% if form.emp_car.errors %}
                                    <div class="invalid-feedback d-block">{{ form.emp_car.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- معلومات التأمين -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="heading6">
                        <button class="accordion-button collapsed" type="button" 
                                data-bs-toggle="collapse" data-bs-target="#collapse6" 
                                aria-expanded="false" aria-controls="collapse6">
                            معلومات التأمين
                        </button>
                    </h2>
                    <div id="collapse6" class="accordion-collapse collapse" 
                         aria-labelledby="heading6" data-bs-parent="#employeeFormAccordion">
                        <div class="accordion-body">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.insurance_status.id_for_label }}" class="form-label">{{ form.insurance_status.label }}</label>
                                    {{ form.insurance_status }}
                                    {% if form.insurance_status.errors %}
                                    <div class="invalid-feedback d-block">{{ form.insurance_status.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.insurance_salary.id_for_label }}" class="form-label">{{ form.insurance_salary.label }}</label>
                                    {{ form.insurance_salary }}
                                    {% if form.insurance_salary.errors %}
                                    <div class="invalid-feedback d-block">{{ form.insurance_salary.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.health_card.id_for_label }}" class="form-label">{{ form.health_card.label }}</label>
                                    {{ form.health_card }}
                                    {% if form.health_card.errors %}
                                    <div class="invalid-feedback d-block">{{ form.health_card.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.shift_type.id_for_label }}" class="form-label">{{ form.shift_type.label }}</label>
                                    {{ form.shift_type }}
                                    {% if form.shift_type.errors %}
                                    <div class="invalid-feedback d-block">{{ form.shift_type.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="{% url 'Hr:list' %}" class="btn btn-light me-md-2">إلغاء</a>
                <button type="submit" class="btn btn-primary px-4">
                    <i class="fas fa-save me-1"></i>
                    {{ button_text }}
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحقق من صحة النماذج
    (function () {
        'use strict'
        
        // أشكال تحتاج إلى التحقق من صحتها
        var forms = document.querySelectorAll('.needs-validation')
        
        // حلقة عليهم ومنع الإرسال
        Array.prototype.slice.call(forms)
            .forEach(function (form) {
                form.addEventListener('submit', function (event) {
                    if (!form.checkValidity()) {
                        event.preventDefault()
                        event.stopPropagation()
                    }
                    
                    form.classList.add('was-validated')
                }, false)
            })
    })()
    
    // تعبئة الاسم الكامل تلقائيًا
    const firstNameInput = document.getElementById('id_emp_first_name');
    const secondNameInput = document.getElementById('id_emp_second_name');
    const fullNameInput = document.getElementById('id_emp_full_name');
    
    if (firstNameInput && secondNameInput && fullNameInput) {
        const updateFullName = () => {
            const firstName = firstNameInput.value.trim() || '';
            const secondName = secondNameInput.value.trim() || '';
            
            if (firstName || secondName) {
                fullNameInput.value = [firstName, secondName].filter(Boolean).join(' ');
            }
        };
        
        firstNameInput.addEventListener('blur', updateFullName);
        secondNameInput.addEventListener('blur', updateFullName);
    }
    
    // حساب مبلغ التأمين المستحق
    const insuranceSalaryInput = document.getElementById('id_insurance_salary');
    const percentageInput = document.getElementById('id_percentage_insurance_payable');
    const dueAmountInput = document.getElementById('id_due_insurance_amount');
    
    if (insuranceSalaryInput && percentageInput && dueAmountInput) {
        const updateDueAmount = () => {
            const salary = parseFloat(insuranceSalaryInput.value) || 0;
            const percentage = parseFloat(percentageInput.value) || 0;
            
            if (salary && percentage) {
                dueAmountInput.value = (salary * percentage / 100).toFixed(2);
            } else {
                dueAmountInput.value = '';
            }
        };
        
        insuranceSalaryInput.addEventListener('input', updateDueAmount);
        percentageInput.addEventListener('input', updateDueAmount);
    }
});
</script>
{% endblock %}

{% block extra_css %}
<style>
    /* إعدادات التصميم لنموذج الموظف */
    .accordion-button:not(.collapsed) {
        background-color: rgba(13, 110, 253, 0.1);
        color: #0d6efd;
    }
    
    .accordion-button:focus {
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }
</style>
{% endblock %}
