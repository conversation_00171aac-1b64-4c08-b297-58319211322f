# Generated by Django 5.0.14 on 2025-05-07 11:13

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Car',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('car_code', models.CharField(max_length=20, unique=True)),
                ('car_name', models.CharField(max_length=100)),
                ('car_type', models.CharField(choices=[('microbus', 'Microbus'), ('bus', 'Bus'), ('passenger', 'Passenger'), ('private', 'Private')], max_length=20)),
                ('distance_traveled', models.FloatField(default=0)),
                ('fuel_type', models.CharField(choices=[('diesel', 'Diesel'), ('gasoline', 'Gasoline'), ('gas', 'Gas')], max_length=20)),
                ('passengers_count', models.PositiveIntegerField()),
                ('car_status', models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive')], default='active', max_length=20)),
                ('fuel_consumption_rate', models.FloatField(help_text='Fuel consumption rate in liters/km')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['car_code'],
            },
        ),
        migrations.CreateModel(
            name='Settings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('diesel_price', models.FloatField(default=10)),
                ('gasoline_price', models.FloatField(default=12)),
                ('gas_price', models.FloatField(default=8)),
                ('maintenance_rate', models.FloatField(default=0.25, help_text='Maintenance cost per km')),
                ('depreciation_rate', models.FloatField(default=0.3, help_text='Depreciation cost per km')),
                ('license_rate', models.FloatField(default=0.1, help_text='License cost per km')),
                ('driver_profit_rate', models.FloatField(default=0.5, help_text='Driver profit per km')),
                ('tax_rate', models.FloatField(default=14, help_text='Tax percentage')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Settings',
                'verbose_name_plural': 'Settings',
            },
        ),
        migrations.CreateModel(
            name='Trip',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('distance', models.FloatField(help_text='Distance in km')),
                ('fuel_cost', models.FloatField(blank=True, null=True)),
                ('maintenance_cost', models.FloatField(blank=True, null=True)),
                ('depreciation_cost', models.FloatField(blank=True, null=True)),
                ('license_cost', models.FloatField(blank=True, null=True)),
                ('driver_profit', models.FloatField(blank=True, null=True)),
                ('total_base_cost', models.FloatField(blank=True, null=True)),
                ('tax_amount', models.FloatField(blank=True, null=True)),
                ('final_price', models.FloatField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('car', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='trips', to='cars.car')),
            ],
            options={
                'ordering': ['-date', 'car__car_code'],
            },
        ),
    ]
