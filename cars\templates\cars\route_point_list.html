{% extends 'cars\base.html' %}

{% block title %}نقاط خط السير - نظام إدارة نشاط النقل{% endblock %}

{% block header %}نقاط خط السير للسيارة: {{ car.car_name }}{% endblock %}

{% block content %}
    <div class="mb-3 d-flex justify-content-between align-items-center">
        <div>
            <h5 class="mb-0">كود السيارة: {{ car.car_code }}</h5>
            <p class="text-muted mb-0">{% if car.supplier %}المورد: {{ car.supplier.name }}{% else %}لا يوجد مورد{% endif %}</p>
        </div>
        <div>
            <a href="{% url 'cars:car_route_report' car.id %}" class="btn btn-info me-2">
                <i class="bi bi-file-text"></i> تقرير خط السير
            </a>
            <a href="{% url 'cars:route_point_add' car.id %}" class="btn btn-success">
                <i class="bi bi-plus-circle"></i> إضافة نقطة جديدة
            </a>
        </div>
    </div>

    <div class="table-container">
        <table class="table table-striped table-hover">
            <thead class="table-dark">
                <tr>
                    <th>الترتيب</th>
                    <th>اسم النقطة</th>
                    <th>وقت المغادرة</th>
                    <th>عدد الموظفين</th>
                    <th>العمليات</th>
                </tr>
            </thead>
            <tbody>
                {% for point in route_points %}
                    <tr>
                        <td>{{ point.order }}</td>
                        <td>{{ point.point_name }}</td>
                        <td>{{ point.departure_time }}</td>
                        <td>{{ point.employees.count }}</td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{% url 'cars:route_point_edit' point.id %}" class="btn btn-sm btn-primary" title="تعديل">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <a href="{% url 'cars:route_point_delete' point.id %}" class="btn btn-sm btn-danger" title="حذف">
                                    <i class="bi bi-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                {% empty %}
                    <tr>
                        <td colspan="5" class="text-center py-4">
                            <div class="alert alert-info mb-0">
                                لا توجد نقاط خط سير مسجلة لهذه السيارة. قم بإضافة نقطة جديدة من خلال زر الإضافة.
                            </div>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    <div class="mt-3">
        <a href="{% url 'cars:car_list' %}" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> العودة لقائمة السيارات
        </a>
    </div>
{% endblock %}