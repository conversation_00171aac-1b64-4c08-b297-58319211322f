{% extends 'cars\base.html' %}
{% load custom_filters %}

{% block title %}حساب متوسط السعر - نظام إدارة نشاط النقل{% endblock %}

{% block header %}حساب متوسط سعر التشغيل{% endblock %}

{% block content %}
    <div class="mb-3">
        <p class="alert alert-info">
            <i class="bi bi-info-circle me-2"></i>
            هذه الصفحة تعرض متوسط سعر التشغيل لكل سيارة نشطة بناءً على الإعدادات الحالية. يتم حساب السعر لمسافة قياسية 100 كم.
        </p>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="card-title mb-0">الإعدادات المستخدمة في الحساب</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                    <strong>سعر سولار:</strong>
                    <p>{{ settings.diesel_price|floatformat:2 }} ج.م</p>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                    <strong>سعر بنزين:</strong>
                    <p>{{ settings.gasoline_price|floatformat:2 }} ج.م</p>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                    <strong>سعر غاز:</strong>
                    <p>{{ settings.gas_price|floatformat:2 }} ج.م</p>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                    <strong>معدل الصيانة:</strong>
                    <p>{{ settings.maintenance_rate|floatformat:2 }} ج.م/كم</p>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                    <strong>معدل الإهلاك:</strong>
                    <p>{{ settings.depreciation_rate|floatformat:2 }} ج.م/كم</p>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                    <strong>نسبة الضريبة:</strong>
                    <p>{{ settings.tax_rate|floatformat:2 }}%</p>
                </div>
            </div>
            <div class="text-end">
                <a href="{% url 'cars:settings_edit' %}" class="btn btn-outline-primary">
                    <i class="bi bi-gear"></i> تعديل الإعدادات
                </a>
            </div>
        </div>
    </div>

    <div class="table-container mb-4">
        <table class="table table-striped table-bordered">
            <thead class="table-dark">
                <tr>
                    <th>كود السيارة</th>
                    <th>اسم السيارة</th>
                    <th>نوع السيارة</th>
                    <th>نوع الوقود</th>
                    <th>معدل استهلاك الوقود</th>
                    <th>تكلفة الوقود</th>
                    <th>تكلفة الصيانة</th>
                    <th>الإهلاك</th>
                    <th>ترخيص السيارة</th>
                    <th>ربح السائق</th>
                    <th>إجمالي التكلفة الأساسية</th>
                    <th>القيمة الضريبية</th>
                    <th>إجمالي التكلفة</th>
                    <th>متوسط السعر/كم</th>
                </tr>
            </thead>
            <tbody>
                {% for car_data in cars %}
                    <tr>
                        <td>{{ car_data.car.car_code }}</td>
                        <td>{{ car_data.car.car_name }}</td>
                        <td>
                            {% if car_data.car.car_type == 'microbus' %}
                                ميكروباص
                            {% elif car_data.car.car_type == 'bus' %}
                                أتوبيس
                            {% elif car_data.car.car_type == 'passenger' %}
                                ركاب
                            {% elif car_data.car.car_type == 'private' %}
                                ملاكي
                            {% else %}
                                {{ car_data.car.car_type }}
                            {% endif %}
                        </td>
                        <td>
                            {% if car_data.car.fuel_type == 'diesel' %}
                                سولار ({{ car_data.fuel_price|floatformat:2 }} ج.م)
                            {% elif car_data.car.fuel_type == 'gasoline' %}
                                بنزين ({{ car_data.fuel_price|floatformat:2 }} ج.م)
                            {% elif car_data.car.fuel_type == 'gas' %}
                                غاز ({{ car_data.fuel_price|floatformat:2 }} ج.م)
                            {% else %}
                                {{ car_data.car.fuel_type }}
                            {% endif %}
                        </td>
                        <td>{{ car_data.car.fuel_consumption_rate|floatformat:2 }} لتر/كم</td>
                        <td>{{ car_data.fuel_cost|floatformat:2 }} ج.م</td>
                        <td>{{ car_data.maintenance_cost|floatformat:2 }} ج.م</td>
                        <td>{{ car_data.depreciation_cost|floatformat:2 }} ج.م</td>
                        <td>{{ car_data.license_cost|floatformat:2 }} ج.م</td>
                        <td>{{ car_data.driver_profit|floatformat:2 }} ج.م</td>
                        <td>{{ car_data.total_base_cost|floatformat:2 }} ج.م</td>
                        <td>{{ car_data.tax_amount|floatformat:2 }} ج.م</td>
                        <td>{{ car_data.final_price|floatformat:2 }} ج.م</td>
                        <td class="fw-bold bg-light text-success">{{ car_data.average_price|floatformat:2 }} ج.م</td>
                    </tr>
                {% empty %}
                    <tr>
                        <td colspan="14" class="text-center py-4">
                            <div class="alert alert-warning mb-0">
                                لا توجد سيارات نشطة حاليًا. قم بإضافة سيارة من خلال 
                                <a href="{% url 'cars:car_add' %}" class="alert-link">صفحة إضافة سيارة</a>.
                            </div>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    {% if cars %}
    <div class="card">
        <div class="card-header bg-success text-white">
            <h5 class="card-title mb-0">الملخص والتوصيات</h5>
        </div>
        <div class="card-body">
            <h6>السيارات الأكثر اقتصاداً في التشغيل:</h6>
            <div class="table-responsive">
                <table class="table table-sm table-bordered">
                    <thead class="table-light">
                        <tr>
                            <th>الترتيب</th>
                            <th>السيارة</th>
                            <th>متوسط السعر/كم</th>
                            <th>نوع الوقود</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for car_data in cars|slice:":5" %}
                            <tr>
                                <td>{{ forloop.counter }}</td>
                                <td>{{ car_data.car.car_name }}</td>
                                <td>{{ car_data.average_price|floatformat:2 }} ج.م</td>
                                <td>
                                    {% if car_data.car.fuel_type == 'diesel' %}
                                        سولار
                                    {% elif car_data.car.fuel_type == 'gasoline' %}
                                        بنزين
                                    {% elif car_data.car.fuel_type == 'gas' %}
                                        غاز
                                    {% else %}
                                        {{ car_data.car.fuel_type }}
                                    {% endif %}
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <div class="alert alert-info mt-3">
                <i class="bi bi-lightbulb me-2"></i>
                توصيات لتحسين متوسط سعر التشغيل:
                <ul>
                    <li>تحسين عادات القيادة لتقليل استهلاك الوقود</li>
                    <li>جدولة الصيانة الدورية لمنع المشاكل الكبيرة</li>
                    <li>اختيار الطرق الأكثر كفاءة لتقليل المسافات</li>
                    <li>مراقبة أسعار الوقود واختيار أفضل الأوقات للتزود</li>
                </ul>
            </div>
        </div>
    </div>
    {% endif %}
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Highlight the row with the lowest average price
        const rows = document.querySelectorAll('tbody tr');
        if (rows.length > 0) {
            rows[0].classList.add('table-success');
        }
    });
</script>
{% endblock %}
