{% extends 'Hr/base_hr.html' %}
{% load static %}

{% block title %}{{ title }} - نظام الدولية{% endblock %}

{% block page_title %}{{ title }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموظفين</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:payroll_period_list' %}">فترات الرواتب</a></li>
<li class="breadcrumb-item active">{{ title }}</li>
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">{{ title }}</h5>
    </div>
    <div class="card-body">
        <form method="post" novalidate>
            {% csrf_token %}
            
            {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                {{ error }}
                {% endfor %}
            </div>
            {% endif %}
            
            <div class="row g-3">
                <div class="col-md-6">
                    <label for="{{ form.period.id_for_label }}" class="form-label">{{ form.period.label }}</label>
                    {{ form.period }}
                    {% if form.period.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.period.errors %}
                        {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                    {% if form.period.help_text %}
                    <div class="form-text">{{ form.period.help_text }}</div>
                    {% endif %}
                </div>
                
                <div class="col-md-6">
                    <label for="{{ form.status.id_for_label }}" class="form-label">{{ form.status.label }}</label>
                    {{ form.status }}
                    {% if form.status.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.status.errors %}
                        {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                    {% if form.status.help_text %}
                    <div class="form-text">{{ form.status.help_text }}</div>
                    {% endif %}
                </div>

                <div class="col-md-6">
                    <label for="{{ form.total_amount.id_for_label }}" class="form-label">{{ form.total_amount.label }}</label>
                    {{ form.total_amount }}
                    {% if form.total_amount.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.total_amount.errors %}
                        {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                    {% if form.total_amount.help_text %}
                    <div class="form-text">{{ form.total_amount.help_text }}</div>
                    {% endif %}
                </div>
            </div>

            <div class="mt-4">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i> حفظ
                </button>
                <a href="{% url 'Hr:payroll_period_list' %}" class="btn btn-secondary">
                    <i class="fas fa-times me-1"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}