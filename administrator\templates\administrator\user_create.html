{% extends 'administrator/base_admin.html' %}
{% load static %}

{% block title %}إضافة مستخدم جديد | {{ system_settings.system_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="page-title">إضافة مستخدم جديد</h2>
                <div>
                    <a href="{% url 'administrator:permission_dashboard' %}" class="btn btn-info me-2">
                        <i class="fas fa-question-circle me-1"></i>
                        لوحة الصلاحيات
                    </a>
                    <a href="{% url 'administrator:user_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-1"></i>
                        العودة إلى قائمة المستخدمين
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card border-0 shadow mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">بيانات المستخدم الجديد</h5>
                </div>
                <div class="card-body">
                    <form method="post" id="userForm">
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.username.id_for_label }}" class="form-label fw-bold">{{ form.username.label }} <span class="text-danger">*</span></label>
                                {{ form.username }}
                                {% if form.username.errors %}
                                <div class="text-danger">
                                    {% for error in form.username.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                {% if form.username.help_text %}
                                <div class="form-text text-muted">{{ form.username.help_text }}</div>
                                {% endif %}
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label fw-bold">{{ form.email.label }}</label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                <div class="text-danger">
                                    {% for error in form.email.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.first_name.id_for_label }}" class="form-label fw-bold">{{ form.first_name.label }}</label>
                                {{ form.first_name }}
                                {% if form.first_name.errors %}
                                <div class="text-danger">
                                    {% for error in form.first_name.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="{{ form.last_name.id_for_label }}" class="form-label fw-bold">{{ form.last_name.label }}</label>
                                {{ form.last_name }}
                                {% if form.last_name.errors %}
                                <div class="text-danger">
                                    {% for error in form.last_name.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.password1.id_for_label }}" class="form-label fw-bold">{{ form.password1.label }} <span class="text-danger">*</span></label>
                                {{ form.password1 }}
                                {% if form.password1.errors %}
                                <div class="text-danger">
                                    {% for error in form.password1.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                {% if form.password1.help_text %}
                                <div class="form-text text-muted small">{{ form.password1.help_text }}</div>
                                {% endif %}
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="{{ form.password2.id_for_label }}" class="form-label fw-bold">{{ form.password2.label }} <span class="text-danger">*</span></label>
                                {{ form.password2 }}
                                {% if form.password2.errors %}
                                <div class="text-danger">
                                    {% for error in form.password2.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.Role.id_for_label }}" class="form-label fw-bold">{{ form.Role.label }} <span class="text-danger">*</span></label>
                                {{ form.Role }}
                                {% if form.Role.errors %}
                                <div class="text-danger">
                                    {% for error in form.Role.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>

                            <div class="col-md-6 mb-3">
                                <div class="form-check mt-4">
                                    {{ form.IsActive }}
                                    <label class="form-check-label fw-bold" for="{{ form.IsActive.id_for_label }}">
                                        {{ form.IsActive.label }}
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            <button type="submit" name="save_and_return" class="btn btn-primary me-md-2">
                                <i class="fas fa-save me-1"></i> حفظ
                            </button>
                            <button type="submit" name="save_and_add_to_group" class="btn btn-success me-md-2">
                                <i class="fas fa-users me-1"></i> حفظ وإضافة إلى مجموعة
                            </button>
                            <button type="submit" name="save_and_edit_permissions" class="btn btn-warning">
                                <i class="fas fa-key me-1"></i> حفظ وتعديل الصلاحيات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card border-0 shadow mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">روابط سريعة</h5>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        <a href="{% url 'administrator:user_list' %}" class="list-group-item list-group-item-action">
                            <i class="fas fa-users me-2"></i> قائمة المستخدمين
                        </a>
                        <a href="{% url 'administrator:group_list' %}" class="list-group-item list-group-item-action">
                            <i class="fas fa-layer-group me-2"></i> إدارة المجموعات
                        </a>
                        <a href="{% url 'administrator:user_list' %}" class="list-group-item list-group-item-action">
                            <i class="fas fa-user-friends me-2"></i> إدارة عضوية المجموعات
                        </a>
                        <a href="{% url 'administrator:permission_dashboard' %}" class="list-group-item list-group-item-action">
                            <i class="fas fa-shield-alt me-2"></i> لوحة الصلاحيات
                        </a>
                    </div>
                </div>
            </div>

            <div class="card border-0 shadow">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">المجموعات المتاحة</h5>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">يمكنك إضافة المستخدم إلى أي من هذه المجموعات بعد إنشاء الحساب:</p>

                    {% if groups %}
                    <div class="list-group">
                        {% for group in groups %}
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <span>{{ group.name }}</span>
                                <span class="badge bg-secondary rounded-pill">{{ group.user_set.count }} مستخدم</span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        لا توجد مجموعات متاحة. <a href="{% url 'administrator:group_add' %}" class="alert-link">إنشاء مجموعة جديدة</a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Password strength indicator
        const password1Input = document.getElementById('{{ form.password1.id_for_label }}');
        const password2Input = document.getElementById('{{ form.password2.id_for_label }}');

        if (password1Input && password2Input) {
            // Check password match
            password2Input.addEventListener('input', function() {
                if (password1Input.value !== password2Input.value) {
                    password2Input.classList.add('is-invalid');
                } else {
                    password2Input.classList.remove('is-invalid');
                    password2Input.classList.add('is-valid');
                }
            });

            // Simple password strength check
            password1Input.addEventListener('input', function() {
                const value = password1Input.value;

                // Remove previous classes
                password1Input.classList.remove('is-valid', 'is-invalid');

                if (value.length < 8) {
                    password1Input.classList.add('is-invalid');
                } else if (value.length >= 8) {
                    password1Input.classList.add('is-valid');
                }
            });
        }
    });
</script>
{% endblock %}

{% block extra_css %}
<style>
    .form-control:focus, .form-select:focus {
        border-color: #3f51b5;
        box-shadow: 0 0 0 0.25rem rgba(63, 81, 181, 0.25);
    }

    .form-check-input:checked {
        background-color: #3f51b5;
        border-color: #3f51b5;
    }

    .list-group-item {
        transition: all 0.2s;
    }

    .list-group-item:hover {
        background-color: #f8f9fa;
    }
</style>
{% endblock %}
