{% extends 'cars\base.html' %}
{% load custom_filters %}

{% block title %}حذف رحلة - نظام إدارة نشاط النقل{% endblock %}

{% block header %}حذف رحلة{% endblock %}

{% block content %}
    <div class="row">
        <div class="col-md-6 mx-auto">
            <div class="card mb-4">
                <div class="card-header bg-danger text-white">
                    <h5 class="card-title mb-0">تأكيد حذف الرحلة</h5>
                </div>
                <div class="card-body">
                    <p class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        هل أنت متأكد من رغبتك في حذف الرحلة التالية؟ هذا الإجراء لا يمكن التراجع عنه.
                    </p>
                    
                    <div class="table-responsive mb-4">
                        <table class="table table-bordered table-hover">
                            <tr>
                                <th class="table-light" style="width: 30%">التاريخ</th>
                                <td>{{ trip.date|date:"Y-m-d" }}</td>
                            </tr>
                            <tr>
                                <th class="table-light">السيارة</th>
                                <td>{{ trip.car.car_code }} - {{ trip.car.car_name }}</td>
                            </tr>
                            <tr>
                                <th class="table-light">نوع السيارة</th>
                                <td>
                                    {% if trip.car.car_type == 'microbus' %}
                                        ميكروباص
                                    {% elif trip.car.car_type == 'bus' %}
                                        أتوبيس
                                    {% elif trip.car.car_type == 'passenger' %}
                                        ركاب
                                    {% elif trip.car.car_type == 'private' %}
                                        ملاكي
                                    {% else %}
                                        {{ trip.car.car_type }}
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th class="table-light">المسافة المقطوعة</th>
                                <td>{{ trip.distance|floatformat:2 }} كم</td>
                            </tr>
                            <tr>
                                <th class="table-light">إجمالي التكلفة</th>
                                <td>{{ trip.final_price|floatformat:2 }} ج.م</td>
                            </tr>
                        </table>
                    </div>
                    
                    <div class="alert alert-info mb-4">
                        <p><strong>ملاحظة:</strong> سيتم تحديث المسافة الكلية المقطوعة للسيارة عند حذف هذه الرحلة.</p>
                        <ul>
                            <li>المسافة الكلية للسيارة حاليًا: {{ trip.car.distance_traveled|floatformat:2 }} كم</li>
                            <li>المسافة المقطوعة في هذه الرحلة: {{ trip.distance|floatformat:2 }} كم</li>
                            <li>المسافة الكلية بعد الحذف: {{ trip.car.distance_traveled|sub:trip.distance|floatformat:2 }} كم</li>
                        </ul>
                    </div>
                    
                    <form method="post">
                        {% csrf_token %}
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'cars:trip_list' %}" class="btn btn-secondary me-md-2">
                                <i class="bi bi-x-circle"></i> إلغاء
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="bi bi-trash"></i> تأكيد الحذف
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
