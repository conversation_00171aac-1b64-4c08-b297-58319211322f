/* Attendance app custom styles */

/* Card styles */
.card {
    margin-bottom: 1.5rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-5px);
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

/* Stats cards */
.icon-circle {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.border-left-danger {
    border-left: 0.25rem solid #e74a3b !important;
}

/* Text colors */
.text-primary {
    color: #4e73df !important;
}

.text-success {
    color: #1cc88a !important;
}

.text-info {
    color: #36b9cc !important;
}

.text-warning {
    color: #f6c23e !important;
}

.text-danger {
    color: #e74a3b !important;
}

/* Badge styles */
.badge {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
    border-radius: 50px;
}

.badge-success {
    background-color: #1cc88a;
}

.badge-warning {
    background-color: #f6c23e;
    color: #fff;
}

.badge-danger {
    background-color: #e74a3b;
}

.badge-info {
    background-color: #36b9cc;
}

/* Table styles */
.table-bordered {
    border: 1px solid #e3e6f0;
}

.table-bordered th,
.table-bordered td {
    border: 1px solid #e3e6f0;
}

.table th {
    background-color: #f8f9fc;
    font-weight: bold;
}

/* Form styles */
.form-control {
    border-radius: 0.35rem;
    border-color: #d1d3e2;
}

.form-control:focus {
    border-color: #bac8f3;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* Today's Attendance Status */
#current-time {
    font-size: 2.5rem;
    font-weight: 600;
    color: var(--bs-primary);
    text-align: center;
}

/* Attendance Form */
.mark-attendance-form {
    max-width: 500px;
    margin: 0 auto;
}

.mark-attendance-form .btn {
    transition: all 0.3s ease;
}

.mark-attendance-form .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* Status badges */
.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-weight: 500;
}

.status-present {
    background-color: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.status-absent {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.status-late {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.status-leave {
    background-color: rgba(23, 162, 184, 0.1);
    color: #17a2b8;
}

/* Attendance table */
.attendance-table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.attendance-table td, 
.attendance-table th {
    vertical-align: middle;
    padding: 1rem;
}

.attendance-table tbody tr {
    transition: background-color 0.2s ease;
}

.attendance-table tbody tr:hover {
    background-color: rgba(0,0,0,0.02);
}

/* Print styles */
@media print {
    .sidebar-nav,
    .card-header button,
    .form-group button,
    .pagination {
        display: none !important;
    }

    .card {
        border: none !important;
        box-shadow: none !important;
    }

    .table {
        width: 100% !important;
    }
}
