# دليل إعداد قاعدة البيانات لنظام الدولية

## مقدمة

هذا الدليل يشرح كيفية إعداد قاعدة بيانات SQL Server لاستخدامها مع نظام الدولية. يمكنك استخدام قاعدة بيانات موجودة أو إنشاء قاعدة بيانات جديدة.

## المتطلبات الأساسية

1. تثبيت SQL Server (2016 أو أحدث)
2. تثبيت برامج تشغيل ODBC لـ SQL Server
3. تمكين بروتوكول TCP/IP في إعدادات SQL Server
4. فتح المنفذ المناسب (عادة 1433) في جدار الحماية

## خطوات إعداد قاعدة البيانات

### الطريقة 1: استخدام أداة إعداد قاعدة البيانات

1. قم بتشغيل ملف `setup_database.bat` الموجود في مجلد النظام
2. سيتم فتح نافذة موجه الأوامر وتشغيل خادم ويب محلي على العنوان `http://localhost:8000`
3. سيتم فتح المتصفح تلقائيًا على صفحة إعداد قاعدة البيانات
4. أدخل معلومات الاتصال بقاعدة البيانات:
   - اسم الخادم: اسم خادم SQL Server أو عنوان IP الخاص به (مثال: `SERVERNAME\SQLEXPRESS` أو `localhost`)
   - اسم قاعدة البيانات: اسم قاعدة البيانات التي تريد استخدامها
   - نوع المصادقة: يمكنك استخدام مصادقة Windows أو مصادقة SQL Server
   - اسم المستخدم وكلمة المرور: في حالة استخدام مصادقة SQL Server
   - المنفذ: المنفذ الذي يستمع عليه SQL Server (عادة 1433)
5. اختر "إنشاء قاعدة البيانات إذا لم تكن موجودة" إذا كنت تريد إنشاء قاعدة بيانات جديدة
6. انقر على "اختبار الاتصال" للتأكد من صحة معلومات الاتصال
7. انقر على "حفظ الإعدادات" لحفظ إعدادات الاتصال بقاعدة البيانات

### الطريقة 2: التعديل اليدوي لملف الإعدادات

1. افتح ملف `ElDawliya_sys/settings.py` في محرر نصوص
2. ابحث عن قسم `DATABASES`
3. قم بتعديل إعدادات الاتصال بقاعدة البيانات كما يلي:

```python
DATABASES = {
    'default': {
        'ENGINE': 'mssql',
        'NAME': 'اسم_قاعدة_البيانات',
        'HOST': 'اسم_الخادم',
        'PORT': '1433',
        # لمصادقة SQL Server
        'USER': 'اسم_المستخدم',
        'PASSWORD': 'كلمة_المرور',
        'OPTIONS': {
            'driver': 'ODBC Driver 17 for SQL Server',
            'Trusted_Connection': 'no',
        },
        # أو لمصادقة Windows
        # 'OPTIONS': {
        #     'driver': 'ODBC Driver 17 for SQL Server',
        #     'Trusted_Connection': 'yes',
        # },
    }
}
```

4. احفظ الملف

## استكشاف الأخطاء وإصلاحها

### مشكلة: لا يمكن الاتصال بالخادم

**الحل:**
- تأكد من أن SQL Server قيد التشغيل
- تأكد من صحة اسم الخادم
- تأكد من تمكين بروتوكول TCP/IP في إعدادات SQL Server
- تأكد من فتح المنفذ المناسب في جدار الحماية

### مشكلة: فشل تسجيل الدخول

**الحل:**
- تأكد من صحة اسم المستخدم وكلمة المرور
- تأكد من أن المستخدم لديه صلاحيات كافية للوصول إلى قاعدة البيانات
- جرب استخدام مصادقة Windows بدلاً من مصادقة SQL Server

### مشكلة: لم يتم العثور على برنامج تشغيل ODBC

**الحل:**
- قم بتثبيت برامج تشغيل ODBC لـ SQL Server من موقع Microsoft
- جرب استخدام برنامج تشغيل آخر (مثل `SQL Server Native Client 11.0` أو `SQL Server`)

### مشكلة: قاعدة البيانات غير موجودة

**الحل:**
- تأكد من صحة اسم قاعدة البيانات
- حدد خيار "إنشاء قاعدة البيانات إذا لم تكن موجودة" لإنشاء قاعدة بيانات جديدة
- قم بإنشاء قاعدة البيانات يدويًا باستخدام SQL Server Management Studio

## الخطوات التالية

بعد إعداد قاعدة البيانات بنجاح، يمكنك:

1. تشغيل النظام باستخدام الأمر `python manage.py runserver`
2. تنفيذ الترحيلات باستخدام الأمر `python manage.py migrate`
3. إنشاء مستخدم مسؤول باستخدام الأمر `python manage.py createsuperuser`

## المساعدة والدعم

إذا واجهت أي مشكلة في إعداد قاعدة البيانات، يرجى التواصل مع فريق الدعم الفني.
