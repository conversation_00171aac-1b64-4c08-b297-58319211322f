{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load json_filters %}

{% block title %}{{ title }} - نظام الدولية{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'Hr/css/leaves.css' %}">
<!-- Chart.js -->
<link rel="stylesheet" href="{% static 'admin/css/vendor/chart.js/Chart.min.css' %}">
{% endblock %}

{% block page_title %}{{ title }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:list' %}">الموظفين</a></li>
<li class="breadcrumb-item active">{{ title }}</li>
{% endblock %}

{% block content %}
<!-- ملخص الإحصائيات -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-0">إجمالي الإجازات</h6>
                        <h2 class="mb-0 mt-2">{{ status_counts.total }}</h2>
                    </div>
                    <i class="fas fa-calendar-alt fa-3x opacity-50"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-0">الإجازات المعتمدة</h6>
                        <h2 class="mb-0 mt-2">{{ status_counts.approved }}</h2>
                        <small>{{ status_percentages.approved|floatformat:1 }}%</small>
                    </div>
                    <i class="fas fa-check-circle fa-3x opacity-50"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-0">الإجازات المعلقة</h6>
                        <h2 class="mb-0 mt-2">{{ status_counts.pending }}</h2>
                        <small>{{ status_percentages.pending|floatformat:1 }}%</small>
                    </div>
                    <i class="fas fa-clock fa-3x opacity-50"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-danger text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-0">الإجازات المرفوضة</h6>
                        <h2 class="mb-0 mt-2">{{ status_counts.rejected }}</h2>
                        <small>{{ status_percentages.rejected|floatformat:1 }}%</small>
                    </div>
                    <i class="fas fa-times-circle fa-3x opacity-50"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- توزيع الإجازات الشهري -->
    <div class="col-md-8 mb-4">
        <div class="card shadow-sm h-100">
            <div class="card-header bg-white">
                <h5 class="mb-0">توزيع الإجازات الشهري</h5>
            </div>
            <div class="card-body">
                <canvas id="monthlyChart"></canvas>
            </div>
        </div>
    </div>

    <!-- توزيع أنواع الإجازات -->
    <div class="col-md-4 mb-4">
        <div class="card shadow-sm h-100">
            <div class="card-header bg-white">
                <h5 class="mb-0">توزيع أنواع الإجازات</h5>
            </div>
            <div class="card-body">
                <canvas id="leaveTypeChart"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- تفاصيل توزيع الإجازات -->
    <div class="col-md-12 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-white">
                <h5 class="mb-0">تفاصيل توزيع الإجازات حسب النوع</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>نوع الإجازة</th>
                                <th>عدد الإجازات</th>
                                <th>مجموع الأيام</th>
                                <th>النسبة المئوية</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for leave in leave_by_type %}
                            <tr>
                                <td>{{ leave.leave_type__name|default:"غير محدد" }}</td>
                                <td>{{ leave.count }}</td>
                                <td>{{ leave.total_days }}</td>
                                <td>{{ leave.count|divisibleby:status_counts.total|floatformat:1 }}%</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Chart.js -->
<script src="{% static 'admin/js/vendor/chart.js/Chart.min.js' %}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // توزيع الإجازات الشهري
        var monthlyData = {
            labels: JSON.parse('{{ leaves_by_month_data.labels|json|escapejs }}'),
            datasets: [{
                label: 'عدد الإجازات',
                data: JSON.parse('{{ leaves_by_month_data.counts|json|escapejs }}'),
                borderColor: 'rgb(75, 192, 192)',
                tension: 0.1,
                fill: false
            }]
        };

        var monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
        new Chart(monthlyCtx, {
            type: 'line',
            data: monthlyData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        // توزيع أنواع الإجازات
        var leaveTypeData = {
            labels: JSON.parse('{{ leave_by_type_data.labels|json|escapejs }}'),
            datasets: [{
                data: JSON.parse('{{ leave_by_type_data.counts|json|escapejs }}'),
                backgroundColor: [
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 206, 86, 0.8)',
                    'rgba(75, 192, 192, 0.8)',
                    'rgba(153, 102, 255, 0.8)',
                    'rgba(201, 203, 207, 0.8)'
                ]
            }]
        };

        var leaveTypeCtx = document.getElementById('leaveTypeChart').getContext('2d');
        new Chart(leaveTypeCtx, {
            type: 'doughnut',
            data: leaveTypeData,
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    });
</script>
{% endblock %}
