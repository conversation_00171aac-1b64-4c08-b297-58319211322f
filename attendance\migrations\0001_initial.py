# Generated by Django 5.0.14 on 2025-05-23 00:55

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('Hr', '0003_alter_employee_emp_image'),
    ]

    operations = [
        migrations.CreateModel(
            name='AttendanceRule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Rule Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('late_grace_period', models.IntegerField(default=15, validators=[django.core.validators.MinValueValidator(0)], verbose_name='Late Grace Period (minutes)')),
                ('early_leave_grace_period', models.IntegerField(default=15, validators=[django.core.validators.MinValueValidator(0)], verbose_name='Early Leave Grace Period (minutes)')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
            ],
            options={
                'verbose_name': 'Attendance Rule',
                'verbose_name_plural': 'Attendance Rules',
            },
        ),
        migrations.CreateModel(
            name='LeaveType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Leave Type')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='Code')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('is_paid', models.BooleanField(default=True, verbose_name='Is Paid Leave')),
                ('max_days_per_year', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(0)], verbose_name='Maximum Days Per Year')),
                ('requires_approval', models.BooleanField(default=True, verbose_name='Requires Approval')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
            ],
            options={
                'verbose_name': 'Leave Type',
                'verbose_name_plural': 'Leave Types',
            },
        ),
        migrations.CreateModel(
            name='EmployeeAttendanceProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('work_hours_per_day', models.DecimalField(decimal_places=2, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(24)], verbose_name='Work Hours Per Day')),
                ('salary_status', models.CharField(choices=[('active', 'Active'), ('suspended', 'Suspended')], default='active', max_length=20, verbose_name='Salary Status')),
                ('attendance_status', models.CharField(choices=[('active', 'Active'), ('suspended', 'Suspended')], default='active', max_length=20, verbose_name='Attendance Status')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('attendance_rule', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='employee_profiles', to='attendance.attendancerule', verbose_name='Attendance Rule')),
                ('employee', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='attendance_profile', to='Hr.employee', verbose_name='Employee')),
            ],
            options={
                'verbose_name': 'Employee Attendance Profile',
                'verbose_name_plural': 'Employee Attendance Profiles',
            },
        ),
        migrations.CreateModel(
            name='AttendanceRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='Date')),
                ('check_in', models.DateTimeField(blank=True, null=True, verbose_name='Check In Time')),
                ('check_out', models.DateTimeField(blank=True, null=True, verbose_name='Check Out Time')),
                ('record_type', models.CharField(choices=[('present', 'Present'), ('absent', 'Absent'), ('leave', 'Leave'), ('holiday', 'Holiday')], default='present', max_length=20, verbose_name='Record Type')),
                ('late_minutes', models.IntegerField(default=0, verbose_name='Late Minutes')),
                ('early_leave_minutes', models.IntegerField(default=0, verbose_name='Early Leave Minutes')),
                ('overtime_minutes', models.IntegerField(default=0, verbose_name='Overtime Minutes')),
                ('break_minutes', models.IntegerField(default=0, verbose_name='Break Minutes')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='new_attendance_records', to='Hr.employee', verbose_name='Employee')),
            ],
            options={
                'verbose_name': 'Attendance Record',
                'verbose_name_plural': 'Attendance Records',
                'unique_together': {('employee', 'date')},
            },
        ),
        migrations.CreateModel(
            name='LeaveBalance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.PositiveIntegerField(verbose_name='Year')),
                ('allocated_days', models.DecimalField(decimal_places=2, max_digits=6, validators=[django.core.validators.MinValueValidator(0)], verbose_name='Allocated Days')),
                ('used_days', models.DecimalField(decimal_places=2, default=0, max_digits=6, validators=[django.core.validators.MinValueValidator(0)], verbose_name='Used Days')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leave_balances', to='Hr.employee', verbose_name='Employee')),
                ('leave_type', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='employee_balances', to='attendance.leavetype', verbose_name='Leave Type')),
            ],
            options={
                'verbose_name': 'Leave Balance',
                'verbose_name_plural': 'Leave Balances',
                'unique_together': {('employee', 'leave_type', 'year')},
            },
        ),
        migrations.CreateModel(
            name='WeeklyHoliday',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('day', models.IntegerField(choices=[(0, 'Monday'), (1, 'Tuesday'), (2, 'Wednesday'), (3, 'Thursday'), (4, 'Friday'), (5, 'Saturday'), (6, 'Sunday')], verbose_name='Holiday Day')),
                ('rule', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='holidays', to='attendance.attendancerule', verbose_name='Attendance Rule')),
            ],
            options={
                'verbose_name': 'Weekly Holiday',
                'verbose_name_plural': 'Weekly Holidays',
                'unique_together': {('rule', 'day')},
            },
        ),
        migrations.CreateModel(
            name='WorkSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('day_of_week', models.IntegerField(choices=[(0, 'Monday'), (1, 'Tuesday'), (2, 'Wednesday'), (3, 'Thursday'), (4, 'Friday'), (5, 'Saturday'), (6, 'Sunday')], verbose_name='Day of Week')),
                ('start_time', models.TimeField(verbose_name='Start Time')),
                ('end_time', models.TimeField(verbose_name='End Time')),
                ('break_start', models.TimeField(blank=True, null=True, verbose_name='Break Start Time')),
                ('break_end', models.TimeField(blank=True, null=True, verbose_name='Break End Time')),
                ('rule', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='schedules', to='attendance.attendancerule', verbose_name='Attendance Rule')),
            ],
            options={
                'verbose_name': 'Work Schedule',
                'verbose_name_plural': 'Work Schedules',
                'unique_together': {('rule', 'day_of_week')},
            },
        ),
    ]
