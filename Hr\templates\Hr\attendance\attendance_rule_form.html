{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ page_title }} - نظام الدولية{% endblock %}

{% block page_title %}{{ page_title }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموظفين</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:attendance:attendance_rule_list' %}">قواعد الحضور</a></li>
<li class="breadcrumb-item active">{{ page_title }}</li>
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">{{ page_title }}</h5>
    </div>
    <div class="card-body">
        <form method="post" novalidate>
            {% csrf_token %}

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.name.id_for_label }}" class="form-label">{{ form.name.label }}</label>
                        {{ form.name }}
                        {% if form.name.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.name.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }}</label>
                        {{ form.description }}
                        {% if form.description.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.description.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.late_grace_minutes.id_for_label }}" class="form-label">{{ form.late_grace_minutes.label }}</label>
                        {{ form.late_grace_minutes }}
                        {% if form.late_grace_minutes.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.late_grace_minutes.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.early_leave_grace_minutes.id_for_label }}" class="form-label">{{ form.early_leave_grace_minutes.label }}</label>
                        {{ form.early_leave_grace_minutes }}
                        {% if form.early_leave_grace_minutes.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.early_leave_grace_minutes.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.is_active }}
                            <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                {{ form.is_active.label }}
                            </label>
                        </div>
                        {% if form.is_active.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.is_active.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0">جدول العمل الأسبوعي</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>اليوم</th>
                                    <th>وقت البداية</th>
                                    <th>وقت النهاية</th>
                                    <th>إجازة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for day, name in days %}
                                <tr>
                                    <td>{{ name }}</td>
                                    <td>
                                        <input type="time" class="form-control" name="start_time_{{ day }}"
                                            value="{% if form.instance.pk %}{{ form.instance.get_work_schedule|get_item:day|get_item:'start_time' }}{% endif %}">
                                    </td>
                                    <td>
                                        <input type="time" class="form-control" name="end_time_{{ day }}"
                                            value="{% if form.instance.pk %}{{ form.instance.get_work_schedule|get_item:day|get_item:'end_time' }}{% endif %}">
                                    </td>
                                    <td>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" name="weekdays" value="{{ day }}"
                                                {% if day in form.weekdays.initial %}checked{% endif %}>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-between">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ
                </button>
                <a href="{% url 'Hr:attendance:attendance_rule_list' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle weekday checkboxes
        const weekdayCheckboxes = document.querySelectorAll('input[name="weekdays"]');
        weekdayCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const row = this.closest('tr');
                const inputs = row.querySelectorAll('input[type="time"]');
                inputs.forEach(input => {
                    input.disabled = this.checked;
                    if (this.checked) {
                        input.value = '';
                    }
                });
            });

            // Initialize state
            if (checkbox.checked) {
                const row = checkbox.closest('tr');
                const inputs = row.querySelectorAll('input[type="time"]');
                inputs.forEach(input => {
                    input.disabled = true;
                    input.value = '';
                });
            }
        });
    });
</script>
{% endblock %}
