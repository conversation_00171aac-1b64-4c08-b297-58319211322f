# دليل ربط الذكاء الاصطناعي مع API نظام الدولية

## مقدمة

تم تكامل الذكاء الاصطناعي (Gemini AI) مع واجهات برمجة التطبيقات (APIs) الداخلية لنظام الدولية. هذا التكامل يسمح للذكاء الاصطناعي بالوصول إلى بيانات النظام الفعلية والرد على الاستفسارات المتعلقة بمختلف أقسام النظام.

## الإعداد الذي تم تنفيذه

1. **تكوين مفتاح API صالح**: تم تحديث ملف `.env` بمفتاح Gemini API صالح.

2. **إعداد مزودي خدمة الذكاء الاصطناعي**: تم تهيئة مزود خدمة Gemini في قاعدة البيانات.

3. **تكوين إعدادات المستخدمين**: تم إنشاء إعدادات Gemini لجميع المستخدمين باستخدام سكربت `setup_ai_configuration.py`.

4. **ربط الذكاء الاصطناعي مع APIs الداخلية**: تم تحسين خدمة `GeminiService` و `DataAnalysisService` للكشف عن استعلامات المستخدم حول بيانات النظام وجلب البيانات ذات الصلة.

## كيفية استخدام الميزة

### استعلامات المخزون

يمكنك طرح أسئلة حول المخزون مثل:

- "ما هي المنتجات منخفضة المخزون؟"
- "عطني تقرير عن الأصناف الناقصة في المستودع"
- "ما هي الأصناف التي وصلت للحد الأدنى؟"
- "كم عدد المنتجات التي تحتاج إلى إعادة طلب؟"

سيقوم الذكاء الاصطناعي بجلب المعلومات من قاعدة البيانات وعرض تقرير مفصل يتضمن:
- قائمة بالأصناف منخفضة المخزون
- الكميات الحالية والحد الأدنى لكل صنف 
- توصيات للإدارة بناءً على البيانات

### استعلامات مستقبلية (قيد التطوير)

يمكن توسيع النظام للإجابة على المزيد من الاستعلامات مثل:

- استعلامات عن الموظفين والأقسام
- معلومات عن المبيعات والإيرادات
- تحليلات المهام والاجتماعات
- بيانات المشتريات والطلبات

## كيف يعمل التكامل تقنيًا

1. **كشف نية المستخدم**: عندما ترسل رسالة، يقوم النظام بتحليل محتوى الرسالة للكشف عن نوع الاستعلام (مثل استعلامات المخزون).

2. **جلب البيانات**: بناءً على نوع الاستعلام، يتم استدعاء خدمة التحليل المناسبة (`DataAnalysisService`) التي تجلب البيانات من قاعدة البيانات.

3. **معالجة البيانات والتحليل**: يتم معالجة البيانات وتحويلها إلى تنسيق مناسب، ثم إرسالها إلى Gemini AI لتحليلها وإنشاء تقرير مفصل.

4. **عرض النتائج**: يتم عرض التحليل والتقرير للمستخدم في واجهة محادثة الذكاء الاصطناعي.

## إضافة المزيد من التكاملات

يمكن توسيع النظام بسهولة لدعم المزيد من أنواع الاستعلامات:

1. قم بإضافة دالة جديدة في `DataAnalysisService` للاستعلام عن النوع الجديد من البيانات.
2. قم بتحديث منطق كشف النية في دالة `chat_with_context` في `GeminiService` للكشف عن نوع الاستعلام الجديد.
3. قم بربط كشف النية بدالة استعلام البيانات المناسبة.

## استكشاف الأخطاء وإصلاحها

إذا واجهت مشاكل في استخدام الميزة:

1. **تأكد من صحة مفتاح API**: تحقق من أن مفتاح Gemini API في ملف `.env` صالح.

2. **إعادة تشغيل الخادم**: بعد تغيير أي إعدادات، قم بإعادة تشغيل الخادم:
   ```
   python manage.py runserver
   ```

3. **إعادة تشغيل إعدادات AI**: إذا لزم الأمر، قم بتشغيل سكربت إعداد AI:
   ```
   python setup_ai_configuration.py
   ```

4. **التحقق من سجلات الأخطاء**: يمكنك الاطلاع على ملفات `api.log` و `debug.log` لفهم أي أخطاء قد تحدث.

## مثال للاستخدام

### سؤال: "ما هي المنتجات منخفضة المخزون في المستودع؟"

**الرد المتوقع**: 

```
# تقرير الأصناف منخفضة المخزون

## الملخص العام
يوجد حاليًا 5 أصناف منخفضة المخزون في المستودع، بإجمالي نقص يبلغ 120 وحدة.

## الأصناف حسب أولوية التوريد:

### أولوية مرتفعة (نقص أكثر من 50%)
1. مسامير حديد 10مم - نقص: 45 وحدة (75%)
2. دهان أبيض - نقص: 30 وحدة (60%) 

### أولوية متوسطة (نقص 20-50%)
3. أسلاك كهرباء - نقص: 25 وحدة (42%)
4. صمامات مياه - نقص: 15 وحدة (30%)

### أولوية منخفضة (نقص أقل من 20%)
5. أنابيب نحاسية - نقص: 5 وحدات (10%)

## توصيات للإدارة
1. إصدار طلبات شراء فورية للأصناف ذات الأولوية المرتفعة
2. جدولة طلبات الأصناف ذات الأولوية المتوسطة خلال الأسبوع القادم
3. مراقبة الأصناف ذات الأولوية المنخفضة دون إجراء فوري
```

## خاتمة

هذا التكامل بين الذكاء الاصطناعي و APIs النظام يمثل خطوة مهمة نحو تحويل نظام الدولية إلى نظام أكثر ذكاءً وفعالية. يمكن للمستخدمين الآن الحصول على تحليلات وتوصيات فورية بناءً على بيانات النظام الفعلية، مما يساعد في اتخاذ قرارات أفضل وأكثر كفاءة.
