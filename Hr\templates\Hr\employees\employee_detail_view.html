{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load image_utils %}

{% block title %}بيانات الموظف تفصيلي - نظام الدولية{% endblock %}

{% block page_title %}بيانات الموظف تفصيلي{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:employees:list' %}">الموظفين</a></li>
<li class="breadcrumb-item active">بيانات الموظف تفصيلي</li>
{% endblock %}

{% block content %}
<!-- Search Box -->
<div class="card shadow-sm mb-4">
    <div class="card-body">
        <form method="get" class="row g-3 align-items-end">
            <div class="col-md-4">
                <label class="form-label">كود الموظف</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-id-badge"></i></span>
                    <input type="text" name="emp_id" class="form-control" placeholder="أدخل كود الموظف..." value="{{ request.GET.emp_id }}">
                </div>
            </div>
            <div class="col-md-4">
                <label class="form-label">الرقم القومي</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-fingerprint"></i></span>
                    <input type="text" name="national_id" class="form-control" placeholder="أدخل الرقم القومي..." value="{{ request.GET.national_id }}">
                </div>
            </div>
            <div class="col-md-4">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search me-2"></i>بحث
                </button>
            </div>
        </form>
    </div>
</div>

{% if employee %}
<!-- Employee Information -->
<div class="row">
    <!-- Basic Info Card -->
    <div class="col-lg-4 mb-4">
        <div class="card shadow-sm h-100">
            <div class="card-body text-center">
                {% if employee.emp_image %}
                <img src="{{ employee.emp_image|binary_to_img }}" alt="{{ employee.emp_full_name }}" class="rounded-circle mb-3" width="120" height="120">
                {% else %}
                <div class="avatar bg-primary text-white rounded-circle mb-3 mx-auto" style="width: 120px; height: 120px; font-size: 3rem; line-height: 120px;">
                    {{ employee.emp_first_name|slice:":1"|upper }}
                </div>
                {% endif %}
                
                <h5 class="mb-1">{{ employee.emp_full_name }}</h5>
                <p class="text-muted mb-3">{{ employee.jop_name }}</p>
                
                <div class="d-flex justify-content-center gap-2 mb-3">
                    <span class="badge {% if employee.working_condition == 'سارى' %}bg-success{% elif employee.working_condition == 'إجازة' %}bg-warning{% else %}bg-danger{% endif %}">
                        {{ employee.working_condition }}
                    </span>
                </div>

                <hr>
                
                <div class="text-start">
                    <div class="mb-3">
                        <label class="text-muted small">رقم الموظف:</label>
                        <p class="mb-0">{{ employee.emp_id }}</p>
                    </div>
                    <div class="mb-3">
                        <label class="text-muted small">القسم:</label>
                        <p class="mb-0">{{ employee.department.dept_name }}</p>
                    </div>
                    <div class="mb-3">
                        <label class="text-muted small">تاريخ التعيين:</label>
                        <p class="mb-0">{{ employee.emp_date_hiring|date:"Y-m-d" }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Info Tabs -->
    <div class="col-lg-8">
        <div class="card shadow-sm">
            <div class="card-header bg-white">
                <ul class="nav nav-tabs card-header-tabs" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" data-bs-toggle="tab" href="#personal" role="tab">
                            <i class="fas fa-user me-1"></i>
                            البيانات الشخصية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#work" role="tab">
                            <i class="fas fa-briefcase me-1"></i>
                            بيانات العمل
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#insurance" role="tab">
                            <i class="fas fa-shield-alt me-1"></i>
                            التأمينات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#documents" role="tab">
                            <i class="fas fa-file-alt me-1"></i>
                            المستندات
                        </a>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content">
                    <!-- Personal Info Tab -->
                    <div class="tab-pane fade show active" id="personal" role="tabpanel">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label text-muted">الاسم الأول</label>
                                <p class="border-bottom pb-2">{{ employee.emp_first_name }}</p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-muted">الاسم الثاني</label>
                                <p class="border-bottom pb-2">{{ employee.emp_second_name }}</p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-muted">اسم الأم</label>
                                <p class="border-bottom pb-2">{{ employee.mother_name }}</p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-muted">الرقم القومي</label>
                                <p class="border-bottom pb-2">{{ employee.national_id }}</p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-muted">الجنسية</label>
                                <p class="border-bottom pb-2">{{ employee.emp_nationality }}</p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-muted">الحالة الاجتماعية</label>
                                <p class="border-bottom pb-2">{{ employee.emp_marital_status }}</p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-muted">رقم الهاتف 1</label>
                                <p class="border-bottom pb-2">{{ employee.emp_phone1 }}</p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-muted">رقم الهاتف 2</label>
                                <p class="border-bottom pb-2">{{ employee.emp_phone2 }}</p>
                            </div>
                            <div class="col-12">
                                <label class="form-label text-muted">العنوان</label>
                                <p class="border-bottom pb-2">{{ employee.emp_address }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Work Info Tab -->
                    <div class="tab-pane fade" id="work" role="tabpanel">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label text-muted">القسم</label>
                                <p class="border-bottom pb-2">{{ employee.department.dept_name }}</p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-muted">الوظيفة</label>
                                <p class="border-bottom pb-2">{{ employee.jop_name }}</p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-muted">نوع الوردية</label>
                                <p class="border-bottom pb-2">{{ employee.shift_type }}</p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-muted">السيارة</label>
                                <p class="border-bottom pb-2">{{ employee.emp_car }}</p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-muted">نقطة التقاط السيارة</label>
                                <p class="border-bottom pb-2">{{ employee.car_pick_up_point }}</p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-muted">وقت ركوب السيارة</label>
                                <p class="border-bottom pb-2">{{ employee.car_ride_time|date:"H:i" }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Insurance Tab -->
                    <div class="tab-pane fade" id="insurance" role="tabpanel">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label text-muted">حالة التأمين</label>
                                <p class="border-bottom pb-2">{{ employee.insurance_status }}</p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-muted">رقم التأمين</label>
                                <p class="border-bottom pb-2">{{ employee.number_insurance }}</p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-muted">تاريخ بداية التأمين</label>
                                <p class="border-bottom pb-2">{{ employee.date_insurance_start|date:"Y-m-d" }}</p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-muted">راتب التأمين</label>
                                <p class="border-bottom pb-2">{{ employee.insurance_salary }}</p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-muted">نسبة التأمين المستحق</label>
                                <p class="border-bottom pb-2">{{ employee.percentage_insurance_payable }}%</p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-muted">مبلغ التأمين المستحق</label>
                                <p class="border-bottom pb-2">{{ employee.due_insurance_amount }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Documents Tab -->
                    <div class="tab-pane fade" id="documents" role="tabpanel">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="d-flex justify-content-between align-items-center border-bottom pb-2 mb-3">
                                    <span>شهادة الميلاد</span>
                                    <i class="fas fa-{% if employee.birth_certificate %}check text-success{% else %}times text-danger{% endif %}"></i>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex justify-content-between align-items-center border-bottom pb-2 mb-3">
                                    <span>مطبوعة التأمين</span>
                                    <i class="fas fa-{% if employee.insurance_printout %}check text-success{% else %}times text-danger{% endif %}"></i>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex justify-content-between align-items-center border-bottom pb-2 mb-3">
                                    <span>صورة البطاقة الشخصية</span>
                                    <i class="fas fa-{% if employee.id_card_photo %}check text-success{% else %}times text-danger{% endif %}"></i>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex justify-content-between align-items-center border-bottom pb-2 mb-3">
                                    <span>صور شخصية</span>
                                    <i class="fas fa-{% if employee.personal_photos %}check text-success{% else %}times text-danger{% endif %}"></i>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex justify-content-between align-items-center border-bottom pb-2 mb-3">
                                    <span>عقد العمل</span>
                                    <i class="fas fa-{% if employee.employment_contract %}check text-success{% else %}times text-danger{% endif %}"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% else %}
<!-- No Employee Selected Message -->
<div class="text-center py-5">
    <i class="fas fa-search fa-3x text-muted mb-3"></i>
    <h5>ابحث عن موظف باستخدام الكود أو الرقم القومي</h5>
    <p class="text-muted">أدخل بيانات البحث في النموذج أعلاه للعثور على الموظف المطلوب</p>
</div>
{% endif %}
{% endblock %}