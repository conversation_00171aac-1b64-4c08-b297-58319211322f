{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load image_utils %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .search-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        color: white;
    }
    
    .employee-card {
        border: 2px solid #e9ecef;
        border-radius: 15px;
        transition: all 0.3s ease;
    }
    
    .employee-card.selected {
        border-color: #007bff;
        box-shadow: 0 0 20px rgba(0, 123, 255, 0.3);
    }
    
    .employee-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid #fff;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .employee-search-results {
        max-height: 400px;
        overflow-y: auto;
    }
    
    .note-form-section {
        background: #f8f9fa;
        border-radius: 15px;
        border: 2px dashed #dee2e6;
        transition: all 0.3s ease;
    }
    
    .note-form-section.active {
        border-color: #007bff;
        background: #fff;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .search-result-item {
        cursor: pointer;
        transition: all 0.3s ease;
        border-radius: 10px;
    }
    
    .search-result-item:hover {
        background-color: #f8f9fa;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .loading-spinner {
        display: none;
    }
    
    .form-floating label {
        color: #6c757d;
    }
    
    .btn-search {
        background: linear-gradient(45deg, #007bff, #0056b3);
        border: none;
        border-radius: 25px;
        padding: 12px 30px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        transition: all 0.3s ease;
    }
    
    .btn-search:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0, 123, 255, 0.4);
    }
</style>
{% endblock %}

{% block page_title %}
<div class="d-flex align-items-center justify-content-between">
    <div>
        <h1 class="h3 mb-1 text-gray-800">
            <i class="fas fa-plus-circle text-primary me-2"></i>
            {{ title }}
        </h1>
        <p class="text-muted mb-0">ابحث عن الموظف وأضف ملاحظة جديدة</p>
    </div>
    <div>
        <a href="{% url 'Hr:notes:dashboard' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للوحة المعلومات
        </a>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <!-- Employee Search Section -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header search-section">
                <h5 class="mb-0 text-white">
                    <i class="fas fa-search me-2"></i>
                    البحث عن الموظف
                </h5>
            </div>
            <div class="card-body">
                <!-- Quick Search -->
                <form id="employeeSearchForm" class="mb-4">
                    <div class="form-floating mb-3">
                        {{ search_form.quick_search }}
                        <label for="{{ search_form.quick_search.id_for_label }}">{{ search_form.quick_search.label }}</label>
                    </div>
                    
                    <!-- Advanced Search (Collapsible) -->
                    <div class="mb-3">
                        <button type="button" class="btn btn-outline-secondary btn-sm" data-bs-toggle="collapse" data-bs-target="#advancedSearch">
                            <i class="fas fa-cog me-1"></i>
                            بحث متقدم
                        </button>
                    </div>
                    
                    <div class="collapse" id="advancedSearch">
                        <div class="row g-3 mb-3">
                            <div class="col-md-6">
                                <div class="form-floating">
                                    {{ search_form.employee_id }}
                                    <label for="{{ search_form.employee_id.id_for_label }}">{{ search_form.employee_id.label }}</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    {{ search_form.national_id }}
                                    <label for="{{ search_form.national_id.id_for_label }}">{{ search_form.national_id.label }}</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    {{ search_form.phone }}
                                    <label for="{{ search_form.phone.id_for_label }}">{{ search_form.phone.label }}</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    {{ search_form.department }}
                                    <label for="{{ search_form.department.id_for_label }}">{{ search_form.department.label }}</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-search text-white w-100">
                        <i class="fas fa-search me-2"></i>
                        بحث عن الموظفين
                        <div class="loading-spinner spinner-border spinner-border-sm ms-2" role="status">
                            <span class="visually-hidden">جاري البحث...</span>
                        </div>
                    </button>
                </form>
                
                <!-- Search Results -->
                <div id="searchResults" class="employee-search-results">
                    {% if selected_employee %}
                    <div class="search-result-item p-3 mb-3 border rounded selected-employee" 
                         data-employee-id="{{ selected_employee.emp_id }}">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                {% if selected_employee.emp_image %}
                                <img src="{{ selected_employee.emp_image|binary_to_img }}" 
                                     alt="{{ selected_employee.emp_full_name }}"
                                     class="employee-avatar" width="60" height="60">
                                {% else %}
                                <div class="employee-avatar bg-primary text-white d-flex align-items-center justify-content-center"
                                     style="width: 60px; height: 60px; font-size: 1.5rem; font-weight: bold;">
                                    {{ selected_employee.emp_first_name|slice:":1"|upper }}
                                </div>
                                {% endif %}
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">{{ selected_employee.emp_full_name|default:selected_employee.emp_first_name }}</h6>
                                <p class="text-muted mb-1">رقم الموظف: {{ selected_employee.emp_id }}</p>
                                <p class="text-muted mb-0">
                                    {% if selected_employee.department %}
                                    القسم: {{ selected_employee.department.dept_name }}
                                    {% endif %}
                                </p>
                            </div>
                            <div class="text-success">
                                <i class="fas fa-check-circle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="text-center py-4 text-muted">
                        <i class="fas fa-search fa-3x mb-3"></i>
                        <h5>ابحث عن الموظف</h5>
                        <p>استخدم نموذج البحث أعلاه للعثور على الموظف المطلوب</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Selected Employee Info & Note Form -->
    <div class="col-lg-6 mb-4">
        <!-- Selected Employee Info -->
        <div id="selectedEmployeeInfo" class="card mb-4" style="{% if not selected_employee %}display: none;{% endif %}">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-user-check me-2"></i>
                    معلومات الموظف المحدد
                </h5>
            </div>
            <div class="card-body" id="employeeInfoContent">
                {% if selected_employee %}
                <div class="d-flex align-items-center mb-3">
                    <div class="me-3">
                        {% if selected_employee.emp_image %}
                        <img src="{{ selected_employee.emp_image|binary_to_img }}" 
                             alt="{{ selected_employee.emp_full_name }}"
                             class="employee-avatar">
                        {% else %}
                        <div class="employee-avatar bg-primary text-white d-flex align-items-center justify-content-center"
                             style="font-size: 2rem; font-weight: bold;">
                            {{ selected_employee.emp_first_name|slice:":1"|upper }}
                        </div>
                        {% endif %}
                    </div>
                    <div>
                        <h4 class="mb-1">{{ selected_employee.emp_full_name|default:selected_employee.emp_first_name }}</h4>
                        <p class="text-muted mb-0">{{ selected_employee.jop_name|default:"غير محدد" }}</p>
                    </div>
                </div>
                
                <div class="row g-3">
                    <div class="col-md-6">
                        <strong>رقم الموظف:</strong><br>
                        <span class="text-muted">{{ selected_employee.emp_id }}</span>
                    </div>
                    <div class="col-md-6">
                        <strong>القسم:</strong><br>
                        <span class="text-muted">{{ selected_employee.department.dept_name|default:"غير محدد" }}</span>
                    </div>
                    <div class="col-md-6">
                        <strong>الهاتف:</strong><br>
                        <span class="text-muted">{{ selected_employee.emp_phone1|default:"غير محدد" }}</span>
                    </div>
                    <div class="col-md-6">
                        <strong>حالة العمل:</strong><br>
                        <span class="badge bg-{% if selected_employee.working_condition == 'سارى' %}success{% else %}warning{% endif %}">
                            {{ selected_employee.working_condition|default:"غير محدد" }}
                        </span>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Note Creation Form -->
        <div class="note-form-section p-4 {% if selected_employee %}active{% endif %}" id="noteFormSection">
            <h5 class="mb-4">
                <i class="fas fa-sticky-note me-2"></i>
                إضافة ملاحظة جديدة
            </h5>
            
            {% if not selected_employee %}
            <div class="text-center py-4 text-muted">
                <i class="fas fa-user-plus fa-3x mb-3"></i>
                <h5>اختر موظفاً أولاً</h5>
                <p>يجب اختيار موظف من نتائج البحث قبل إضافة الملاحظة</p>
            </div>
            {% else %}
            <form method="post" id="noteForm">
                {% csrf_token %}
                <input type="hidden" name="employee_id" value="{{ selected_employee.emp_id }}" id="selectedEmployeeId">
                <input type="hidden" name="create_note" value="1">
                
                <div class="row g-3">
                    <div class="col-12">
                        <div class="form-floating">
                            {{ note_form.title }}
                            <label for="{{ note_form.title.id_for_label }}">{{ note_form.title.label }}</label>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-floating">
                            {{ note_form.note_type }}
                            <label for="{{ note_form.note_type.id_for_label }}">{{ note_form.note_type.label }}</label>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-floating">
                            {{ note_form.priority }}
                            <label for="{{ note_form.priority.id_for_label }}">{{ note_form.priority.label }}</label>
                        </div>
                    </div>
                    
                    <div class="col-12">
                        <div class="form-floating">
                            {{ note_form.content }}
                            <label for="{{ note_form.content.id_for_label }}">{{ note_form.content.label }}</label>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-floating">
                            {{ note_form.evaluation_link }}
                            <label for="{{ note_form.evaluation_link.id_for_label }}">{{ note_form.evaluation_link.label }}</label>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-floating">
                            {{ note_form.evaluation_score }}
                            <label for="{{ note_form.evaluation_score.id_for_label }}">{{ note_form.evaluation_score.label }}</label>
                        </div>
                    </div>
                    
                    <div class="col-12">
                        <div class="form-floating">
                            {{ note_form.tags }}
                            <label for="{{ note_form.tags.id_for_label }}">{{ note_form.tags.label }}</label>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-check form-switch">
                            {{ note_form.is_important }}
                            <label class="form-check-label" for="{{ note_form.is_important.id_for_label }}">
                                {{ note_form.is_important.label }}
                            </label>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-check form-switch">
                            {{ note_form.is_confidential }}
                            <label class="form-check-label" for="{{ note_form.is_confidential.id_for_label }}">
                                {{ note_form.is_confidential.label }}
                            </label>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-check form-switch">
                            {{ note_form.follow_up_required }}
                            <label class="form-check-label" for="{{ note_form.follow_up_required.id_for_label }}">
                                {{ note_form.follow_up_required.label }}
                            </label>
                        </div>
                    </div>
                    
                    <div class="col-md-6" id="followUpDateField" style="{% if not note_form.follow_up_required.value %}display: none;{% endif %}">
                        <div class="form-floating">
                            {{ note_form.follow_up_date }}
                            <label for="{{ note_form.follow_up_date.id_for_label }}">{{ note_form.follow_up_date.label }}</label>
                        </div>
                    </div>
                </div>
                
                <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                    <button type="button" class="btn btn-outline-secondary me-md-2" onclick="resetForm()">
                        <i class="fas fa-undo me-2"></i>
                        إعادة تعيين
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        حفظ الملاحظة
                    </button>
                </div>
            </form>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchForm = document.getElementById('employeeSearchForm');
    const searchResults = document.getElementById('searchResults');
    const loadingSpinner = document.querySelector('.loading-spinner');
    const selectedEmployeeInfo = document.getElementById('selectedEmployeeInfo');
    const noteFormSection = document.getElementById('noteFormSection');
    const followUpRequired = document.getElementById('{{ note_form.follow_up_required.id_for_label }}');
    const followUpDateField = document.getElementById('followUpDateField');
    
    // Handle follow-up date visibility
    if (followUpRequired) {
        followUpRequired.addEventListener('change', function() {
            if (this.checked) {
                followUpDateField.style.display = 'block';
            } else {
                followUpDateField.style.display = 'none';
            }
        });
    }
    
    // Handle employee search
    searchForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        loadingSpinner.style.display = 'inline-block';
        
        fetch('{% url "Hr:notes:employee_search_ajax" %}?' + new URLSearchParams(formData))
            .then(response => response.json())
            .then(data => {
                loadingSpinner.style.display = 'none';
                
                if (data.success) {
                    displaySearchResults(data.employees);
                } else {
                    showAlert('حدث خطأ أثناء البحث', 'error');
                }
            })
            .catch(error => {
                loadingSpinner.style.display = 'none';
                showAlert('حدث خطأ في الاتصال', 'error');
            });
    });
    
    function displaySearchResults(employees) {
        if (employees.length === 0) {
            searchResults.innerHTML = `
                <div class="text-center py-4 text-muted">
                    <i class="fas fa-user-slash fa-3x mb-3"></i>
                    <h5>لم يتم العثور على موظفين</h5>
                    <p>جرب معايير بحث مختلفة</p>
                </div>
            `;
            return;
        }
        
        let html = '';
        employees.forEach(employee => {
            html += `
                <div class="search-result-item p-3 mb-3 border rounded" 
                     data-employee-id="${employee.emp_id}"
                     onclick="selectEmployee(this, ${JSON.stringify(employee).replace(/"/g, '&quot;')})">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <div class="employee-avatar bg-primary text-white d-flex align-items-center justify-content-center"
                                 style="width: 60px; height: 60px; font-size: 1.5rem; font-weight: bold;">
                                ${employee.emp_first_name.charAt(0).toUpperCase()}
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">${employee.emp_full_name || employee.emp_first_name}</h6>
                            <p class="text-muted mb-1">رقم الموظف: ${employee.emp_id}</p>
                            <p class="text-muted mb-0">القسم: ${employee.department_name || 'غير محدد'}</p>
                        </div>
                        <div class="text-primary">
                            <i class="fas fa-user-plus fa-lg"></i>
                        </div>
                    </div>
                </div>
            `;
        });
        
        searchResults.innerHTML = html;
    }
    
    window.selectEmployee = function(element, employee) {
        // Remove previous selection
        document.querySelectorAll('.search-result-item').forEach(item => {
            item.classList.remove('selected-employee');
        });
        
        // Add selection to clicked item
        element.classList.add('selected-employee');
        
        // Update selected employee info
        updateSelectedEmployeeInfo(employee);
        
        // Show note form
        noteFormSection.classList.add('active');
        selectedEmployeeInfo.style.display = 'block';
        
        // Update hidden field
        document.getElementById('selectedEmployeeId').value = employee.emp_id;
    };
    
    function updateSelectedEmployeeInfo(employee) {
        const content = `
            <div class="d-flex align-items-center mb-3">
                <div class="me-3">
                    <div class="employee-avatar bg-primary text-white d-flex align-items-center justify-content-center"
                         style="font-size: 2rem; font-weight: bold;">
                        ${employee.emp_first_name.charAt(0).toUpperCase()}
                    </div>
                </div>
                <div>
                    <h4 class="mb-1">${employee.emp_full_name || employee.emp_first_name}</h4>
                    <p class="text-muted mb-0">غير محدد</p>
                </div>
            </div>
            
            <div class="row g-3">
                <div class="col-md-6">
                    <strong>رقم الموظف:</strong><br>
                    <span class="text-muted">${employee.emp_id}</span>
                </div>
                <div class="col-md-6">
                    <strong>القسم:</strong><br>
                    <span class="text-muted">${employee.department_name || 'غير محدد'}</span>
                </div>
                <div class="col-md-6">
                    <strong>الهاتف:</strong><br>
                    <span class="text-muted">${employee.phone || 'غير محدد'}</span>
                </div>
                <div class="col-md-6">
                    <strong>حالة العمل:</strong><br>
                    <span class="badge bg-${employee.working_condition === 'سارى' ? 'success' : 'warning'}">
                        ${employee.working_condition || 'غير محدد'}
                    </span>
                </div>
            </div>
        `;
        
        document.getElementById('employeeInfoContent').innerHTML = content;
    }
    
    window.resetForm = function() {
        document.getElementById('noteForm').reset();
        followUpDateField.style.display = 'none';
    };
    
    function showAlert(message, type) {
        // Simple alert implementation
        alert(message);
    }
});
</script>
{% endblock %}
