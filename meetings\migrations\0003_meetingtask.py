# Generated by Django 5.0.14 on 2025-05-26 18:31

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('meetings', '0002_alter_meeting_options'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='MeetingTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField(verbose_name='وصف المهمة')),
                ('status', models.CharField(choices=[('pending', 'قيد الانتظار'), ('in_progress', 'قيد التنفيذ'), ('completed', 'مكتملة')], default='pending', max_length=20, verbose_name='الحالة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء المتوقع')),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='meeting_tasks', to=settings.AUTH_USER_MODEL, verbose_name='تم تعيينها لـ')),
                ('meeting', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='meeting_tasks', to='meetings.meeting', verbose_name='الاجتماع')),
            ],
            options={
                'verbose_name': 'مهمة اجتماع',
                'verbose_name_plural': 'مهام الاجتماعات',
            },
        ),
    ]
