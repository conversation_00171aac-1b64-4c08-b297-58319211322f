{% extends 'employee_tasks/base_employee_tasks.html' %}

{% block title %}تعديل خطوة - نظام الدولية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'employee_tasks:dashboard' %}">لوحة التحكم</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'employee_tasks:task_list' %}">المهام</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'employee_tasks:task_detail' task.pk %}">{{ task.title }}</a></li>
                    <li class="breadcrumb-item active" aria-current="page">تعديل خطوة</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">تعديل خطوة للمهمة: {{ task.title }}</h5>
                </div>
                <div class="card-body">
                    <form method="post" action="{% url 'employee_tasks:step_edit' task.pk step.pk %}">
                        {% csrf_token %}
                        
                        <!-- Description -->
                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">وصف الخطوة <span class="text-danger">*</span></label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="text-danger">
                                    {% for error in form.description.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Completed -->
                        <div class="mb-3 form-check">
                            {{ form.completed }}
                            <label class="form-check-label" for="{{ form.completed.id_for_label }}">
                                مكتملة
                            </label>
                            {% if form.completed.errors %}
                                <div class="text-danger">
                                    {% for error in form.completed.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="d-flex justify-content-between mt-4">
                            <a href="{% url 'employee_tasks:task_detail' task.pk %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right me-1"></i> إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
