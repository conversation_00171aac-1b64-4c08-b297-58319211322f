<!-- templates/inventory/supplier_list.html -->
{% extends 'inventory/base_inventory.html' %}
{% load inventory_permission_tags %}

{% block title %}قائمة الموردين - نظام إدارة المخزن{% endblock %}

{% block extra_css %}
<style>
    .search-box {
        position: relative;
        max-width: 300px;
    }
    
    .search-box .form-control {
        padding-right: 38px;
        border-radius: 50px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        transition: all 0.3s;
    }
    
    .search-box .form-control:focus {
        box-shadow: 0 3px 10px rgba(0,0,0,0.15);
    }
    
    .search-icon {
        position: absolute;
        right: 14px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
    }
    
    .supplier-card {
        transition: all 0.3s;
        border-radius: 10px;
        overflow: hidden;
        height: 100%;
    }
    
    .supplier-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    
    .supplier-actions {
        opacity: 0.7;
        transition: all 0.2s;
    }
    
    .supplier-card:hover .supplier-actions {
        opacity: 1;
    }
    
    .empty-state {
        padding: 50px 20px;
        text-align: center;
        background-color: #f8f9fa;
        border-radius: 10px;
    }
    
    .empty-state-icon {
        font-size: 3.5rem;
        color: #adb5bd;
        margin-bottom: 1rem;
    }
    
    .supplier-table th {
        background-color: #f8f9fa;
        font-weight: 600;
    }
    
    .supplier-table td {
        vertical-align: middle;
    }
    
    .btn-icon {
        width: 36px;
        height: 36px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        padding: 0;
    }
    
    .supplier-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 50px;
    }
    
    /* Animations */
    .fade-in-up {
        animation: fadeInUp 0.5s both;
    }
    
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .table-row {
        transition: all 0.2s;
    }
    
    .table-row:hover {
        background-color: rgba(63, 81, 181, 0.05);
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-1">قائمة الموردين</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="{% url 'inventory:dashboard' %}">الرئيسية</a></li>
                <li class="breadcrumb-item active" aria-current="page">الموردين</li>
            </ol>
        </nav>
    </div>
    {% if perms.inventory.add_supplier or user.is_superuser %}
    <a href="{% url 'inventory:supplier_add' %}" class="btn btn-primary">
        <i class="fas fa-plus-circle me-1"></i> إضافة مورد جديد
    </a>
    {% endif %}
</div>

<div class="card shadow-sm mb-4 fade-in-up">
    <!-- Card Header with Search and Filters -->
    <div class="card-header bg-white py-3">
        <div class="row g-3 align-items-center">
            <div class="col-md-6 col-lg-4">
                <div class="search-box">
                    <input type="text" id="supplierSearch" class="form-control" placeholder="بحث عن مورد..." />
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
            <div class="col-md-6 col-lg-8 text-md-end">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary active" id="viewTable">
                        <i class="fas fa-list"></i>
                    </button>
                    <button type="button" class="btn btn-outline-primary" id="viewCards">
                        <i class="fas fa-th-large"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Card Body with Table View -->
    <div class="card-body p-0" id="tableView">
        <div class="table-responsive">
            <table class="table table-hover align-middle mb-0 supplier-table">
                <thead>
                    <tr>
                        <th class="ps-4">كود المورد</th>
                        <th>اسم المورد</th>
                        <th>رقم الهاتف</th>
                        <th>البريد الإلكتروني</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for supplier in suppliers %}
                    <tr class="table-row">
                        <td class="ps-4">
                            <span class="badge bg-light text-dark">{{ supplier.id }}</span>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-inizial rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-2" style="width: 36px; height: 36px;">
                                    {{ supplier.name|slice:":1" }}
                                </div>
                                <div>{{ supplier.name }}</div>
                            </div>
                        </td>
                        <td>
                            {% if supplier.phone %}
                            <a href="tel:{{ supplier.phone }}" class="text-decoration-none">
                                <i class="fas fa-phone-alt text-muted me-1"></i>
                                {{ supplier.phone }}
                            </a>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if supplier.email %}
                            <a href="mailto:{{ supplier.email }}" class="text-decoration-none">
                                <i class="fas fa-envelope text-muted me-1"></i>
                                {{ supplier.email }}
                            </a>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group supplier-actions" role="group">
                                <a href="#" class="btn btn-sm btn-info me-1" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if perms.inventory.change_supplier or user.is_superuser %}
                                <a href="{% url 'inventory:supplier_edit' supplier.id %}" class="btn btn-sm btn-primary me-1" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% endif %}

                                {% if perms.inventory.delete_supplier or user.is_superuser %}
                                <button type="button" class="btn btn-sm btn-danger delete-supplier" 
                                        data-supplier-id="{{ supplier.id }}"
                                        data-supplier-name="{{ supplier.name }}" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5">
                            <div class="empty-state">
                                <i class="fas fa-truck-loading empty-state-icon"></i>
                                <h5>لا يوجد موردين مسجلين</h5>
                                <p class="text-muted mb-3">لم يتم العثور على أي موردين في النظام حالياً</p>
                                {% if perms.inventory.add_supplier or user.is_superuser %}
                                <a href="{% url 'inventory:supplier_add' %}" class="btn btn-primary">
                                    <i class="fas fa-plus-circle me-1"></i> إضافة مورد جديد
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- Card Body with Card View (hidden by default) -->
    <div class="card-body d-none" id="cardView">
        <div class="row g-3">
            {% for supplier in suppliers %}
            <div class="col-md-6 col-lg-4 col-xl-3 supplier-item">
                <div class="card supplier-card h-100">
                    <div class="card-body">
                        <div class="text-center mb-3">
                            <div class="avatar-inizial bg-light rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 64px; height: 64px;">
                                <span class="fs-4 fw-bold text-primary">{{ supplier.name|slice:":1" }}</span>
                            </div>
                            <h5 class="card-title mb-0">{{ supplier.name }}</h5>
                            <div class="text-muted small mb-2">كود: {{ supplier.id }}</div>
                        </div>
                        <hr>
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2">
                                <i class="fas fa-phone-alt text-muted me-2"></i>
                                {% if supplier.phone %}
                                <a href="tel:{{ supplier.phone }}" class="text-decoration-none">{{ supplier.phone }}</a>
                                {% else %}
                                <span class="text-muted">غير متوفر</span>
                                {% endif %}
                            </li>
                            <li>
                                <i class="fas fa-envelope text-muted me-2"></i>
                                {% if supplier.email %}
                                <a href="mailto:{{ supplier.email }}" class="text-decoration-none">{{ supplier.email }}</a>
                                {% else %}
                                <span class="text-muted">غير متوفر</span>
                                {% endif %}
                            </li>
                        </ul>
                    </div>
                    <div class="card-footer bg-white p-3 border-top supplier-actions">
                        <div class="d-flex justify-content-between">
                            <a href="#" class="btn btn-sm btn-info" title="عرض التفاصيل">
                                <i class="fas fa-eye me-1"></i>
                                <span>عرض</span>
                            </a>
                            <div>
                                {% if perms.inventory.change_supplier or user.is_superuser %}
                                <a href="{% url 'inventory:supplier_edit' supplier.id %}" class="btn btn-sm btn-primary me-1" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% endif %}

                                {% if perms.inventory.delete_supplier or user.is_superuser %}
                                <button type="button" class="btn btn-sm btn-danger delete-supplier" 
                                        data-supplier-id="{{ supplier.id }}"
                                        data-supplier-name="{{ supplier.name }}" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-12">
                <div class="empty-state">
                    <i class="fas fa-truck-loading empty-state-icon"></i>
                    <h5>لا يوجد موردين مسجلين</h5>
                    <p class="text-muted mb-3">لم يتم العثور على أي موردين في النظام حالياً</p>
                    {% if perms.inventory.add_supplier or user.is_superuser %}
                    <a href="{% url 'inventory:supplier_add' %}" class="btn btn-primary">
                        <i class="fas fa-plus-circle me-1"></i> إضافة مورد جديد
                    </a>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Card Footer with Pagination (if needed) -->
    {% if suppliers.paginator %}
    <div class="card-footer bg-white border-top py-3">
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center mb-0">
                {% if suppliers.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1" aria-label="First">
                        <span aria-hidden="true">&laquo;&laquo;</span>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ suppliers.previous_page_number }}" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">&laquo;</span>
                </li>
                {% endif %}
                
                {% for i in suppliers.paginator.page_range %}
                    {% if suppliers.number == i %}
                    <li class="page-item active"><span class="page-link">{{ i }}</span></li>
                    {% elif i > suppliers.number|add:'-3' and i < suppliers.number|add:'3' %}
                    <li class="page-item"><a class="page-link" href="?page={{ i }}">{{ i }}</a></li>
                    {% endif %}
                {% endfor %}
                
                {% if suppliers.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ suppliers.next_page_number }}" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ suppliers.paginator.num_pages }}" aria-label="Last">
                        <span aria-hidden="true">&raquo;&raquo;</span>
                    </a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">&raquo;</span>
                </li>
                {% endif %}
            </ul>
        </nav>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle delete supplier buttons
        var deleteButtons = document.querySelectorAll('.delete-supplier');
        
        deleteButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                var supplierId = this.getAttribute('data-supplier-id');
                var supplierName = this.getAttribute('data-supplier-name');
                
                if (confirm('هل أنت متأكد من حذف المورد "' + supplierName + '"؟')) {
                    window.location.href = "{% url 'inventory:supplier_delete' 0 %}".replace('0', supplierId);
                }
            });
        });

        // Toggle view (table/cards)
        var viewTableBtn = document.getElementById('viewTable');
        var viewCardsBtn = document.getElementById('viewCards');
        var tableView = document.getElementById('tableView');
        var cardView = document.getElementById('cardView');

        if (viewTableBtn && viewCardsBtn && tableView && cardView) {
            viewTableBtn.addEventListener('click', function() {
                tableView.classList.remove('d-none');
                cardView.classList.add('d-none');
                viewTableBtn.classList.add('active');
                viewCardsBtn.classList.remove('active');
                localStorage.setItem('supplierViewPreference', 'table');
            });

            viewCardsBtn.addEventListener('click', function() {
                cardView.classList.remove('d-none');
                tableView.classList.add('d-none');
                viewCardsBtn.classList.add('active');
                viewTableBtn.classList.remove('active');
                localStorage.setItem('supplierViewPreference', 'cards');
            });
            
            // Check for saved preference
            var savedPreference = localStorage.getItem('supplierViewPreference');
            if (savedPreference === 'cards') {
                cardView.classList.remove('d-none');
                tableView.classList.add('d-none');
                viewCardsBtn.classList.add('active');
                viewTableBtn.classList.remove('active');
            }
        }
        
        // Search functionality
        var searchInput = document.getElementById('supplierSearch');
        var tableRows = document.querySelectorAll('.table-row');
        var cardItems = document.querySelectorAll('.supplier-item');
        
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                var searchText = this.value.toLowerCase();
                
                // Filter table rows
                tableRows.forEach(function(row) {
                    var nameCell = row.querySelector('td:nth-child(2)');
                    var phoneCell = row.querySelector('td:nth-child(3)');
                    var emailCell = row.querySelector('td:nth-child(4)');
                    
                    var name = nameCell ? nameCell.textContent.toLowerCase() : '';
                    var phone = phoneCell ? phoneCell.textContent.toLowerCase() : '';
                    var email = emailCell ? emailCell.textContent.toLowerCase() : '';
                    
                    var matches = name.includes(searchText) || 
                                 phone.includes(searchText) || 
                                 email.includes(searchText);
                    
                    row.style.display = matches ? '' : 'none';
                });
                
                // Filter card items
                cardItems.forEach(function(card) {
                    var nameElement = card.querySelector('.card-title');
                    var phoneElement = card.querySelector('li:nth-child(1)');
                    var emailElement = card.querySelector('li:nth-child(2)');
                    
                    var name = nameElement ? nameElement.textContent.toLowerCase() : '';
                    var phone = phoneElement ? phoneElement.textContent.toLowerCase() : '';
                    var email = emailElement ? emailElement.textContent.toLowerCase() : '';
                    
                    var matches = name.includes(searchText) || 
                                 phone.includes(searchText) || 
                                 email.includes(searchText);
                    
                    card.style.display = matches ? '' : 'none';
                });
            });
        }
    });
</script>
{% endblock %}
