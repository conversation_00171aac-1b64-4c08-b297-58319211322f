{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ page_title }} - نظام الدولية{% endblock %}

{% block page_title %}{{ page_title }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموظفين</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:attendance:attendance_machine_list' %}">أجهزة البصمة</a></li>
<li class="breadcrumb-item active">{{ page_title }}</li>
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">{{ page_title }}</h5>
    </div>
    <div class="card-body">
        <form method="post" novalidate>
            {% csrf_token %}

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.name.id_for_label }}" class="form-label">{{ form.name.label }}</label>
                        {{ form.name }}
                        {% if form.name.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.name.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.ip_address.id_for_label }}" class="form-label">{{ form.ip_address.label }}</label>
                        {{ form.ip_address }}
                        {% if form.ip_address.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.ip_address.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.port.id_for_label }}" class="form-label">{{ form.port.label }}</label>
                        {{ form.port }}
                        {% if form.port.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.port.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.machine_type.id_for_label }}" class="form-label">{{ form.machine_type.label }}</label>
                        {{ form.machine_type }}
                        {% if form.machine_type.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.machine_type.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.location.id_for_label }}" class="form-label">{{ form.location.label }}</label>
                        {{ form.location }}
                        {% if form.location.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.location.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.is_active }}
                            <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                {{ form.is_active.label }}
                            </label>
                        </div>
                        {% if form.is_active.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.is_active.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-between">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ
                </button>
                <a href="{% url 'Hr:attendance:attendance_machine_list' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}