{% extends 'administrator/base_admin.html' %}
{% load static %}

{% block title %}إدارة مجموعات المستخدم - لوحة تحكم مدير النظام{% endblock %}

{% block page_icon %}users-cog{% endblock %}
{% block page_header %}إدارة المجموعات للمستخدم {{ user_obj.username }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" />
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" />
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-users-cog me-2"></i>
                    إدارة مجموعات المستخدم
                </h5>
                <a href="{% url 'administrator:user_list' %}" class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة إلى قائمة المستخدمين
                </a>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card shadow-sm">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">معلومات المستخدم</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>اسم المستخدم:</strong> {{ object.username }}</p>
                                <p><strong>الاسم الكامل:</strong> 
                                    {% if object.first_name or object.last_name %}
                                    {{ object.first_name }} {{ object.last_name }}
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </p>
                                <p><strong>البريد الإلكتروني:</strong> 
                                    {% if object.email %}
                                    {{ object.email }}
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </p>
                                <p><strong>حالة النشاط:</strong> 
                                    {% if object.is_active %}
                                    <span class="badge bg-success">نشط</span>
                                    {% else %}
                                    <span class="badge bg-danger">غير نشط</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="form-group">
                                <label class="form-label fw-bold">المجموعات المتاحة:</label>
                                <select name="groups" multiple class="form-select" id="id_groups" size="10">
                                    {% for group in groups %}
                                    <option value="{{ group.id }}" {% if group in user_groups %}selected{% endif %}>
                                        {{ group.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                                <div class="form-text text-muted">اختر المجموعات التي سينضم إليها المستخدم. المستخدم سيحصل على جميع صلاحيات المجموعات التي ينضم إليها.</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        يمكنك أيضًا <a href="{% url 'administrator:user_permissions' object.id %}">إدارة الصلاحيات المباشرة</a> للمستخدم بدون الحاجة لإضافته إلى مجموعة.
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                        <a href="{% url 'administrator:user_list' %}" class="btn btn-outline-secondary">
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
    $(document).ready(function() {
        // Initialize Select2 for multiple select
        $('#id_groups').select2({
            theme: 'bootstrap-5',
            width: '100%',
            placeholder: 'اختر المجموعات',
            allowClear: true
        });
    });
</script>
{% endblock %}
