# Generated by Django 5.0.14 on 2025-05-24 23:01

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='APIKey',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.Char<PERSON>ield(help_text='اسم مفتاح API', max_length=100)),
                ('key', models.Char<PERSON>ield(help_text='مفتاح API', max_length=64, unique=True)),
                ('is_active', models.BooleanField(default=True, help_text='هل المفتاح نشط؟')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('last_used', models.DateTimeField(blank=True, null=True)),
                ('expires_at', models.DateTimeField(blank=True, help_text='تاريخ انتهاء الصلاحية', null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='api_keys', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'مفتاح API',
                'verbose_name_plural': 'مفاتيح API',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='APIUsageLog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('endpoint', models.CharField(help_text='نقطة النهاية المستخدمة', max_length=200)),
                ('method', models.CharField(help_text='طريقة HTTP', max_length=10)),
                ('status_code', models.IntegerField(help_text='رمز الاستجابة')),
                ('response_time', models.FloatField(help_text='وقت الاستجابة بالثواني')),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('api_key', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='api.apikey')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'سجل استخدام API',
                'verbose_name_plural': 'سجلات استخدام API',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='GeminiConversation',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(help_text='عنوان المحادثة', max_length=200)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='gemini_conversations', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'محادثة Gemini',
                'verbose_name_plural': 'محادثات Gemini',
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='GeminiMessage',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('role', models.CharField(choices=[('user', 'المستخدم'), ('assistant', 'المساعد'), ('system', 'النظام')], max_length=10)),
                ('content', models.TextField(help_text='محتوى الرسالة')),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('tokens_used', models.IntegerField(default=0, help_text='عدد الرموز المستخدمة')),
                ('conversation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='api.geminiconversation')),
            ],
            options={
                'verbose_name': 'رسالة Gemini',
                'verbose_name_plural': 'رسائل Gemini',
                'ordering': ['timestamp'],
            },
        ),
    ]
