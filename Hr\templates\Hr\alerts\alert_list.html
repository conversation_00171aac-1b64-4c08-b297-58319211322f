{% extends 'Hr/base_hr.html' %}
{% load static %}

{% block title %}التنبيهات - الموارد البشرية{% endblock %}

{% block page_title %}التنبيهات{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموارد البشرية</a></li>
<li class="breadcrumb-item active">التنبيهات</li>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <!-- Alert Settings Card -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light py-3 d-flex justify-content-between align-items-center">
                <h5 class="mb-0 fw-semibold">
                    <i class="fas fa-cog text-primary me-2"></i>إعدادات التنبيهات
                </h5>
                <button class="btn btn-sm btn-primary" data-bs-toggle="collapse" data-bs-target="#settingsCollapse" aria-expanded="false" aria-controls="settingsCollapse">
                    <i class="fas fa-sliders-h me-1"></i> تعديل الإعدادات
                </button>
            </div>
            <div class="collapse" id="settingsCollapse">
                <div class="card-body p-4">
                    <form method="post" action="{% url 'Hr:alerts:settings' %}" class="row g-3">
                        {% csrf_token %}
                        <div class="col-md-6">
                            <label class="form-label">تنبيه انتهاء العقود قبل (بالأيام)</label>
                            <input type="number" class="form-control" name="contract_alert_days" value="{{ settings.contract_alert_days }}" min="1" max="90">
                            <div class="form-text">سيتم تنبيهك بالعقود التي تنتهي خلال هذه المدة</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">تنبيه انتهاء البطاقات الصحية قبل (بالأيام)</label>
                            <input type="number" class="form-control" name="health_card_alert_days" value="{{ settings.health_card_alert_days }}" min="1" max="90">
                            <div class="form-text">سيتم تنبيهك بالبطاقات الصحية التي تنتهي خلال هذه المدة</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">تنبيه المهام المتأخرة بعد (بالأيام)</label>
                            <input type="number" class="form-control" name="task_overdue_days" value="{{ settings.task_overdue_days }}" min="0" max="30">
                            <div class="form-text">سيتم تنبيهك بالمهام التي تأخرت لهذه المدة بعد تاريخ الاستحقاق</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">تنبيه بدل مواصلات قبل (بالأيام)</label>
                            <input type="number" class="form-control" name="transportation_allowance_days" value="{{ settings.transportation_allowance_days }}" min="1" max="90">
                            <div class="form-text">سيتم تنبيهك بموعد تجديد بدل المواصلات قبل هذه المدة</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">مستوى أهمية التنبيهات</label>
                            <select class="form-select" name="alert_importance_level">
                                <option value="high" {% if settings.alert_importance_level == 'high' %}selected{% endif %}>عالي</option>
                                <option value="medium" {% if settings.alert_importance_level == 'medium' %}selected{% endif %}>متوسط</option>
                                <option value="low" {% if settings.alert_importance_level == 'low' %}selected{% endif %}>منخفض</option>
                            </select>
                            <div class="form-text">تحديد مستوى أهمية التنبيهات المعروضة</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">إرسال إشعارات البريد الإلكتروني</label>
                            <div class="form-check form-switch mt-2">
                                <input class="form-check-input" type="checkbox" name="email_notifications" id="emailNotifications" {% if settings.email_notifications %}checked{% endif %}>
                                <label class="form-check-label" for="emailNotifications">تفعيل إشعارات البريد الإلكتروني</label>
                            </div>
                            <div class="form-text">سيتم إرسال إشعارات بالتنبيهات الهامة إلى بريدك الإلكتروني</div>
                        </div>
                        <div class="col-12 mt-4 text-center">
                            <button type="submit" class="btn btn-primary px-4">
                                <i class="fas fa-save me-1"></i> حفظ الإعدادات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Alerts Summary Card -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light py-3">
                <h5 class="mb-0 fw-semibold">
                    <i class="fas fa-bell text-primary me-2"></i>ملخص التنبيهات
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="row g-0">
                    <div class="col-md-3 col-6 border-end border-bottom p-4 text-center">
                        <div class="alert-summary-item">
                            <div class="alert-icon d-inline-block rounded-circle bg-danger-subtle text-danger mb-2">
                                <i class="fas fa-file-contract"></i>
                            </div>
                            <h3 class="mb-1">{{ contract_alerts.count }}</h3>
                            <p class="mb-0 text-muted">عقود قاربت على الانتهاء</p>
                        </div>
                    </div>
                    <div class="col-md-3 col-6 border-bottom p-4 text-center">
                        <div class="alert-summary-item">
                            <div class="alert-icon d-inline-block rounded-circle bg-warning-subtle text-warning mb-2">
                                <i class="fas fa-id-card"></i>
                            </div>
                            <h3 class="mb-1">{{ health_card_alerts.count }}</h3>
                            <p class="mb-0 text-muted">بطاقات صحية منتهية</p>
                        </div>
                    </div>
                    <div class="col-md-3 col-6 border-end border-bottom border-top-md-0 p-4 text-center">
                        <div class="alert-summary-item">
                            <div class="alert-icon d-inline-block rounded-circle bg-info-subtle text-info mb-2">
                                <i class="fas fa-tasks"></i>
                            </div>
                            <h3 class="mb-1">{{ task_alerts.count }}</h3>
                            <p class="mb-0 text-muted">مهام متأخرة</p>
                        </div>
                    </div>
                    <div class="col-md-3 col-6 border-bottom border-top-md-0 p-4 text-center">
                        <div class="alert-summary-item">
                            <div class="alert-icon d-inline-block rounded-circle bg-success-subtle text-success mb-2">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <h3 class="mb-1">{{ insurance_alerts.count }}</h3>
                            <p class="mb-0 text-muted">تأمينات بحاجة للمتابعة</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Alerts Tabs -->
        <div class="card shadow-sm">
            <div class="card-header bg-light p-0 border-bottom">
                <ul class="nav nav-tabs card-header-tabs" id="alertsTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="contracts-tab" data-bs-toggle="tab" data-bs-target="#contracts" type="button" role="tab" aria-controls="contracts" aria-selected="true">
                            <i class="fas fa-file-contract me-1"></i>
                            العقود
                            {% if contract_alerts.count > 0 %}
                            <span class="badge rounded-pill bg-danger ms-1">{{ contract_alerts.count }}</span>
                            {% endif %}
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="health-tab" data-bs-toggle="tab" data-bs-target="#health" type="button" role="tab" aria-controls="health" aria-selected="false">
                            <i class="fas fa-id-card me-1"></i>
                            البطاقات الصحية
                            {% if health_card_alerts.count > 0 %}
                            <span class="badge rounded-pill bg-warning text-dark ms-1">{{ health_card_alerts.count }}</span>
                            {% endif %}
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="tasks-tab" data-bs-toggle="tab" data-bs-target="#tasks" type="button" role="tab" aria-controls="tasks" aria-selected="false">
                            <i class="fas fa-tasks me-1"></i>
                            المهام
                            {% if task_alerts.count > 0 %}
                            <span class="badge rounded-pill bg-info ms-1">{{ task_alerts.count }}</span>
                            {% endif %}
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="insurance-tab" data-bs-toggle="tab" data-bs-target="#insurance" type="button" role="tab" aria-controls="insurance" aria-selected="false">
                            <i class="fas fa-shield-alt me-1"></i>
                            التأمينات
                            {% if insurance_alerts.count > 0 %}
                            <span class="badge rounded-pill bg-success ms-1">{{ insurance_alerts.count }}</span>
                            {% endif %}
                        </button>
                    </li>
                </ul>
            </div>
            <div class="card-body p-0">
                <div class="tab-content" id="alertsTabContent">
                    <!-- Contracts Tab -->
                    <div class="tab-pane fade show active" id="contracts" role="tabpanel" aria-labelledby="contracts-tab">
                        {% if contract_alerts %}
                            <div class="table-responsive">
                                <table class="table table-hover align-middle mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th class="py-3 px-4">الموظف</th>
                                            <th class="py-3 px-4">القسم</th>
                                            <th class="py-3 px-4">تاريخ انتهاء العقد</th>
                                            <th class="py-3 px-4">الأيام المتبقية</th>
                                            <th class="py-3 px-4 text-center">الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for alert in contract_alerts %}
                                        <tr>
                                            <td class="px-4">
                                                <div class="d-flex align-items-center">
                                                    {% if alert.employee.emp_image %}
                                                    <img src="{{ alert.employee.emp_image|binary_to_img }}" alt="{{ alert.employee.emp_full_name }}" class="rounded-circle me-2 object-fit-cover" width="40" height="40">
                                                    {% else %}
                                                    <div class="avatar bg-secondary text-white me-2 flex-shrink-0">
                                                        {{ alert.employee.emp_first_name|slice:":1"|upper }}
                                                    </div>
                                                    {% endif %}
                                                    <div>
                                                        <a href="{% url 'Hr:employees:detail' alert.employee.emp_id %}" class="fw-medium d-block text-decoration-none text-dark">{{ alert.employee.emp_full_name }}</a>
                                                        <small class="text-muted">{{ alert.employee.emp_id }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="px-4">{{ alert.employee.department.dept_name|default:"-" }}</td>
                                            <td class="px-4">{{ alert.employee.contract_expiry_date|date:"Y-m-d" }}</td>
                                            <td class="px-4">
                                                <span class="badge rounded-pill px-3 py-2 {% if alert.days_remaining <= 7 %}bg-danger{% elif alert.days_remaining <= 14 %}bg-warning{% else %}bg-info{% endif %}">
                                                    {{ alert.days_remaining }} أيام
                                                </span>
                                            </td>
                                            <td class="px-4 text-center">
                                                <div class="btn-group btn-group-sm">
                                                    <a href="{% url 'Hr:employees:detail' alert.employee.emp_id %}" class="btn btn-outline-primary" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{% url 'Hr:employees:edit' alert.employee.emp_id %}" class="btn btn-outline-secondary" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="{% url 'Hr:renew_contract' alert.employee.emp_id %}" class="btn btn-outline-success" title="تجديد العقد">
                                                        <i class="fas fa-sync-alt"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-check-circle fa-3x mb-3 text-success"></i>
                                <p class="mb-0">لا توجد عقود قاربت على الانتهاء حالياً.</p>
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- Health Cards Tab -->
                    <div class="tab-pane fade" id="health" role="tabpanel" aria-labelledby="health-tab">
                        {% if health_card_alerts %}
                            <div class="table-responsive">
                                <table class="table table-hover align-middle mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th class="py-3 px-4">الموظف</th>
                                            <th class="py-3 px-4">رقم البطاقة</th>
                                            <th class="py-3 px-4">تاريخ الانتهاء</th>
                                            <th class="py-3 px-4">الأيام المتبقية</th>
                                            <th class="py-3 px-4 text-center">الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for alert in health_card_alerts %}
                                        <tr>
                                            <td class="px-4">
                                                <div class="d-flex align-items-center">
                                                    {% if alert.employee.emp_image %}
                                                    <img src="{{ alert.employee.emp_image|binary_to_img }}" alt="{{ alert.employee.emp_full_name }}" class="rounded-circle me-2 object-fit-cover" width="40" height="40">
                                                    {% else %}
                                                    <div class="avatar bg-secondary text-white me-2 flex-shrink-0">
                                                        {{ alert.employee.emp_first_name|slice:":1"|upper }}
                                                    </div>
                                                    {% endif %}
                                                    <div>
                                                        <a href="{% url 'Hr:employees:detail' alert.employee.emp_id %}" class="fw-medium d-block text-decoration-none text-dark">{{ alert.employee.emp_full_name }}</a>
                                                        <small class="text-muted">{{ alert.employee.emp_id }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="px-4">{{ alert.employee.health_card_number|default:"-" }}</td>
                                            <td class="px-4">{{ alert.employee.health_card_expiration_date|date:"Y-m-d" }}</td>
                                            <td class="px-4">
                                                <span class="badge rounded-pill px-3 py-2 {% if alert.days_remaining <= 7 %}bg-danger{% elif alert.days_remaining <= 14 %}bg-warning{% else %}bg-info{% endif %}">
                                                    {{ alert.days_remaining }} أيام
                                                </span>
                                            </td>
                                            <td class="px-4 text-center">
                                                <div class="btn-group btn-group-sm">
                                                    <a href="{% url 'Hr:employees:detail' alert.employee.emp_id %}" class="btn btn-outline-primary" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{% url 'Hr:employees:edit' alert.employee.emp_id %}" class="btn btn-outline-secondary" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="{% url 'Hr:renew_health_card' alert.employee.emp_id %}" class="btn btn-outline-success" title="تجديد البطاقة">
                                                        <i class="fas fa-sync-alt"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-check-circle fa-3x mb-3 text-success"></i>
                                <p class="mb-0">لا توجد بطاقات صحية منتهية حالياً.</p>
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- Tasks Tab -->
                    <div class="tab-pane fade" id="tasks" role="tabpanel" aria-labelledby="tasks-tab">
                        {% if task_alerts %}
                            <div class="table-responsive">
                                <table class="table table-hover align-middle mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th class="py-3 px-4">عنوان المهمة</th>
                                            <th class="py-3 px-4">المسؤول</th>
                                            <th class="py-3 px-4">تاريخ الاستحقاق</th>
                                            <th class="py-3 px-4">الحالة</th>
                                            <th class="py-3 px-4">الأولوية</th>
                                            <th class="py-3 px-4 text-center">الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for alert in task_alerts %}
                                        <tr>
                                            <td class="px-4">
                                                <a href="{% url 'Hr:hr_tasks:detail' alert.task.id %}" class="fw-medium d-block text-decoration-none text-dark">{{ alert.task.title }}</a>
                                                <small class="text-muted">{{ alert.task.description|truncatechars:50 }}</small>
                                            </td>
                                            <td class="px-4">
                                                {% if alert.task.assigned_to %}
                                                {{ alert.task.assigned_to.get_full_name }}
                                                {% else %}
                                                <span class="text-muted">-</span>
                                                {% endif %}
                                            </td>
                                            <td class="px-4">{{ alert.task.due_date|date:"Y-m-d" }}</td>
                                            <td class="px-4">
                                                <span class="badge rounded-pill px-3 py-2 
                                                    {% if alert.task.status == 'pending' %}bg-secondary
                                                    {% elif alert.task.status == 'in_progress' %}bg-primary
                                                    {% elif alert.task.status == 'completed' %}bg-success
                                                    {% elif alert.task.status == 'cancelled' %}bg-danger
                                                    {% endif %}">
                                                    {{ alert.task.get_status_display }}
                                                </span>
                                            </td>
                                            <td class="px-4">
                                                <span class="badge rounded-pill px-3 py-2 
                                                    {% if alert.task.priority == 'low' %}bg-success
                                                    {% elif alert.task.priority == 'medium' %}bg-warning
                                                    {% elif alert.task.priority == 'high' %}bg-danger
                                                    {% elif alert.task.priority == 'urgent' %}bg-danger text-white
                                                    {% endif %}">
                                                    {{ alert.task.get_priority_display }}
                                                </span>
                                            </td>
                                            <td class="px-4 text-center">
                                                <div class="btn-group btn-group-sm">
                                                    <a href="{% url 'Hr:hr_tasks:detail' alert.task.id %}" class="btn btn-outline-primary" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{% url 'Hr:hr_tasks:edit' alert.task.id %}" class="btn btn-outline-secondary" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    {% if alert.task.status != 'completed' %}
                                                    <a href="{% url 'Hr:hr_tasks:complete' alert.task.id %}" class="btn btn-outline-success" title="إكمال المهمة">
                                                        <i class="fas fa-check"></i>
                                                    </a>
                                                    {% endif %}
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-check-circle fa-3x mb-3 text-success"></i>
                                <p class="mb-0">لا توجد مهام متأخرة حالياً.</p>
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- Insurance Tab -->
                    <div class="tab-pane fade" id="insurance" role="tabpanel" aria-labelledby="insurance-tab">
                        {% if insurance_alerts %}
                            <div class="table-responsive">
                                <table class="table table-hover align-middle mb-0">
{% extends 'Hr/base_hr.html' %}
{% load static %}

{% block title %}التنبيهات - الموارد البشرية{% endblock %}

{% block page_title %}التنبيهات{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموارد البشرية</a></li>
<li class="breadcrumb-item active">التنبيهات</li>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <!-- Alert Settings Card -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light py-3 d-flex justify-content-between align-items-center">
                <h5 class="mb-0 fw-semibold">
                    <i class="fas fa-cog text-primary me-2"></i>إعدادات التنبيهات
                </h5>
                <button class="btn btn-sm btn-primary" data-bs-toggle="collapse" data-bs-target="#settingsCollapse" aria-expanded="false" aria-controls="settingsCollapse">
                    <i class="fas fa-sliders-h me-1"></i> تعديل الإعدادات
                </button>
            </div>
            <div class="collapse" id="settingsCollapse">
                <div class="card-body p-4">
                    <form method="post" action="{% url 'Hr:alerts:settings' %}" class="row g-3">
                        {% csrf_token %}
                        <div class="col-md-6">
                            <label class="form-label">تنبيه انتهاء العقود قبل (بالأيام)</label>
                            <input type="number" class="form-control" name="contract_alert_days" value="{{ settings.contract_alert_days }}" min="1" max="90">
                            <div class="form-text">سيتم تنبيهك بالعقود التي تنتهي خلال هذه المدة</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">تنبيه انتهاء البطاقات الصحية قبل (بالأيام)</label>
                            <input type="number" class="form-control" name="health_card_alert_days" value="{{ settings.health_card_alert_days }}" min="1" max="90">
                            <div class="form-text">سيتم تنبيهك بالبطاقات الصحية التي تنتهي خلال هذه المدة</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">تنبيه المهام المتأخرة بعد (بالأيام)</label>
                            <input type="number" class="form-control" name="task_overdue_days" value="{{ settings.task_overdue_days }}" min="0" max="30">
                            <div class="form-text">سيتم تنبيهك بالمهام التي تأخرت لهذه المدة بعد تاريخ الاستحقاق</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">تنبيه بدل مواصلات قبل (بالأيام)</label>
                            <input type="number" class="form-control" name="transportation_allowance_days" value="{{ settings.transportation_allowance_days }}" min="1" max="90">
                            <div class="form-text">سيتم تنبيهك بموعد تجديد بدل المواصلات قبل هذه المدة</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">مستوى أهمية التنبيهات</label>
                            <select class="form-select" name="alert_importance_level">
                                <option value="high" {% if settings.alert_importance_level == 'high' %}selected{% endif %}>عالي</option>
                                <option value="medium" {% if settings.alert_importance_level == 'medium' %}selected{% endif %}>متوسط</option>
                                <option value="low" {% if settings.alert_importance_level == 'low' %}selected{% endif %}>منخفض</option>
                            </select>
                            <div class="form-text">تحديد مستوى أهمية التنبيهات المعروضة</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">إرسال إشعارات البريد الإلكتروني</label>
                            <div class="form-check form-switch mt-2">
                                <input class="form-check-input" type="checkbox" name="email_notifications" id="emailNotifications" {% if settings.email_notifications %}checked{% endif %}>
                                <label class="form-check-label" for="emailNotifications">تفعيل إشعارات البريد الإلكتروني</label>
                            </div>
                            <div class="form-text">سيتم إرسال إشعارات بالتنبيهات الهامة إلى بريدك الإلكتروني</div>
                        </div>
                        <div class="col-12 mt-4 text-center">
                            <button type="submit" class="btn btn-primary px-4">
                                <i class="fas fa-save me-1"></i> حفظ الإعدادات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Alerts Summary Card -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light py-3">
                <h5 class="mb-0 fw-semibold">
                    <i class="fas fa-bell text-primary me-2"></i>ملخص التنبيهات
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="row g-0">
                    <div class="col-md-3 col-6 border-end border-bottom p-4 text-center">
                        <div class="alert-summary-item">
                            <div class="alert-icon d-inline-block rounded-circle bg-danger-subtle text-danger mb-2">
                                <i class="fas fa-file-contract"></i>
                            </div>
                            <h3 class="mb-1">{{ contract_alerts.count }}</h3>
                            <p class="mb-0 text-muted">عقود قاربت على الانتهاء</p>
                        </div>
                    </div>
                    <div class="col-md-3 col-6 border-bottom p-4 text-center">
                        <div class="alert-summary-item">
                            <div class="alert-icon d-inline-block rounded-circle bg-warning-subtle text-warning mb-2">
                                <i class="fas fa-id-card"></i>
                            </div>
                            <h3 class="mb-1">{{ health_card_alerts.count }}</h3>
                            <p class="mb-0 text-muted">بطاقات صحية منتهية</p>
                        </div>
                    </div>
                    <div class="col-md-3 col-6 border-end border-bottom border-top-md-0 p-4 text-center">
                        <div class="alert-summary-item">
                            <div class="alert-icon d-inline-block rounded-circle bg-info-subtle text-info mb-2">
                                <i class="fas fa-tasks"></i>
                            </div>
                            <h3 class="mb-1">{{ task_alerts.count }}</h3>
                            <p class="mb-0 text-muted">مهام متأخرة</p>
                        </div>
                    </div>
                    <div class="col-md-3 col-6 border-bottom border-top-md-0 p-4 text-center">
                        <div class="alert-summary-item">
                            <div class="alert-icon d-inline-block rounded-circle bg-success-subtle text-success mb-2">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <h3 class="mb-1">{{ insurance_alerts.count }}</h3>
                            <p class="mb-0 text-muted">تأمينات بحاجة للمتابعة</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Alerts Tabs -->
        <div class="card shadow-sm">
            <div class="card-header bg-light p-0 border-bottom">
                <ul class="nav nav-tabs card-header-tabs" id="alertsTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="contracts-tab" data-bs-toggle="tab" data-bs-target="#contracts" type="button" role="tab" aria-controls="contracts" aria-selected="true">
                            <i class="fas fa-file-contract me-1"></i>
                            العقود
                            {% if contract_alerts.count > 0 %}
                            <span class="badge rounded-pill bg-danger ms-1">{{ contract_alerts.count }}</span>
                            {% endif %}
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="health-tab" data-bs-toggle="tab" data-bs-target="#health" type="button" role="tab" aria-controls="health" aria-selected="false">
                            <i class="fas fa-id-card me-1"></i>
                            البطاقات الصحية
                            {% if health_card_alerts.count > 0 %}
                            <span class="badge rounded-pill bg-warning text-dark ms-1">{{ health_card_alerts.count }}</span>
                            {% endif %}
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="tasks-tab" data-bs-toggle="tab" data-bs-target="#tasks" type="button" role="tab" aria-controls="tasks" aria-selected="false">
                            <i class="fas fa-tasks me-1"></i>
                            المهام
                            {% if task_alerts.count > 0 %}
                            <span class="badge rounded-pill bg-info ms-1">{{ task_alerts.count }}</span>
                            {% endif %}
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="insurance-tab" data-bs-toggle="tab" data-bs-target="#insurance" type="button" role="tab" aria-controls="insurance" aria-selected="false">
                            <i class="fas fa-shield-alt me-1"></i>
                            التأمينات
                            {% if insurance_alerts.count > 0 %}
                            <span class="badge rounded-pill bg-success ms-1">{{ insurance_alerts.count }}</span>
                            {% endif %}
                        </button>
                    </li>
                </ul>
            </div>
            <div class="card-body p-0">
                <div class="tab-content" id="alertsTabContent">
                    <!-- Contracts Tab -->
                    <div class="tab-pane fade show active" id="contracts" role="tabpanel" aria-labelledby="contracts-tab">
                        {% if contract_alerts %}
                            <div class="table-responsive">
                                <table class="table table-hover align-middle mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th class="py-3 px-4">الموظف</th>
                                            <th class="py-3 px-4">القسم</th>
                                            <th class="py-3 px-4">تاريخ انتهاء العقد</th>
                                            <th class="py-3 px-4">الأيام المتبقية</th>
                                            <th class="py-3 px-4 text-center">الإجرا
