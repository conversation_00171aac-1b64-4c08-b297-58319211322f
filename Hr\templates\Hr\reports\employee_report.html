{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load django_permissions %}

{% block title %}تقرير الموظفين{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="page-title mb-4">تقرير الموظفين</h2>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <form method="get" class="mb-4">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="department" class="form-label">القسم</label>
                    <select name="department" id="department" class="form-select">
                        <option value="">-- جميع الأقسام --</option>
                        {% for dept in departments %}
                        <option value="{{ dept.id }}" {% if selected_department == dept.id|stringformat:"s" %}selected{% endif %}>
                            {{ dept.dept_name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="working_condition" class="form-label">حالة العمل</label>
                    <select name="working_condition" id="working_condition" class="form-select">
                        <option value="">-- جميع الحالات --</option>
                        {% for condition_code, condition_name in employee.WORKING_CONDITION_CHOICES %}
                        <option value="{{ condition_code }}" {% if selected_working_condition == condition_code %}selected{% endif %}>
                            {{ condition_name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col-12 text-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-1"></i>
                    بحث
                </button>
                <a href="{% url 'Hr:reports:employee_report' %}" class="btn btn-secondary">
                    <i class="fas fa-redo me-1"></i>
                    إعادة تعيين
                </a>
                {% if perms.Hr.export_employee_data or user|is_admin %}
                <div class="btn-group ms-2">
                    <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-download me-1"></i>
                        تصدير
                    </button>
                    <ul class="dropdown-menu">
                        <li>
                            <a class="dropdown-item" href="?{{ request.GET.urlencode }}&export=excel">
                                <i class="fas fa-file-excel me-1 text-success"></i>
                                Excel
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="?{{ request.GET.urlencode }}&export=csv">
                                <i class="fas fa-file-csv me-1 text-info"></i>
                                CSV
                            </a>
                        </li>
                    </ul>
                </div>
                {% endif %}
            </div>
        </div>
    </form>

    <!-- جدول البيانات -->
    {% if employees %}
    <div class="table-responsive">
        <table class="table table-hover">
            <thead>
                <tr>
                    <th>رقم الموظف</th>
                    <th>الاسم</th>
                    <th>القسم</th>
                    <th>الوظيفة</th>
                    <th>حالة العمل</th>
                    <th>تاريخ التعيين</th>
                    <th>حالة التأمين</th>
                    {% if perms.Hr.view_employee_details or user|is_admin %}
                    <th class="text-center">الإجراءات</th>
                    {% endif %}
                </tr>
            </thead>
            <tbody>
                {% for emp in employees %}
                <tr>
                    <td>{{ emp.emp_id }}</td>
                    <td>{{ emp.emp_full_name }}</td>
                    <td>{{ emp.department.dept_name|default:emp.dept_name }}</td>
                    <td>{{ emp.jop_name }}</td>
                    <td>{{ emp.working_condition }}</td>
                    <td>{{ emp.emp_date_hiring|date:"Y-m-d" }}</td>
                    <td>{{ emp.insurance_status }}</td>
                    {% if perms.Hr.view_employee_details or user|is_admin %}
                    <td class="text-center">
                        <a href="{% url 'Hr:employees:detail' emp.emp_id %}" class="btn btn-sm btn-primary">
                            <i class="fas fa-eye"></i>
                        </a>
                        {% if perms.Hr.print_employee or user|is_admin %}
                        <a href="{% url 'Hr:employees:print' emp.emp_id %}" class="btn btn-sm btn-info">
                            <i class="fas fa-print"></i>
                        </a>
                        {% endif %}
                    </td>
                    {% endif %}
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <div class="text-center py-5">
        <p>لا توجد بيانات للعرض</p>
    </div>
    {% endif %}
</div>
{% endblock %}
