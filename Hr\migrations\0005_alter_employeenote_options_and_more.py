# Generated by Django 5.0.14 on 2025-06-15 06:01

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Hr', '0004_alter_employeesalaryitem_options_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='employeenote',
            options={'managed': True, 'ordering': ['-created_at'], 'permissions': [('view_confidential_notes', 'يمكن عرض الملاحظات السرية'), ('manage_all_notes', 'يمكن إدارة جميع الملاحظات')], 'verbose_name': 'ملاحظة الموظف', 'verbose_name_plural': 'ملاحظات الموظفين'},
        ),
        migrations.AddField(
            model_name='employeenote',
            name='evaluation_link',
            field=models.CharField(blank=True, help_text='رابط اختياري لربط الملاحظة بتقييم الأداء', max_length=500, null=True, verbose_name='رابط التقييم'),
        ),
        migrations.AddField(
            model_name='employeenote',
            name='evaluation_score',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='درجة التقييم المرتبطة بالملاحظة (اختياري)', max_digits=5, null=True, verbose_name='درجة التقييم'),
        ),
        migrations.AddField(
            model_name='employeenote',
            name='follow_up_date',
            field=models.DateField(blank=True, null=True, verbose_name='تاريخ المتابعة'),
        ),
        migrations.AddField(
            model_name='employeenote',
            name='follow_up_required',
            field=models.BooleanField(default=False, verbose_name='يتطلب متابعة'),
        ),
        migrations.AddField(
            model_name='employeenote',
            name='is_active',
            field=models.BooleanField(default=True, help_text='إلغاء تفعيل الملاحظة بدلاً من حذفها', verbose_name='نشطة'),
        ),
        migrations.AddField(
            model_name='employeenote',
            name='is_confidential',
            field=models.BooleanField(default=False, help_text='ملاحظة سرية - محدودة الوصول', verbose_name='سرية'),
        ),
        migrations.AddField(
            model_name='employeenote',
            name='last_modified_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='modified_employee_notes', to=settings.AUTH_USER_MODEL, verbose_name='آخر تعديل بواسطة'),
        ),
        migrations.AddField(
            model_name='employeenote',
            name='note_type',
            field=models.CharField(choices=[('positive', 'إيجابية/جيدة'), ('negative', 'سلبية/ضعيفة'), ('general', 'عامة/محايدة')], default='general', max_length=20, verbose_name='نوع الملاحظة'),
        ),
        migrations.AddField(
            model_name='employeenote',
            name='priority',
            field=models.CharField(choices=[('low', 'منخفضة'), ('medium', 'متوسطة'), ('high', 'عالية'), ('urgent', 'عاجلة')], default='medium', max_length=20, verbose_name='الأولوية'),
        ),
        migrations.AddField(
            model_name='employeenote',
            name='tags',
            field=models.CharField(blank=True, help_text='علامات مفصولة بفواصل للبحث والتصنيف', max_length=500, null=True, verbose_name='العلامات'),
        ),
        migrations.AlterField(
            model_name='employeenote',
            name='content',
            field=models.TextField(help_text='اكتب تفاصيل الملاحظة هنا', verbose_name='محتوى الملاحظة'),
        ),
        migrations.AlterField(
            model_name='employeenote',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_employee_notes', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة'),
        ),
        migrations.AlterField(
            model_name='employeenote',
            name='is_important',
            field=models.BooleanField(default=False, verbose_name='ملاحظة مهمة'),
        ),
        migrations.AlterField(
            model_name='employeenote',
            name='title',
            field=models.CharField(max_length=200, verbose_name='عنوان الملاحظة'),
        ),
        migrations.AlterField(
            model_name='employeenote',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='تاريخ آخر تحديث'),
        ),
        migrations.CreateModel(
            name='EmployeeNoteHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('created', 'تم الإنشاء'), ('updated', 'تم التحديث'), ('deleted', 'تم الحذف'), ('restored', 'تم الاستعادة')], max_length=20, verbose_name='الإجراء')),
                ('changed_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التغيير')),
                ('old_values', models.JSONField(blank=True, null=True, verbose_name='القيم السابقة')),
                ('new_values', models.JSONField(blank=True, null=True, verbose_name='القيم الجديدة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات التغيير')),
                ('changed_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم التغيير بواسطة')),
                ('note', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='history', to='Hr.employeenote', verbose_name='الملاحظة')),
            ],
            options={
                'verbose_name': 'تاريخ ملاحظة الموظف',
                'verbose_name_plural': 'تاريخ ملاحظات الموظفين',
                'ordering': ['-changed_at'],
                'managed': True,
            },
        ),
    ]
