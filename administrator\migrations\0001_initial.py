# Generated by Django 5.0.14 on 2025-05-07 05:28

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        ('contenttypes', '0002_remove_content_type_name'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AppModule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم التطبيق')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='كود التطبيق')),
                ('description', models.TextField(blank=True, verbose_name='وصف التطبيق')),
                ('icon', models.CharField(blank=True, max_length=50, verbose_name='أيقونة التطبيق')),
                ('order', models.IntegerField(default=0, verbose_name='الترتيب')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
            ],
            options={
                'verbose_name': 'تطبيق',
                'verbose_name_plural': 'التطبيقات',
                'ordering': ['order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='SystemSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('db_host', models.CharField(max_length=255, verbose_name='قاعدة البيانات - المضيف')),
                ('db_name', models.CharField(max_length=255, verbose_name='قاعدة البيانات - الاسم')),
                ('db_user', models.CharField(max_length=255, verbose_name='قاعدة البيانات - اسم المستخدم')),
                ('db_password', models.CharField(max_length=255, verbose_name='قاعدة البيانات - كلمة المرور')),
                ('db_port', models.CharField(default='1433', max_length=10, verbose_name='قاعدة البيانات - المنفذ')),
                ('company_name', models.CharField(max_length=255, verbose_name='اسم الشركة')),
                ('company_address', models.TextField(blank=True, verbose_name='عنوان الشركة')),
                ('company_phone', models.CharField(blank=True, max_length=50, verbose_name='هاتف الشركة')),
                ('company_email', models.EmailField(blank=True, max_length=254, verbose_name='البريد الإلكتروني للشركة')),
                ('company_website', models.URLField(blank=True, verbose_name='موقع الشركة الإلكتروني')),
                ('company_logo', models.ImageField(blank=True, null=True, upload_to='company/', verbose_name='شعار الشركة')),
                ('system_name', models.CharField(default='نظام الدولية', max_length=255, verbose_name='اسم النظام')),
                ('enable_debugging', models.BooleanField(default=False, verbose_name='تفعيل وضع التصحيح')),
                ('maintenance_mode', models.BooleanField(default=False, verbose_name='وضع الصيانة')),
                ('timezone', models.CharField(default='Asia/Riyadh', max_length=50, verbose_name='المنطقة الزمنية')),
                ('date_format', models.CharField(default='Y-m-d', max_length=50, verbose_name='تنسيق التاريخ')),
                ('language', models.CharField(choices=[('ar', 'العربية'), ('en', 'English')], default='ar', max_length=2, verbose_name='لغة النظام')),
                ('font_family', models.CharField(choices=[('cairo', 'Cairo'), ('tajawal', 'Tajawal'), ('almarai', 'Almarai'), ('ibm-plex-sans-arabic', 'IBM Plex Sans Arabic'), ('noto-sans-arabic', 'Noto Sans Arabic')], default='cairo', max_length=50, verbose_name='الخط المستخدم')),
                ('text_direction', models.CharField(choices=[('rtl', 'من اليمين إلى اليسار'), ('ltr', 'من اليسار إلى اليمين')], default='rtl', max_length=3, verbose_name='اتجاه النص')),
                ('last_modified', models.DateTimeField(auto_now=True, verbose_name='آخر تعديل')),
            ],
            options={
                'verbose_name': 'إعدادات النظام',
                'verbose_name_plural': 'إعدادات النظام',
            },
        ),
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم القسم')),
                ('icon', models.CharField(help_text='اسم الأيقونة من Font Awesome مثال: fa-user', max_length=50, verbose_name='أيقونة القسم')),
                ('url_name', models.CharField(help_text='الاسم المستخدم في الروابط', max_length=100, verbose_name='اسم الرابط')),
                ('description', models.CharField(blank=True, max_length=255, verbose_name='وصف القسم')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('order', models.IntegerField(default=0, verbose_name='الترتيب')),
                ('require_admin', models.BooleanField(default=False, verbose_name='يتطلب صلاحيات المدير')),
                ('groups', models.ManyToManyField(blank=True, related_name='allowed_departments', to='auth.group', verbose_name='المجموعات المسموح لها')),
            ],
            options={
                'verbose_name': 'القسم',
                'verbose_name_plural': 'الأقسام',
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='GroupProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField(blank=True, verbose_name='وصف المجموعة')),
                ('group', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to='auth.group', verbose_name='المجموعة')),
            ],
            options={
                'verbose_name': 'ملف المجموعة',
                'verbose_name_plural': 'ملفات المجموعات',
            },
        ),
        migrations.CreateModel(
            name='Module',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الوحدة')),
                ('icon', models.CharField(max_length=50, verbose_name='أيقونة الوحدة')),
                ('url', models.CharField(max_length=255, verbose_name='رابط الوحدة')),
                ('description', models.CharField(blank=True, max_length=255, verbose_name='وصف الوحدة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('order', models.IntegerField(default=0, verbose_name='الترتيب')),
                ('bg_color', models.CharField(default='#3498db', max_length=20, verbose_name='لون الخلفية')),
                ('require_admin', models.BooleanField(default=False, verbose_name='يتطلب صلاحيات المدير')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='modules', to='administrator.department', verbose_name='القسم')),
                ('groups', models.ManyToManyField(blank=True, related_name='allowed_modules', to='auth.group', verbose_name='المجموعات المسموح لها')),
            ],
            options={
                'verbose_name': 'الوحدة',
                'verbose_name_plural': 'الوحدات',
                'ordering': ['department__order', 'order'],
            },
        ),
        migrations.CreateModel(
            name='OperationPermission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم العملية')),
                ('permission_type', models.CharField(choices=[('view', 'عرض'), ('add', 'إضافة'), ('edit', 'تعديل'), ('delete', 'حذف'), ('print', 'طباعة')], max_length=10, verbose_name='نوع الصلاحية')),
                ('code', models.CharField(max_length=100, verbose_name='كود العملية')),
                ('description', models.TextField(blank=True, verbose_name='وصف العملية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('app_module', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='operations', to='administrator.appmodule', verbose_name='التطبيق')),
            ],
            options={
                'verbose_name': 'صلاحية عملية',
                'verbose_name_plural': 'صلاحيات العمليات',
                'ordering': ['app_module__name', 'name', 'permission_type'],
                'unique_together': {('app_module', 'code', 'permission_type')},
            },
        ),
        migrations.CreateModel(
            name='PagePermission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الصفحة')),
                ('url_pattern', models.CharField(max_length=255, verbose_name='نمط URL')),
                ('template_path', models.CharField(blank=True, max_length=255, verbose_name='مسار القالب')),
                ('description', models.TextField(blank=True, verbose_name='وصف الصفحة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('app_module', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='pages', to='administrator.appmodule', verbose_name='التطبيق')),
            ],
            options={
                'verbose_name': 'صلاحية صفحة',
                'verbose_name_plural': 'صلاحيات الصفحات',
                'ordering': ['app_module__name', 'name'],
                'unique_together': {('app_module', 'url_pattern')},
            },
        ),
        migrations.CreateModel(
            name='TemplatePermission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم القالب')),
                ('app_name', models.CharField(max_length=50, verbose_name='اسم التطبيق')),
                ('template_path', models.CharField(max_length=255, verbose_name='مسار القالب')),
                ('url_pattern', models.CharField(blank=True, max_length=255, verbose_name='نمط URL')),
                ('description', models.TextField(blank=True, verbose_name='وصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('groups', models.ManyToManyField(blank=True, related_name='template_permissions', to='auth.group', verbose_name='المجموعات المسموح لها')),
                ('users', models.ManyToManyField(blank=True, related_name='template_permissions', to=settings.AUTH_USER_MODEL, verbose_name='المستخدمين المسموح لهم')),
            ],
            options={
                'verbose_name': 'صلاحية قالب',
                'verbose_name_plural': 'صلاحيات القوالب',
                'ordering': ['app_name', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Permission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('permission_type', models.CharField(choices=[('view', 'عرض'), ('add', 'إضافة'), ('change', 'تعديل'), ('delete', 'حذف'), ('print', 'طباعة')], max_length=10, verbose_name='نوع الصلاحية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('groups', models.ManyToManyField(blank=True, related_name='custom_permissions', to='auth.group', verbose_name='المجموعات المسموح لها')),
                ('module', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='permissions', to='administrator.module', verbose_name='الوحدة')),
                ('users', models.ManyToManyField(blank=True, related_name='custom_permissions', to=settings.AUTH_USER_MODEL, verbose_name='المستخدمين المسموح لهم')),
            ],
            options={
                'verbose_name': 'الصلاحية',
                'verbose_name_plural': 'الصلاحيات',
                'unique_together': {('module', 'permission_type')},
            },
        ),
        migrations.CreateModel(
            name='PermissionAuditLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timestamp', models.DateTimeField(auto_now_add=True, verbose_name='وقت التغيير')),
                ('action', models.CharField(choices=[('add', 'إضافة'), ('remove', 'إزالة'), ('modify', 'تعديل')], max_length=10, verbose_name='الإجراء')),
                ('object_type', models.CharField(choices=[('user', 'مستخدم'), ('group', 'مجموعة'), ('permission', 'صلاحية'), ('module', 'وحدة'), ('department', 'قسم'), ('template', 'قالب')], max_length=20, verbose_name='نوع الكائن')),
                ('object_id', models.PositiveIntegerField(verbose_name='معرف الكائن')),
                ('description', models.TextField(verbose_name='وصف التغيير')),
                ('data', models.JSONField(blank=True, null=True, verbose_name='بيانات إضافية')),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype', verbose_name='نوع المحتوى')),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='permission_audit_logs', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'سجل تدقيق الصلاحيات',
                'verbose_name_plural': 'سجلات تدقيق الصلاحيات',
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['user'], name='administrat_user_id_610edc_idx'), models.Index(fields=['action'], name='administrat_action_04c8da_idx'), models.Index(fields=['object_type'], name='administrat_object__bafb01_idx'), models.Index(fields=['content_type', 'object_id'], name='administrat_content_c3afb0_idx'), models.Index(fields=['timestamp'], name='administrat_timesta_af25c2_idx')],
            },
        ),
        migrations.CreateModel(
            name='UserDepartmentPermission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('can_view', models.BooleanField(default=True, verbose_name='عرض')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='administrator.department', verbose_name='القسم')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='department_permissions', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'صلاحية قسم',
                'verbose_name_plural': 'صلاحيات الأقسام',
                'unique_together': {('user', 'department')},
            },
        ),
        migrations.CreateModel(
            name='UserGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date_joined', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الانضمام')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_memberships', to='auth.group', verbose_name='المجموعة')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='group_memberships', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'عضوية مجموعة',
                'verbose_name_plural': 'عضويات المجموعات',
                'unique_together': {('user', 'group')},
            },
        ),
        migrations.CreateModel(
            name='UserModulePermission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('can_view', models.BooleanField(default=True, verbose_name='عرض')),
                ('can_add', models.BooleanField(default=False, verbose_name='إضافة')),
                ('can_edit', models.BooleanField(default=False, verbose_name='تعديل')),
                ('can_delete', models.BooleanField(default=False, verbose_name='حذف')),
                ('can_print', models.BooleanField(default=False, verbose_name='طباعة')),
                ('module', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='administrator.module', verbose_name='الوحدة')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='module_permissions', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'صلاحية وحدة',
                'verbose_name_plural': 'صلاحيات الوحدات',
                'unique_together': {('user', 'module')},
            },
        ),
        migrations.CreateModel(
            name='UserOperationPermission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('operation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_permissions', to='administrator.operationpermission', verbose_name='العملية')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='operation_permissions', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'صلاحية عملية للمستخدم',
                'verbose_name_plural': 'صلاحيات العمليات للمستخدمين',
                'unique_together': {('user', 'operation')},
            },
        ),
        migrations.CreateModel(
            name='UserPagePermission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('page', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_permissions', to='administrator.pagepermission', verbose_name='الصفحة')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='page_permissions', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'صلاحية صفحة للمستخدم',
                'verbose_name_plural': 'صلاحيات الصفحات للمستخدمين',
                'unique_together': {('user', 'page')},
            },
        ),
    ]
