<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد قاعدة البيانات - نظام الدولية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* RTL reset for Bootstrap */
        body {
            direction: rtl;
            text-align: right;
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }

        .db-setup-container {
            max-width: 900px;
            margin: 50px auto;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
            padding: 1.5rem;
            border-bottom: none;
        }

        .card-title {
            font-weight: 700;
            margin-bottom: 0;
            color: white;
            display: flex;
            align-items: center;
        }

        .card-title i {
            margin-left: 10px;
            font-size: 1.5rem;
        }

        .card-body {
            padding: 2rem;
            background-color: #ffffff;
        }

        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .form-control, .form-select {
            border-radius: 10px;
            padding: 0.75rem 1rem;
            border: 2px solid #e3e6f0;
            transition: all 0.3s;
            background-color: white;
        }

        .form-control:focus, .form-select:focus {
            border-color: #4e73df;
            box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
        }

        .form-label {
            font-weight: 600;
            color: #5a5c69;
            margin-bottom: 0.5rem;
        }

        .form-text {
            color: #858796;
        }

        .btn {
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
            border: none;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #224abe 0%, #1a3a94 100%);
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #858796 0%, #636678 100%);
            border: none;
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #636678 0%, #4e4f5c 100%);
            transform: translateY(-2px);
        }

        .btn-lg {
            padding: 1rem 2rem;
        }

        .input-group-text {
            background-color: #4e73df;
            color: white;
            border: none;
            border-radius: 10px 0 0 10px;
        }

        .connection-status {
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1.5rem;
        }

        .connection-status.success {
            background-color: rgba(28, 200, 138, 0.1);
            border: 1px solid rgba(28, 200, 138, 0.5);
            color: #13855c;
        }

        .connection-status.error {
            background-color: rgba(231, 74, 59, 0.1);
            border: 1px solid rgba(231, 74, 59, 0.5);
            color: #be2617;
        }

        .connection-status.warning {
            background-color: rgba(246, 194, 62, 0.1);
            border: 1px solid rgba(246, 194, 62, 0.5);
            color: #9d7c1e;
        }

        .connection-status.info {
            background-color: rgba(78, 115, 223, 0.1);
            border: 1px solid rgba(78, 115, 223, 0.5);
            color: #224abe;
        }

        .connection-status i {
            margin-left: 0.5rem;
        }

        .form-section {
            background-color: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }

        .form-section-title {
            font-size: 1.1rem;
            font-weight: 700;
            color: #4e73df;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #e3e6f0;
        }

        .logo-container {
            text-align: center;
            margin-bottom: 20px;
        }

        .logo {
            max-width: 150px;
            height: auto;
        }

        .error-banner {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .loader {
            display: none;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container db-setup-container">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title text-white mb-0">
                    <i class="fas fa-database"></i>
                    إعداد قاعدة بيانات النظام
                </h4>
            </div>
            <div class="card-body">
                <div class="logo-container">
                    <h2 class="mb-4">نظام الشركة الدولية</h2>
                </div>

                {% if messages %}
                    {% for message in messages %}
                        <div class="alert {% if message.tags == 'error' %}alert-danger{% elif message.tags == 'success' %}alert-success{% elif message.tags == 'warning' %}alert-warning{% else %}alert-info{% endif %}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}

                {% if connection_error %}
                <div class="error-banner">
                    <h5><i class="fas fa-exclamation-triangle"></i> تعذر الاتصال بقاعدة البيانات</h5>
                    <p>{{ connection_error }}</p>
                    <p>يرجى التأكد من صحة إعدادات الاتصال بقاعدة البيانات أدناه.</p>
                </div>

                <div class="alert alert-info mb-4">
                    <h5><i class="fas fa-lightbulb"></i> إرشادات لحل مشكلة الاتصال</h5>
                    <ul class="mb-0">
                        <li>تأكد من أن خادم SQL Server مثبت ويعمل على الجهاز أو الخادم</li>
                        <li>تأكد من صحة اسم الخادم (مثال: <code>DESKTOP-PC\SQLEXPRESS</code> أو <code>localhost</code>)</li>
                        <li>تأكد من أن مصادقة SQL Server تعمل بشكل صحيح (اسم المستخدم وكلمة المرور)</li>
                        <li>تأكد من أن قاعدة البيانات موجودة أو قم بإنشاء قاعدة بيانات جديدة</li>
                        <li>تأكد من أن جدار الحماية لا يمنع الاتصال بـ SQL Server</li>
                    </ul>
                </div>
                {% endif %}

                <div class="connection-status info">
                    <i class="fas fa-info-circle"></i>
                    <span>من فضلك قم بتكوين إعدادات الاتصال بقاعدة البيانات لمتابعة استخدام النظام</span>
                </div>

                <div class="alert alert-warning mb-4">
                    <h5><i class="fas fa-exclamation-circle"></i> ملاحظة هامة</h5>
                    <p>يجب أن يكون لديك خادم SQL Server مثبت ومكون بشكل صحيح قبل متابعة إعداد قاعدة البيانات.</p>
                    <p>إذا لم يكن لديك SQL Server مثبت، يمكنك تحميله من <a href="https://www.microsoft.com/en-us/sql-server/sql-server-downloads" target="_blank">موقع Microsoft الرسمي</a>.</p>
                </div>

                <form method="post" class="needs-validation" novalidate id="dbSettingsForm">
                    {% csrf_token %}

                    <div class="form-section">
                        <h5 class="form-section-title"><i class="fas fa-database me-2"></i>نوع الاتصال بقاعدة البيانات</h5>

                        <!-- Database Connection Type -->
                        <div class="form-group">
                            <label for="db_connection_type" class="form-label">{{ form.db_connection_type.label }}</label>
                            <select class="form-select" id="db_connection_type" name="db_connection_type" required>
                                {% for value, text in form.db_connection_type.field.choices %}
                                    <option value="{{ value }}" {% if form.db_connection_type.value == value %}selected{% endif %}>{{ text }}</option>
                                {% endfor %}
                            </select>
                            <div class="form-text">{{ form.db_connection_type.help_text }}</div>
                        </div>

                        <!-- Hidden Database Engine Field -->
                        <input type="hidden" id="db_engine" name="db_engine" value="mssql">
                    </div>

                    <div class="form-section">
                        <h5 class="form-section-title"><i class="fas fa-server me-2"></i>معلومات الاتصال بـ SQL Server</h5>

                        <!-- Windows Authentication -->
                        <div class="form-group form-check mb-4">
                            <input type="checkbox" class="form-check-input" id="use_windows_auth" name="use_windows_auth"
                                   {% if form.use_windows_auth.value %}checked{% endif %}>
                            <label class="form-check-label" for="use_windows_auth">استخدام مصادقة Windows</label>
                            <div class="form-text">تمكين هذا الخيار إذا كنت ترغب في استخدام مصادقة Windows بدلاً من مصادقة SQL Server</div>
                        </div>

                        <!-- Host Name -->
                        <div class="form-group">
                            <label for="host" class="form-label">اسم الخادم (Server Name)</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-server"></i></span>
                                <input type="text" class="form-control" id="host" name="db_host"
                                       placeholder="مثال: SERVERNAME\SQLEXPRESS أو localhost"
                                       value="{{ form.db_host.value|default:'' }}">
                            </div>
                            <div class="form-text">أدخل اسم الخادم أو عنوان IP الخاص بخادم SQL Server</div>
                        </div>

                        <div class="row">
                            <!-- Username -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="username" class="form-label">اسم المستخدم</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                                        <input type="text" class="form-control" id="username" name="db_user"
                                               placeholder="اسم المستخدم" value="{{ form.db_user.value|default:'' }}">
                                    </div>
                                    <div class="form-text">اسم المستخدم للاتصال بقاعدة البيانات</div>
                                </div>
                            </div>

                            <!-- Password -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="password" class="form-label">كلمة المرور</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                        <input type="password" class="form-control" id="password" name="db_password"
                                               placeholder="كلمة المرور" value="{{ form.db_password.value|default:'' }}">
                                    </div>
                                    <div class="form-text">كلمة المرور للاتصال بقاعدة البيانات</div>
                                </div>
                            </div>
                        </div>

                        <!-- Test Connection Button -->
                        <div class="form-group text-center">
                            <button type="button" class="btn btn-secondary" id="testConnection">
                                <span class="loader" id="connectionLoader"></span>
                                <i class="fas fa-plug me-2"></i>اختبار الاتصال
                            </button>
                        </div>
                    </div>

                    <div class="form-section">
                        <h5 class="form-section-title"><i class="fas fa-database me-2"></i>اختيار قاعدة البيانات</h5>

                        <!-- Database Name -->
                        <div class="form-group">
                            <label for="database" class="form-label">اسم قاعدة البيانات</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-database"></i></span>
                                <select class="form-select" id="database" name="db_name">
                                    <option value="">يرجى اختبار الاتصال أولاً لعرض قواعد البيانات</option>
                                    {% if form.db_name.value %}
                                    <option value="{{ form.db_name.value }}" selected>{{ form.db_name.value }}</option>
                                    {% endif %}
                                </select>
                            </div>
                            <div class="form-text">اختر قاعدة بيانات SQL Server التي تريد الاتصال بها</div>
                        </div>

                        <!-- Port -->
                        <div class="form-group">
                            <label for="db_port" class="form-label">المنفذ (Port)</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-network-wired"></i></span>
                                <input type="text" class="form-control" id="db_port" name="db_port"
                                       placeholder="1433" value="{{ form.db_port.value|default:'1433' }}">
                            </div>
                            <div class="form-text">منفذ الاتصال بقاعدة البيانات (عادة 1433 لخادم SQL Server)</div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-primary btn-lg" id="saveButton">
                            <i class="fas fa-save me-2"></i>حفظ الإعدادات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap and custom JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/administrator/js/db_setup.js"></script>
</body>
</html>
