{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load django_permissions %}

{% block title %}{{ page_title }} - نظام الدولية{% endblock %}

{% block page_title %}{{ page_title }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموارد البشرية</a></li>
<li class="breadcrumb-item active">{{ page_title }}</li>
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">حساب الرواتب</h5>
    </div>
    <div class="card-body">
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle"></i>
            تحذير: سيقوم النظام بحساب رواتب جميع الموظفين النشطين للفترة المحددة. تأكد من إعداد بنود الرواتب وقواعد الحضور بشكل صحيح قبل المتابعة.
        </div>

        <form method="post">
            {% csrf_token %}

            <div class="mb-3">
                <label for="{{ form.payroll_period.id_for_label }}" class="form-label">{{ form.payroll_period.label }}</label>
                {{ form.payroll_period }}
                {% if form.payroll_period.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.payroll_period.errors %}
                    {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="mb-3 form-check">
                {{ form.include_attendance }}
                <label for="{{ form.include_attendance.id_for_label }}" class="form-check-label">{{ form.include_attendance.label }}</label>
                {% if form.include_attendance.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.include_attendance.errors %}
                    {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="mb-3 form-check">
                {{ form.recalculate }}
                <label for="{{ form.recalculate.id_for_label }}" class="form-check-label">{{ form.recalculate.label }}</label>
                {% if form.recalculate.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.recalculate.errors %}
                    {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="actions text-end mb-3">
                {% if perms.Hr.add_payroll or user|is_admin %}
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-calculator me-1"></i> احتساب الرواتب
                    </button>
                {% endif %}
                <a href="{% url 'Hr:payroll_entry_list' %}" class="btn btn-secondary">
                    <i class="fas fa-list"></i> عرض سجلات الرواتب
                </a>
            </div>
        </form>
    </div>
</div>

<div class="card mt-4">
    <div class="card-header">
        <h5 class="mb-0">سجلات الرواتب</h5>
    </div>
    <div class="card-body">
        <table class="table table-striped table-bordered">
            <thead>
                <tr>
                    <th class="text-center">#</th>
                    <th>الفترة</th>
                    <th>الموظف</th>
                    <th class="text-center">الراتب الأساسي</th>
                    <th class="text-center">البدلات</th>
                    <th class="text-center">الخصومات</th>
                    <th class="text-center">صافي الراتب</th>
                    <th class="text-center">الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for payroll in payrolls %}
                <tr>
                    <td class="text-center">{{ forloop.counter }}</td>
                    <td>{{ payroll.period }}</td>
                    <td>{{ payroll.employee }}</td>
                    <td class="text-center">{{ payroll.basic_salary }}</td>
                    <td class="text-center">{{ payroll.allowances }}</td>
                    <td class="text-center">{{ payroll.deductions }}</td>
                    <td class="text-center">{{ payroll.net_salary }}</td>
                    <td class="text-center">
                        <div class="btn-group btn-group-sm">
                            {% if perms.Hr.change_payroll or user|is_admin %}
                                <a href="{% url 'Hr:payrolls:edit' payroll.id %}" class="btn btn-primary">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                            {% endif %}
                            {% if perms.Hr.view_payroll or user|is_admin %}
                                <a href="{% url 'Hr:payrolls:print' payroll.id %}" class="btn btn-info" target="_blank">
                                    <i class="fas fa-print"></i> طباعة
                                </a>
                            {% endif %}
                            {% if perms.Hr.delete_payroll or user|is_admin %}
                                <button type="button" class="btn btn-danger delete-payroll" 
                                        data-payroll-id="{{ payroll.id }}"
                                        data-payroll-period="{{ payroll.period }}">
                                    <i class="fas fa-trash"></i> حذف
                                </button>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="8" class="text-center">لا توجد سجلات رواتب لعرضها.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}
