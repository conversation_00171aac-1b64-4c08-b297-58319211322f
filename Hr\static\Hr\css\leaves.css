/* Leave Management Styles */

/* Leave Type Cards */
.leave-type-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.leave-type-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.leave-type-card .card-body {
    padding: 1.5rem;
}

.leave-type-stats {
    display: flex;
    justify-content: space-between;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(0,0,0,0.1);
}

/* Leave Status Badge Colors */
.badge.bg-pending {
    background-color: #f39c12 !important;
}

.badge.bg-approved {
    background-color: #2ecc71 !important;
}

.badge.bg-rejected {
    background-color: #e74c3c !important;
}

.badge.bg-cancelled {
    background-color: #95a5a6 !important;
}

/* Leave Calendar Styles */
.leave-calendar {
    background: #fff;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.leave-calendar .fc-event {
    cursor: pointer;
    padding: 2px 4px;
    margin: 2px 0;
    border-radius: 4px;
}

/* Leave Analytics Chart Colors */
.chart-container {
    position: relative;
    margin: auto;
    height: 300px;
}

.analytics-card {
    transition: all 0.3s ease;
}

.analytics-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.analytics-icon {
    font-size: 2rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin-bottom: 1rem;
}

/* Leave Form Styles */
.leave-form label {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.leave-form .form-control:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.leave-form .required label::after {
    content: " *";
    color: #e74c3c;
}

/* Leave Documents */
.leave-document {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 4px;
    margin-bottom: 0.5rem;
}

.leave-document i {
    margin-left: 0.5rem;
    color: #3498db;
}

/* Responsive Design */
@media (max-width: 768px) {
    .leave-stats {
        flex-direction: column;
    }
    
    .leave-stats .stat-card {
        margin-bottom: 1rem;
    }
    
    .chart-container {
        height: 200px;
    }
}
