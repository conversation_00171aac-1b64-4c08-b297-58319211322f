# Generated by Django 5.0.14 on 2025-05-07 12:08

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cars', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم المورد')),
                ('contact_person', models.CharField(blank=True, max_length=100, null=True, verbose_name='الشخص المسؤول')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('address', models.TextField(blank=True, null=True, verbose_name='العنوان')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'المورد',
                'verbose_name_plural': 'الموردين',
                'ordering': ['name'],
            },
        ),
        migrations.AlterField(
            model_name='car',
            name='car_code',
            field=models.CharField(max_length=20, unique=True, verbose_name='كود السيارة'),
        ),
        migrations.AlterField(
            model_name='car',
            name='car_name',
            field=models.CharField(max_length=100, verbose_name='اسم السيارة'),
        ),
        migrations.AlterField(
            model_name='car',
            name='car_status',
            field=models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive')], default='active', max_length=20, verbose_name='حالة السيارة'),
        ),
        migrations.AlterField(
            model_name='car',
            name='car_type',
            field=models.CharField(choices=[('microbus', 'Microbus'), ('bus', 'Bus'), ('passenger', 'Passenger'), ('private', 'Private')], max_length=20, verbose_name='نوع السيارة'),
        ),
        migrations.AlterField(
            model_name='car',
            name='distance_traveled',
            field=models.FloatField(default=0, verbose_name='المسافة المقطوعة'),
        ),
        migrations.AlterField(
            model_name='car',
            name='fuel_consumption_rate',
            field=models.FloatField(help_text='Fuel consumption rate in liters/km', verbose_name='معدل استهلاك الوقود'),
        ),
        migrations.AlterField(
            model_name='car',
            name='fuel_type',
            field=models.CharField(choices=[('diesel', 'Diesel'), ('gasoline', 'Gasoline'), ('gas', 'Gas')], max_length=20, verbose_name='نوع الوقود'),
        ),
        migrations.AlterField(
            model_name='car',
            name='passengers_count',
            field=models.PositiveIntegerField(verbose_name='عدد الركاب'),
        ),
        migrations.AddField(
            model_name='car',
            name='supplier',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='cars', to='cars.supplier', verbose_name='المورد'),
        ),
    ]
