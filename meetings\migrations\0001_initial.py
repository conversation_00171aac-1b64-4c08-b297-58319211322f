# Generated by Django 5.0.14 on 2025-05-07 05:28

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Meeting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان الاجتماع')),
                ('date', models.DateTimeField(verbose_name='تاريخ ووقت الاجتماع')),
                ('topic', models.TextField(verbose_name='موضوع الاجتماع')),
                ('status', models.CharField(choices=[('pending', 'قيد الانتظار'), ('completed', 'مكتمل'), ('cancelled', 'ملغي')], default='pending', max_length=20, verbose_name='حالة الاجتماع')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='meetings', to=settings.AUTH_USER_MODEL, verbose_name='منشئ الاجتماع')),
            ],
            options={
                'verbose_name': 'اجتماع',
                'verbose_name_plural': 'الاجتماعات',
            },
        ),
        migrations.CreateModel(
            name='Attendee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendees', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
                ('meeting', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendees', to='meetings.meeting', verbose_name='الاجتماع')),
            ],
            options={
                'verbose_name': 'حضور',
                'verbose_name_plural': 'الحضور',
                'unique_together': {('meeting', 'user')},
            },
        ),
    ]
