{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ page_title }} - نظام الدولية{% endblock %}

{% block page_title %}{{ page_title }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموظفين</a></li>
<li class="breadcrumb-item active">{{ page_title }}</li>
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">قواعد حضور الموظفين</h5>
        <div>
            <a href="{% url 'Hr:attendance:employee_attendance_rule_create' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة قاعدة جديدة
            </a>
            <a href="{% url 'Hr:attendance:employee_attendance_rule_bulk_create' %}" class="btn btn-success">
                <i class="fas fa-upload"></i> إضافة بالجملة
            </a>
        </div>
    </div>
    <div class="card-body">
        <!-- Filter Form -->
        <form method="get" class="mb-4">
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="employee" class="form-label">الموظف</label>
                        <select name="employee" id="employee" class="form-select">
                            <option value="">-- جميع الموظفين --</option>
                            {% for emp in employees %}
                            <option value="{{ emp.id }}" {% if selected_employee|stringformat:"s" == emp.id|stringformat:"s" %}selected{% endif %}>
                                {{ emp.emp_full_name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> بحث
                    </button>
                    {% if request.GET %}
                    <a href="{% url 'Hr:attendance:employee_attendance_rule_list' %}" class="btn btn-secondary ms-2">
                        <i class="fas fa-times"></i> إعادة تعيين
                    </a>
                    {% endif %}
                </div>
            </div>
        </form>

        {% if rules %}
        <div class="table-responsive">
            <table class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>الموظف</th>
                        <th>قاعدة الحضور</th>
                        <th>تاريخ السريان</th>
                        <th>تاريخ الانتهاء</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for rule in rules %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ rule.employee.emp_full_name }}</td>
                        <td>{{ rule.attendance_rule.name }}</td>
                        <td>{{ rule.effective_date }}</td>
                        <td>{{ rule.end_date|default:"-" }}</td>
                        <td>
                            {% if rule.is_active %}
                            <span class="badge bg-success">نشط</span>
                            {% else %}
                            <span class="badge bg-danger">غير نشط</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'Hr:attendance:employee_attendance_rule_edit' rule.pk %}" class="btn btn-primary">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                                <a href="{% url 'Hr:attendance:employee_attendance_rule_delete' rule.pk %}" class="btn btn-danger">
                                    <i class="fas fa-trash"></i> حذف
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            لا توجد قواعد حضور مسجلة للموظفين حتى الآن.
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}