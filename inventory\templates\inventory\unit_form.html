{% extends 'inventory/base_inventory.html' %}
{% load static %}

{% block title %}{% if form.instance.id %}تعديل وحدة قياس{% else %}إضافة وحدة قياس جديدة{% endif %}{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <h5 class="mb-0">{% if form.instance.id %}تعديل وحدة قياس{% else %}إضافة وحدة قياس جديدة{% endif %}</h5>
                </div>
                <div class="card-body">
                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="id_name" class="form-label">اسم وحدة القياس</label>
                                <input type="text" class="form-control" id="id_name" name="name" value="{{ form.name.value|default:'' }}" required>
                                <div class="invalid-feedback">
                                    يرجى إدخال اسم وحدة القياس
                                </div>
                                {% if form.name.errors %}
                                <div class="text-danger">
                                    {{ form.name.errors }}
                                </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="id_symbol" class="form-label">رمز وحدة القياس</label>
                                <input type="text" class="form-control" id="id_symbol" name="symbol" value="{{ form.symbol.value|default:'' }}">
                                <small class="text-muted">مثال: كجم، م، سم، لتر</small>
                                {% if form.symbol.errors %}
                                <div class="text-danger">
                                    {{ form.symbol.errors }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between mt-4">
                            <a href="{% url 'inventory:unit_list' %}" class="btn btn-light">
                                <i class="fas fa-arrow-right me-1"></i>
                                العودة للقائمة
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                حفظ
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
