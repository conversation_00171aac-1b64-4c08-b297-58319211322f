{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load django_permissions %}

{% block title %}تقرير مهام الموارد البشرية - نظام الدولية{% endblock %}

{% block content %}
<div class="container">
    <h1 class="my-4">تقرير مهام الموارد البشرية</h1>

    <form method="get" class="mb-4">
        <div class="row">
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="task_type" class="form-label">نوع المهمة</label>
                    <select class="form-select" id="task_type" name="task_type">
                        <option value="">جميع الأنواع</option>
                        {% for type_code, type_name in task_types %}
                        <option value="{{ type_code }}" {% if selected_task_type == type_code %}selected{% endif %}>
                            {{ type_name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="task_name" class="form-label">اسم المهمة</label>
                    <input type="text" class="form-control" id="task_name" name="task_name" value="{{ request.GET.task_name }}">
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="assigned_to" class="form-label">المكلف بها</label>
                    <select class="form-select" id="assigned_to" name="assigned_to">
                        <option value="">جميع المكلفين</option>
                        {% for user in users %}
                        <option value="{{ user.id }}" {% if request.GET.assigned_to == user.id|string %}selected{% endif %}>{{ user.get_full_name }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="status" class="form-label">الحالة</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="open" {% if request.GET.status == 'open' %}selected{% endif %}>مفتوحة</option>
                        <option value="closed" {% if request.GET.status == 'closed' %}selected{% endif %}>مغلقة</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="actions text-end">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-search me-1"></i>
                بحث
            </button>
            <a href="{% url 'Hr:reports:report_detail' 'hr_tasks' %}" class="btn btn-secondary">
                <i class="fas fa-redo me-1"></i>
                إعادة تعيين
            </a>
            {% if perms.Hr.export_hrtask_data or user|is_admin %}
            <div class="btn-group ms-2">
                <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-download me-1"></i>
                    تصدير
                </button>
                <ul class="dropdown-menu">
                    <li>
                        <a class="dropdown-item" href="?{{ request.GET.urlencode }}&export=excel">
                            <i class="fas fa-file-excel me-1 text-success"></i>
                            Excel
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="?{{ request.GET.urlencode }}&export=csv">
                            <i class="fas fa-file-csv me-1 text-info"></i>
                            CSV
                        </a>
                    </li>
                </ul>
            </div>
            {% endif %}
        </div>
    </form>

    <div class="table-responsive">
        <table class="table table-hover">
            <thead>
                <tr>
                    <th>المهمة</th>
                    <th>النوع</th>
                    <th>المكلف بها</th>
                    <th>الأولوية</th>
                    <th>الحالة</th>
                    <th>تاريخ البدء</th>
                    <th>تاريخ الاستحقاق</th>
                    <th>التقدم</th>
                    <th class="text-center">الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for task in tasks %}
                <tr>
                    <td>{{ task.name }}</td>
                    <td>{{ task.get_task_type_display }}</td>
                    <td>{{ task.assigned_to.get_full_name }}</td>
                    <td>{{ task.get_priority_display }}</td>
                    <td>
                        {% if task.status == 'open' %}
                        <span class="badge bg-success">مفتوحة</span>
                        {% else %}
                        <span class="badge bg-danger">مغلقة</span>
                        {% endif %}
                    </td>
                    <td>{{ task.start_date|date:"Y-m-d" }}</td>
                    <td>{{ task.end_date|date:"Y-m-d" }}</td>
                    <td>
                        <div class="progress" style="height: 20px;">
                            <div class="progress-bar" role="progressbar" style="width: {{ task.progress }}%;"
                                aria-valuenow="{{ task.progress }}" aria-valuemin="0" aria-valuemax="100">
                                {{ task.progress }}%
                            </div>
                        </div>
                    </td>
                    <td class="text-center">
                        {% if perms.Hr.view_hrtask or user|is_admin %}
                        <a href="{% url 'Hr:hr_tasks:detail' task.id %}" class="btn btn-sm btn-primary">
                            <i class="fas fa-eye"></i>
                        </a>
                        {% endif %}
                        {% if perms.Hr.print_hrtask or user|is_admin %}
                        <a href="{% url 'Hr:hr_tasks:print' task.id %}" class="btn btn-sm btn-info">
                            <i class="fas fa-print"></i>
                        </a>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}