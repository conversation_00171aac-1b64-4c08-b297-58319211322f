{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load django_permissions %}

{% block title %}تقرير الرواتب الشهري{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="page-title mb-4">تقرير الرواتب الشهري</h2>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <form method="get" class="mb-4">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="period" class="form-label">فترة الراتب</label>
                    <select name="period" id="period" class="form-select">
                        <option value="">-- جميع الفترات --</option>
                        {% for period in periods %}
                        <option value="{{ period.id }}" {% if selected_period == period.id|stringformat:"s" %}selected{% endif %}>
                            {{ period.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="department" class="form-label">القسم</label>
                    <select name="department" id="department" class="form-select">
                        <option value="">-- جميع الأقسام --</option>
                        {% for dept in departments %}
                        <option value="{{ dept.dept_code }}" {% if selected_department == dept.dept_code|stringformat:"s" %}selected{% endif %}>
                            {{ dept.dept_name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col-12 text-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-1"></i>
                    بحث
                </button>
                <a href="{% url 'Hr:reports:monthly_salary_report' %}" class="btn btn-secondary">
                    <i class="fas fa-redo me-1"></i>
                    إعادة تعيين
                </a>
                {% if perms.Hr.export_payroll_data or user|is_admin %}
                <div class="btn-group ms-2">
                    <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-download me-1"></i>
                        تصدير
                    </button>
                    <ul class="dropdown-menu">
                        <li>
                            <a class="dropdown-item" href="?{{ request.GET.urlencode }}&export=excel">
                                <i class="fas fa-file-excel me-1 text-success"></i>
                                Excel
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="?{{ request.GET.urlencode }}&export=csv">
                                <i class="fas fa-file-csv me-1 text-info"></i>
                                CSV
                            </a>
                        </li>
                    </ul>
                </div>
                {% endif %}
            </div>
        </div>
    </form>

    <!-- جدول البيانات -->
    {% if entries %}
    <div class="table-responsive">
        <table class="table table-hover">
            <thead>
                <tr>
                    <th>الرقم</th>
                    <th>الموظف</th>
                    <th>القسم</th>
                    <th>الراتب الأساسي</th>
                    <th>البدلات</th>
                    <th>الخصومات</th>
                    <th>صافي الراتب</th>
                    {% if perms.Hr.view_payroll_details or user|is_admin %}
                    <th class="text-center">الإجراءات</th>
                    {% endif %}
                </tr>
            </thead>
            <tbody>
                {% for entry in entries %}
                <tr>
                    <td>{{ entry.id }}</td>
                    <td>{{ entry.employee.emp_full_name }}</td>
                    <td>{{ entry.employee.department.dept_name }}</td>
                    <td>{{ entry.basic_salary|floatformat:2 }}</td>
                    <td>{{ entry.total_allowances|floatformat:2 }}</td>
                    <td>{{ entry.total_deductions|floatformat:2 }}</td>
                    <td>{{ entry.total_salary|floatformat:2 }}</td>
                    {% if perms.Hr.view_payroll_details or user|is_admin %}
                    <td class="text-center">
                        <a href="{% url 'Hr:payroll:entry_detail' entry.id %}" class="btn btn-sm btn-primary">
                            <i class="fas fa-eye"></i>
                        </a>
                        {% if perms.Hr.print_payroll or user|is_admin %}
                        <a href="{% url 'Hr:payroll:print_slip' entry.id %}" class="btn btn-sm btn-info">
                            <i class="fas fa-print"></i>
                        </a>
                        {% endif %}
                    </td>
                    {% endif %}
                </tr>
                {% endfor %}
            </tbody>
            <tfoot>
                <tr class="fw-bold">
                    <td colspan="3" class="text-end">الإجمالي:</td>
                    <td>{{ total_basic|floatformat:2 }}</td>
                    <td>{{ total_allowances|floatformat:2 }}</td>
                    <td>{{ total_deductions|floatformat:2 }}</td>
                    <td>{{ total_salary|floatformat:2 }}</td>
                    {% if perms.Hr.view_payroll_details or user|is_admin %}
                    <td></td>
                    {% endif %}
                </tr>
            </tfoot>
        </table>
    </div>
    {% else %}
    <div class="text-center py-5">
        <p>لا توجد بيانات للعرض</p>
    </div>
    {% endif %}
</div>
{% endblock %}
