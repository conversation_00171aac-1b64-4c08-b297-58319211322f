{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load django_permissions %}

{% block title %}بنود رواتب الموظفين - نظام الدولية{% endblock %}

{% block page_title %}بنود رواتب الموظفين{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموارد البشرية</a></li>
<li class="breadcrumb-item active">بنود رواتب الموظفين</li>
{% endblock %}

{% block page_actions %}
{% if perms.Hr.add_employeesalaryitem %}
<a href="{% url 'Hr:employee_salary_item_create' %}" class="btn btn-primary me-2">
    <i class="fas fa-plus me-1"></i>
    إضافة بند راتب لموظف
</a>
<a href="{% url 'Hr:employee_salary_item_bulk_create' %}" class="btn btn-secondary">
    <i class="fas fa-users me-1"></i>
    إضافة بالجملة
</a>
{% endif %}
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- Search and Filter Card -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light py-3">
                <h5 class="mb-0">البحث والتصفية</h5>
            </div>
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">اسم الموظف</label>
                        <input type="text" name="employee_name" class="form-control" id="searchInput">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">نوع البند</label>
                        <select name="item_type" class="form-select">
                            <option value="">الكل</option>
                            <option value="addition">إضافة</option>
                            <option value="deduction">خصم</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">القسم</label>
                        <select name="department" class="form-select">
                            <option value="">الكل</option>
                            {% for dept in departments %}
                            <option value="{{ dept.id }}">{{ dept.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </form>
            </div>
        </div>

        <!-- Employee Salary Items -->
        {% if employees_salary_items %}
            {% for emp_id, data in employees_salary_items.items %}
            <div class="card shadow-sm mb-4 employee-card">
                <div class="card-header bg-light py-3 d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>
                        {{ data.employee.emp_full_name }}
                        <small class="text-muted">({{ data.employee.emp_id }})</small>
                    </h5>
                    <span class="badge bg-primary">إجمالي: {{ data.total }}</span>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>البند</th>
                                    <th>النوع</th>
                                    <th>القيمة</th>
                                    <th>تاريخ البدء</th>
                                    <th>تاريخ الانتهاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in data.items %}
                                <tr>
                                    <td>{{ item.salary_item.name }}</td>
                                    <td>
                                        {% if item.salary_item.type == 'addition' %}
                                        <span class="badge bg-success">إضافة</span>
                                        {% else %}
                                        <span class="badge bg-danger">خصم</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ item.amount }}</td>
                                    <td>{{ item.start_date|date:"Y-m-d" }}</td>
                                    <td>{{ item.end_date|date:"Y-m-d"|default:"-" }}</td>
                                    <td>
                                        {% if perms.Hr.change_employeesalaryitem %}
                                        <a href="{% url 'Hr:employee_salary_item_edit' item.pk %}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        {% endif %}
                                        {% if perms.Hr.delete_employeesalaryitem %}
                                        <a href="{% url 'Hr:employee_salary_item_delete' item.pk %}" class="btn btn-sm btn-outline-danger">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            لا توجد بنود رواتب مضافة للموظفين حتى الآن.
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize select2
    $('.form-select').select2({
        theme: 'bootstrap4'
    });

    // Search functionality
    $("#searchInput").on("keyup", function() {
        var value = $(this).val().toLowerCase();
        $(".employee-card").filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
        });
    });

    // Filter by type
    $("select[name='item_type']").change(function() {
        var value = $(this).val().toLowerCase();
        if (value) {
            $(".employee-card tbody tr").filter(function() {
                $(this).toggle($(this).find("td:nth-child(2)").text().toLowerCase().indexOf(value) > -1)
            });
        } else {
            $(".employee-card tbody tr").show();
        }
    });
});
</script>
{% endblock %}