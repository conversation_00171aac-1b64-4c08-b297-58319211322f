# Generated by Django 5.0.14 on 2025-05-07 12:34

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cars', '0002_supplier_alter_car_car_code_alter_car_car_name_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Employee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الموظف')),
                ('job_title', models.CharField(max_length=100, verbose_name='المسمى الوظيفي')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('address', models.TextField(blank=True, null=True, verbose_name='العنوان')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'الموظف',
                'verbose_name_plural': 'الموظفين',
                'ordering': ['name'],
            },
        ),
        migrations.AlterField(
            model_name='car',
            name='car_status',
            field=models.CharField(choices=[('active', 'نشط'), ('inactive', 'غير نشط')], default='active', max_length=20, verbose_name='حالة السيارة'),
        ),
        migrations.AlterField(
            model_name='car',
            name='car_type',
            field=models.CharField(choices=[('microbus', 'ميكروباص'), ('bus', 'اتوبيس'), ('passenger', 'سيارة 7 راكب'), ('private', 'سيارة ملاكي')], max_length=20, verbose_name='نوع السيارة'),
        ),
        migrations.AlterField(
            model_name='car',
            name='fuel_type',
            field=models.CharField(choices=[('diesel', 'جاز'), ('gasoline', 'بنزين'), ('gas', 'غاز')], max_length=20, verbose_name='نوع الوقود'),
        ),
        migrations.CreateModel(
            name='RoutePoint',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('point_name', models.CharField(max_length=100, verbose_name='اسم النقطة')),
                ('departure_time', models.TimeField(verbose_name='وقت المغادرة')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='الترتيب')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('car', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='route_points', to='cars.car', verbose_name='السيارة')),
                ('employees', models.ManyToManyField(blank=True, related_name='route_points', to='cars.employee', verbose_name='الموظفين')),
            ],
            options={
                'verbose_name': 'نقطة خط السير',
                'verbose_name_plural': 'نقاط خط السير',
                'ordering': ['car', 'order'],
                'unique_together': {('car', 'order')},
            },
        ),
    ]
