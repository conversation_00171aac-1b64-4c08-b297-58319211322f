{% extends 'Hr/base_hr.html' %}
{% load static %}

{% block title %}{{ page_title }} - نظام الدولية{% endblock %}

{% block page_title %}{{ page_title }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموظفين</a></li>
<li class="breadcrumb-item active">{{ page_title }}</li>
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">فترات الرواتب</h5>
        <a href="{% url 'employees:payroll_period_create' %}" class="btn btn-primary btn-sm">
            <i class="fas fa-plus"></i> إضافة فترة جديدة
        </a>
    </div>
    <div class="card-body">
        {% if payroll_periods %}
        <div class="table-responsive">
            <table class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>الاسم</th>
                        <th>تاريخ البداية</th>
                        <th>تاريخ النهاية</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for period in payroll_periods %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ period.name }}</td>
                        <td>{{ period.start_date|date:"Y-m-d" }}</td>
                        <td>{{ period.end_date|date:"Y-m-d" }}</td>
                        <td>
                            {% if period.status == 'draft' %}
                            <span class="badge bg-secondary">قيد الإعداد</span>
                            {% elif period.status == 'calculated' %}
                            <span class="badge bg-info">تم الاحتساب</span>
                            {% elif period.status == 'approved' %}
                            <span class="badge bg-success">تم الاعتماد</span>
                            {% elif period.status == 'paid' %}
                            <span class="badge bg-primary">تم الدفع</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'employees:payroll_entry_list' %}?payroll_period={{ period.id }}" class="btn btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'employees:payroll_period_edit' period.id %}" class="btn btn-primary">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'employees:payroll_period_delete' period.id %}" class="btn btn-danger">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            لا توجد فترات رواتب مسجلة حتى الآن.
        </div>
        <div class="text-center mt-3">
            <a href="{% url 'employees:payroll_period_create' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة فترة جديدة
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
