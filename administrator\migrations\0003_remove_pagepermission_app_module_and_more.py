# Generated by Django 5.0.14 on 2025-05-19 20:35

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('administrator', '0002_permissionauditlog_error_message_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='pagepermission',
            name='app_module',
        ),
        migrations.RemoveField(
            model_name='operationpermission',
            name='app_module',
        ),
        migrations.RemoveField(
            model_name='groupprofile',
            name='group',
        ),
        migrations.AlterUniqueTogether(
            name='operationpermission',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='useroperationpermission',
            name='operation',
        ),
        migrations.AlterUniqueTogether(
            name='pagepermission',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='userpagepermission',
            name='page',
        ),
        migrations.AlterUniqueTogether(
            name='permission',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='permission',
            name='groups',
        ),
        migrations.RemoveField(
            model_name='permission',
            name='module',
        ),
        migrations.RemoveField(
            model_name='permission',
            name='users',
        ),
        migrations.RemoveField(
            model_name='permissionauditlog',
            name='content_type',
        ),
        migrations.RemoveField(
            model_name='permissionauditlog',
            name='user',
        ),
        migrations.RemoveField(
            model_name='permissiongroup',
            name='permissions',
        ),
        migrations.RemoveField(
            model_name='templatepermission',
            name='groups',
        ),
        migrations.RemoveField(
            model_name='templatepermission',
            name='users',
        ),
        migrations.AlterUniqueTogether(
            name='userdepartmentpermission',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='userdepartmentpermission',
            name='department',
        ),
        migrations.RemoveField(
            model_name='userdepartmentpermission',
            name='user',
        ),
        migrations.AlterUniqueTogether(
            name='usergroup',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='usergroup',
            name='group',
        ),
        migrations.RemoveField(
            model_name='usergroup',
            name='user',
        ),
        migrations.AlterUniqueTogether(
            name='usermodulepermission',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='usermodulepermission',
            name='module',
        ),
        migrations.RemoveField(
            model_name='usermodulepermission',
            name='user',
        ),
        migrations.AlterUniqueTogether(
            name='useroperationpermission',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='useroperationpermission',
            name='user',
        ),
        migrations.AlterUniqueTogether(
            name='userpagepermission',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='userpagepermission',
            name='user',
        ),
        migrations.AlterField(
            model_name='module',
            name='url',
            field=models.CharField(max_length=200, verbose_name='رابط الوحدة'),
        ),
        migrations.AlterField(
            model_name='systemsettings',
            name='company_logo',
            field=models.ImageField(blank=True, default=django.utils.timezone.now, upload_to='company_logos/', verbose_name='شعار الشركة'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='systemsettings',
            name='company_name',
            field=models.CharField(default='الشركة الدولية', max_length=255, verbose_name='اسم الشركة'),
        ),
        migrations.AlterField(
            model_name='systemsettings',
            name='company_phone',
            field=models.CharField(blank=True, max_length=20, verbose_name='هاتف الشركة'),
        ),
        migrations.AlterField(
            model_name='systemsettings',
            name='company_website',
            field=models.URLField(blank=True, verbose_name='موقع الشركة'),
        ),
        migrations.AlterField(
            model_name='systemsettings',
            name='db_host',
            field=models.CharField(default='localhost', max_length=255, verbose_name='مضيف قاعدة البيانات'),
        ),
        migrations.AlterField(
            model_name='systemsettings',
            name='db_name',
            field=models.CharField(default='eldawliya_db', max_length=255, verbose_name='اسم قاعدة البيانات'),
        ),
        migrations.AlterField(
            model_name='systemsettings',
            name='db_password',
            field=models.CharField(blank=True, max_length=255, verbose_name='كلمة مرور قاعدة البيانات'),
        ),
        migrations.AlterField(
            model_name='systemsettings',
            name='db_port',
            field=models.CharField(default='3306', max_length=10, verbose_name='منفذ قاعدة البيانات'),
        ),
        migrations.AlterField(
            model_name='systemsettings',
            name='db_user',
            field=models.CharField(default='root', max_length=255, verbose_name='مستخدم قاعدة البيانات'),
        ),
        migrations.AlterField(
            model_name='systemsettings',
            name='last_modified',
            field=models.DateTimeField(auto_now=True, verbose_name='آخر تحديث'),
        ),
        migrations.DeleteModel(
            name='AppModule',
        ),
        migrations.DeleteModel(
            name='GroupProfile',
        ),
        migrations.DeleteModel(
            name='OperationPermission',
        ),
        migrations.DeleteModel(
            name='PagePermission',
        ),
        migrations.DeleteModel(
            name='Permission',
        ),
        migrations.DeleteModel(
            name='PermissionAuditLog',
        ),
        migrations.DeleteModel(
            name='PermissionGroup',
        ),
        migrations.DeleteModel(
            name='TemplatePermission',
        ),
        migrations.DeleteModel(
            name='UserDepartmentPermission',
        ),
        migrations.DeleteModel(
            name='UserGroup',
        ),
        migrations.DeleteModel(
            name='UserModulePermission',
        ),
        migrations.DeleteModel(
            name='UserOperationPermission',
        ),
        migrations.DeleteModel(
            name='UserPagePermission',
        ),
    ]
