# Generated by Django 5.0.14 on 2025-05-07 05:28

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('Hr', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='employeeevaluation',
            name='evaluator',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='conducted_evaluations', to=settings.AUTH_USER_MODEL, verbose_name='المقيم'),
        ),
        migrations.AddField(
            model_name='employeefile',
            name='employee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='files', to='Hr.employee', verbose_name='الموظف'),
        ),
        migrations.AddField(
            model_name='employeefile',
            name='uploaded_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='uploaded_files', to=settings.AUTH_USER_MODEL, verbose_name='تم الرفع بواسطة'),
        ),
        migrations.AddField(
            model_name='employeeleave',
            name='approved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_leaves', to=settings.AUTH_USER_MODEL, verbose_name='تمت الموافقة بواسطة'),
        ),
        migrations.AddField(
            model_name='employeeleave',
            name='employee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leaves', to='Hr.employee', verbose_name='الموظف'),
        ),
        migrations.AddField(
            model_name='employeenote',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='employee_notes', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة'),
        ),
        migrations.AddField(
            model_name='employeenote',
            name='employee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notes', to='Hr.employee', verbose_name='الموظف'),
        ),
        migrations.AddField(
            model_name='employeesalaryitem',
            name='employee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='salary_items', to='Hr.employee', verbose_name='الموظف'),
        ),
        migrations.AddField(
            model_name='employeetask',
            name='assigned_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='hr_assigned_tasks', to=settings.AUTH_USER_MODEL, verbose_name='تم التكليف بواسطة'),
        ),
        migrations.AddField(
            model_name='employeetask',
            name='employee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tasks', to='Hr.employee', verbose_name='الموظف'),
        ),
        migrations.AddField(
            model_name='hrtask',
            name='assigned_to',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='hr_tasks', to=settings.AUTH_USER_MODEL, verbose_name='تم التكليف إلى'),
        ),
        migrations.AddField(
            model_name='hrtask',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_hr_tasks', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة'),
        ),
        migrations.AddField(
            model_name='job',
            name='department',
            field=models.ForeignKey(blank=True, db_column='Dept_Code', null=True, on_delete=django.db.models.deletion.SET_NULL, to='Hr.department', verbose_name='القسم'),
        ),
        migrations.AddField(
            model_name='employeeleave',
            name='leave_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='employee_leaves', to='Hr.leavetype', verbose_name='نوع الإجازة'),
        ),
        migrations.AlterUniqueTogether(
            name='officialholiday',
            unique_together={('name', 'date')},
        ),
        migrations.AddField(
            model_name='payrollentry',
            name='employee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payroll_entries', to='Hr.employee', verbose_name='الموظف'),
        ),
        migrations.AddField(
            model_name='payrollitemdetail',
            name='payroll_entry',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='item_details', to='Hr.payrollentry', verbose_name='سجل الراتب'),
        ),
        migrations.AddField(
            model_name='payrollentry',
            name='payroll_period',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='entries', to='Hr.payrollperiod', verbose_name='فترة الراتب'),
        ),
        migrations.AddField(
            model_name='pickuppoint',
            name='car',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='pickup_points', to='Hr.car', verbose_name='السيارة'),
        ),
        migrations.AddField(
            model_name='payrollitemdetail',
            name='salary_item',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Hr.salaryitem', verbose_name='بند الراتب'),
        ),
        migrations.AddField(
            model_name='employeesalaryitem',
            name='salary_item',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='employee_items', to='Hr.salaryitem', verbose_name='بند الراتب'),
        ),
        migrations.AddField(
            model_name='taskstep',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_task_steps', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة'),
        ),
        migrations.AlterUniqueTogether(
            name='attendancesummary',
            unique_together={('employee', 'date')},
        ),
    ]
