{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ page_title }} - نظام الدولية{% endblock %}

{% block page_title %}{{ page_title }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموظفين</a></li>
<li class="breadcrumb-item active">{{ page_title }}</li>
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">سجلات الحضور والانصراف</h5>
        <div>
            <a href="{% url 'Hr:attendance:attendance_record_export' %}?{{ request.GET.urlencode }}" class="btn btn-success">
                <i class="fas fa-file-excel"></i> تصدير إلى Excel
            </a>
        </div>
    </div>
    <div class="card-body">
        <!-- Filter Form -->
        <form method="get" class="mb-4">
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="employee" class="form-label">الموظف</label>
                        <select name="employee" id="employee" class="form-select">
                            <option value="">-- جميع الموظفين --</option>
                            {% for emp in employees %}
                            <option value="{{ emp.id }}" {% if selected_employee|stringformat:"s" == emp.id|stringformat:"s" %}selected{% endif %}>
                                {{ emp.emp_full_name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="start_date" class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" 
                            value="{{ request.GET.start_date }}">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="end_date" class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" 
                            value="{{ request.GET.end_date }}">
                    </div>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <div class="mb-3 w-100">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search"></i> بحث
                        </button>
                    </div>
                </div>
            </div>
            {% if request.GET %}
            <div class="row">
                <div class="col-12">
                    <a href="{% url 'Hr:attendance:attendance_record_list' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> إعادة تعيين
                    </a>
                </div>
            </div>
            {% endif %}
        </form>

        {% if records %}
        <div class="table-responsive">
            <table class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>الموظف</th>
                        <th>التاريخ</th>
                        <th>وقت الحضور</th>
                        <th>حالة الحضور</th>
                        <th>وقت الانصراف</th>
                        <th>حالة الانصراف</th>
                        <th>مدة العمل</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for record in records %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ record.employee.emp_full_name }}</td>
                        <td>{{ record.date }}</td>
                        <td>{{ record.check_in_time|default:"-" }}</td>
                        <td>
                            {% if record.check_in_status == 'on_time' %}
                            <span class="badge bg-success">في الموعد</span>
                            {% elif record.check_in_status == 'late' %}
                            <span class="badge bg-warning">متأخر</span>
                            {% elif record.check_in_status == 'absent' %}
                            <span class="badge bg-danger">غائب</span>
                            {% endif %}
                        </td>
                        <td>{{ record.check_out_time|default:"-" }}</td>
                        <td>
                            {% if record.check_out_status == 'on_time' %}
                            <span class="badge bg-success">في الموعد</span>
                            {% elif record.check_out_status == 'early' %}
                            <span class="badge bg-warning">مغادرة مبكرة</span>
                            {% elif record.check_out_status == 'missing' %}
                            <span class="badge bg-danger">لم يسجل</span>
                            {% endif %}
                        </td>
                        <td>{{ record.work_duration|default:"-" }}</td>
                        <td>{{ record.notes|default:"-" }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Summary Section -->
        <div class="mt-4">
            <h6>ملخص الفترة:</h6>
            <div class="row">
                <div class="col-md-3">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6 class="card-title">إجمالي أيام العمل</h6>
                            <p class="card-text h4">{{ summary.total_days }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <h6 class="card-title">أيام الحضور في الموعد</h6>
                            <p class="card-text h4">{{ summary.on_time_days }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning">
                        <div class="card-body">
                            <h6 class="card-title">أيام التأخير</h6>
                            <p class="card-text h4">{{ summary.late_days }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <h6 class="card-title">أيام الغياب</h6>
                            <p class="card-text h4">{{ summary.absent_days }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            لا توجد سجلات حضور وانصراف للفترة المحددة.
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Initialize select2 for employee dropdown
        $('#employee').select2({
            theme: 'bootstrap-5',
            language: "ar",
            dir: "rtl",
            placeholder: "اختر الموظف"
        });
    });
</script>
{% endblock %}
