{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ page_title }} - نظام الدولية{% endblock %}

{% block page_title %}{{ page_title }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموظفين</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:attendance:employee_attendance_rule_list' %}">قواعد حضور الموظفين</a></li>
<li class="breadcrumb-item active">{{ page_title }}</li>
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">إضافة قواعد حضور للموظفين بالجملة</h5>
    </div>
    <div class="card-body">
        <form method="post" novalidate>
            {% csrf_token %}

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.attendance_rule.id_for_label }}" class="form-label">{{ form.attendance_rule.label }}</label>
                        {{ form.attendance_rule }}
                        {% if form.attendance_rule.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.attendance_rule.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                        <small class="form-text text-muted">اختر قاعدة الحضور التي سيتم تطبيقها على الموظفين المحددين</small>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.effective_date.id_for_label }}" class="form-label">{{ form.effective_date.label }}</label>
                        {{ form.effective_date }}
                        {% if form.effective_date.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.effective_date.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.end_date.id_for_label }}" class="form-label">{{ form.end_date.label }}</label>
                        {{ form.end_date }}
                        {% if form.end_date.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.end_date.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                        <small class="form-text text-muted">اتركه فارغاً إذا كنت لا تريد تحديد تاريخ انتهاء</small>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.is_active }}
                            <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                {{ form.is_active.label }}
                            </label>
                        </div>
                        {% if form.is_active.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.is_active.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.employees.id_for_label }}" class="form-label">{{ form.employees.label }}</label>
                        {{ form.employees }}
                        {% if form.employees.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.employees.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                        <small class="form-text text-muted">اختر الموظفين الذين تريد تطبيق قاعدة الحضور عليهم</small>
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-between">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ
                </button>
                <a href="{% url 'Hr:attendance:employee_attendance_rule_list' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Initialize select2 for attendance rule and employees dropdowns
        $('#{{ form.attendance_rule.id_for_label }}').select2({
            theme: 'bootstrap-5',
            language: "ar",
            dir: "rtl"
        });
        
        $('#{{ form.employees.id_for_label }}').select2({
            theme: 'bootstrap-5',
            language: "ar",
            dir: "rtl",
            placeholder: "اختر الموظفين"
        });
    });
</script>
{% endblock %}