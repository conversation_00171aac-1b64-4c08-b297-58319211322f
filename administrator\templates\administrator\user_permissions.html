{% extends 'administrator/base_admin.html' %}
{% load static %}

{% block title %}إدارة صلاحيات المستخدم - لوحة تحكم مدير النظام{% endblock %}

{% block page_icon %}key{% endblock %}
{% block page_header %}إدارة صلاحيات المستخدم {{ user_obj.username }}{% endblock %}

{% block extra_css %}
<style>
    .permissions-container {
        max-height: 600px;
        overflow-y: auto;
        border-radius: 0.5rem;
        border: 1px solid #dee2e6;
    }
    
    .permission-group {
        margin-bottom: 1.5rem;
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 0.5rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }
    
    .permission-group-header {
        background-color: #f8f9fa;
        padding: 0.7rem 1rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
    }
    
    .permission-group-content {
        padding: 1rem;
    }
    
    .permission-checkbox {
        margin-bottom: 0.5rem;
        padding: 0.25rem;
        border-radius: 0.25rem;
    }
    
    .permission-checkbox:hover {
        background-color: rgba(0, 0, 0, 0.03);
    }
    
    .search-box {
        position: sticky;
        top: 0;
        z-index: 100;
        background-color: white;
        padding: 1rem;
        margin-bottom: 1rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }
    
    .type-view { border-right: 3px solid #28a745; }
    .type-add { border-right: 3px solid #007bff; }
    .type-change { border-right: 3px solid #fd7e14; }
    .type-delete { border-right: 3px solid #dc3545; }
    
    .permission-section {
        margin-bottom: 1rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px dashed #e9ecef;
    }
    
    .action-btn {
        transition: all 0.2s;
    }
    
    .action-btn:hover {
        transform: translateY(-1px);
    }
    
    /* Category Cards Styling */
    .category-cards {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-top: 1rem;
    }
    
    .category-card {
        flex: 1 1 300px;
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 0.5rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        margin-bottom: 1rem;
        overflow: hidden;
        transition: all 0.3s ease;
    }
    
    .category-card:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    
    .category-header {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        color: white;
        font-weight: 600;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .category-body {
        padding: 1rem;
        max-height: 350px;
        overflow-y: auto;
    }
    
    .category-search-box {
        padding: 0.5rem;
        background-color: rgba(0, 0, 0, 0.03);
        border-radius: 0.25rem;
        margin-bottom: 1rem;
    }
    
    .loading-spinner {
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.7);
        z-index: 1000;
    }
    
    .permission-badge {
        margin-right: 0.25rem;
        margin-bottom: 0.25rem;
    }
    
    /* View toggle buttons */
    .view-toggle-buttons {
        margin-bottom: 1rem;
    }
    
    .cards-view {
        display: none;
    }
    
    .cards-view.active {
        display: block;
    }
    
    .standard-view.active {
        display: block;
    }
    
    .standard-view {
        display: none;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Toggle between standard view and cards view
        $("#standardViewBtn").click(function() {
            $(".standard-view").addClass("active");
            $(".cards-view").removeClass("active");
            $(this).addClass("active");
            $("#cardsViewBtn").removeClass("active");
        });

        $("#cardsViewBtn").click(function() {
            $(".cards-view").addClass("active");
            $(".standard-view").removeClass("active");
            $(this).addClass("active");
            $("#standardViewBtn").removeClass("active");
        });

        // Refresh permissions button functionality
        $(".refresh-permissions-btn").click(function() {
            // Show loading spinner
            $(".loading-spinner").removeClass("d-none");
            
            // Reload the page to refresh the permissions list
            location.reload();
        });

        // Permission search functionality
        $("#permissionSearch").on("keyup", function() {
            var value = $(this).val().toLowerCase();
            $(".permission-checkbox").each(function() {
                var text = $(this).text().toLowerCase();
                $(this).toggle(text.indexOf(value) > -1);
            });
        });

        // Clear search button
        $("#clearSearchBtn").click(function() {
            $("#permissionSearch").val("");
            $(".permission-checkbox").show();
        });

        // Category-specific search
        $(".category-search").on("keyup", function() {
            var value = $(this).val().toLowerCase();
            var container = $(this).closest(".category-body").find(".card-checkbox");
            
            container.each(function() {
                var text = $(this).text().toLowerCase();
                $(this).toggle(text.indexOf(value) > -1);
            });
        });

        // Select all permissions in a group
        $(".select-all").click(function() {
            var group = $(this).data("group");
            $("#group-" + group).find("input[type='checkbox']").prop("checked", true);
        });

        // Deselect all permissions in a group
        $(".deselect-all").click(function() {
            var group = $(this).data("group");
            $("#group-" + group).find("input[type='checkbox']").prop("checked", false);
        });

        // Select all permissions in a card
        $(".select-card-all").click(function() {
            var app = $(this).data("app");
            $(this).closest(".category-card").find("input[type='checkbox']").prop("checked", true);
        });

        // Deselect all permissions in a card
        $(".deselect-card-all").click(function() {
            var app = $(this).data("app");
            $(this).closest(".category-card").find("input[type='checkbox']").prop("checked", false);
        });

        // Permission templates
        $(".template-btn").click(function() {
            var template = $(this).data("template");
            $(".loading-spinner").removeClass("d-none");
            
            // Uncheck all checkboxes first
            $("input[type='checkbox']").prop("checked", false);
            
            // Apply template based on selection
            switch(template) {
                case "admin":
                    // Check all permissions
                    $("input[type='checkbox']").prop("checked", true);
                    break;
                case "hr":
                    // Check HR-related permissions
                    $("input[type='checkbox']").each(function() {
                        var label = $(this).parent().text().toLowerCase();
                        if (label.includes("employee") || 
                            label.includes("user") || 
                            label.includes("hr") || 
                            label.includes("attendance")) {
                            $(this).prop("checked", true);
                        }
                    });
                    break;
                case "inventory":
                    // Check inventory-related permissions
                    $("input[type='checkbox']").each(function() {
                        var label = $(this).parent().text().toLowerCase();
                        if (label.includes("product") || 
                            label.includes("inventory") || 
                            label.includes("stock") || 
                            label.includes("supplier")) {
                            $(this).prop("checked", true);
                        }
                    });
                    break;
                case "reports":
                    // Check report-related permissions
                    $("input[type='checkbox']").each(function() {
                        var label = $(this).parent().text().toLowerCase();
                        if (label.includes("report") || 
                            label.includes("view") || 
                            label.includes("export")) {
                            $(this).prop("checked", true);
                        }
                    });
                    break;
            }
            
            // Hide spinner after template applied
            setTimeout(function() {
                $(".loading-spinner").addClass("d-none");
            }, 500);
        });

        // Clear all permissions
        $(".clear-all-btn").click(function() {
            $(".loading-spinner").removeClass("d-none");
            $("input[type='checkbox']").prop("checked", false);
            
            // Hide spinner after clearing
            setTimeout(function() {
                $(".loading-spinner").addClass("d-none");
            }, 300);
        });
    });
</script>
{% endblock %}
{% block content %}
<div class="row">
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-key me-2"></i>
                    إدارة صلاحيات المستخدم المباشرة
                </h5>
                <div class="btn-group">
                    <a href="{% url 'administrator:user_groups' user_obj.id %}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-users me-1"></i>
                        إدارة المجموعات
                    </a>
                    <a href="{% url 'administrator:user_list' %}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-arrow-right me-1"></i>
                        العودة
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <!-- User info card -->
                    <div class="col-md-6 mb-3 mb-md-0">
                        <div class="card shadow-sm h-100">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-user me-2"></i>معلومات المستخدم</h6>
                            </div>
                            <div class="card-body">
                                <div class="row mb-2">
                                    <div class="col-md-4 text-muted">اسم المستخدم:</div>
                                    <div class="col-md-8 fw-bold">{{ user_obj.username }}</div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-md-4 text-muted">الاسم الكامل:</div>
                                    <div class="col-md-8">
                                        {% if user_obj.first_name or user_obj.last_name %}
                                        {{ user_obj.first_name }} {{ user_obj.last_name }}
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-md-4 text-muted">البريد الإلكتروني:</div>
                                    <div class="col-md-8">
                                        {% if user_obj.email %}
                                        {{ user_obj.email }}
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4 text-muted">المجموعات:</div>
                                    <div class="col-md-8">
                                        {% for group in user_obj.groups.all %}
                                        <span class="badge bg-primary me-1">{{ group.name }}</span>
                                        {% empty %}
                                        <span class="text-muted">لا ينتمي إلى أي مجموعة</span>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Permission tools -->
                    <div class="col-md-6">
                        <div class="card shadow-sm h-100">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0"><i class="fas fa-tools me-2"></i>أدوات الصلاحيات</h6>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info mb-3">
                                    <h6 class="alert-heading mb-2"><i class="fas fa-info-circle me-2"></i>معلومات هامة</h6>
                                    <ul class="mb-0">
                                        <li>الصلاحيات المباشرة تطبق بشكل منفصل عن صلاحيات المجموعات.</li>
                                        <li>المستخدم يحصل على مجموع الصلاحيات المباشرة وصلاحيات المجموعات.</li>
                                        <li>استخدم المجموعات لإعطاء صلاحيات للعديد من المستخدمين.</li>
                                    </ul>
                                </div>
                                
                                <div class="bg-light p-3 rounded">
                                    <h6 class="mb-2">قوالب الصلاحيات الجاهزة:</h6>
                                    <div class="btn-group mb-2 w-100">
                                        <button type="button" class="btn btn-sm btn-outline-primary template-btn" data-template="admin">مدير</button>
                                        <button type="button" class="btn btn-sm btn-outline-primary template-btn" data-template="hr">موارد بشرية</button>
                                        <button type="button" class="btn btn-sm btn-outline-primary template-btn" data-template="inventory">مخازن</button>
                                        <button type="button" class="btn btn-sm btn-outline-primary template-btn" data-template="reports">تقارير</button>
                                    </div>
                                    <div class="d-flex gap-2 mb-2">
                                        <button type="button" class="btn btn-sm btn-outline-success refresh-permissions-btn w-100">
                                            <i class="fas fa-sync-alt me-1"></i> تحديث قائمة الصلاحيات
                                        </button>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-danger clear-all-btn w-100">
                                        <i class="fas fa-trash-alt me-1"></i> إزالة جميع الصلاحيات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="permissions-container position-relative">
                                <!-- Loading Spinner (initially hidden) -->
                                <div class="loading-spinner d-none">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">جاري التحميل...</span>
                                    </div>
                                </div>
                                
                                <!-- Enhanced Search Box -->
                                <div class="search-box">
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                                        <input type="text" id="permissionSearch" class="form-control" placeholder="ابحث عن صلاحية...">
                                        <button class="btn btn-outline-secondary" type="button" id="clearSearchBtn">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                    <div class="form-text text-muted mt-1">
                                        <i class="fas fa-info-circle me-1"></i>
                                        اكتب للبحث في الصلاحيات المتاحة
                                    </div>
                                </div>
                                
                                <!-- View Toggle Buttons -->
                                <div class="view-toggle-buttons d-flex justify-content-center mb-3">
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-outline-primary active" id="standardViewBtn">
                                            <i class="fas fa-list me-1"></i> عرض قياسي
                                        </button>
                                        <button type="button" class="btn btn-outline-primary" id="cardsViewBtn">
                                            <i class="fas fa-th-large me-1"></i> عرض البطاقات
                                        </button>
                                    </div>
                                </div>
                                
                                {{ form.permissions.errors }}
                                
                                <!-- Standard View (Default) -->
                                <div class="standard-view active">
                                    <div class="permissions-list px-3 py-2">
                                        {% regroup form.permissions|dictsort:"choice_label" by choice_label.instance.content_type.app_label as app_list %}
                                        
                                        {% for app, perm_list in app_list %}
                                        <div class="permission-group">
                                            <div class="permission-group-header" data-bs-toggle="collapse" data-bs-target="#group-{{ app }}">
                                                <h6 class="mb-0 d-flex align-items-center">
                                                    <i class="fas fa-angle-down me-2"></i>
                                                    {{ app }}
                                                    <span class="badge bg-secondary ms-2">{{ perm_list|length }}</span>
                                                </h6>
                                                <div class="d-flex gap-2">
                                                    <button type="button" class="btn btn-sm btn-outline-primary select-all" data-group="{{ app }}">
                                                        <i class="fas fa-check-square"></i> الكل
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-secondary deselect-all" data-group="{{ app }}">
                                                        <i class="fas fa-square"></i> لا شيء
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="permission-group-content collapse show" id="group-{{ app }}">
                                                <!-- Group by permission type -->
                                                {% regroup perm_list by choice_label.instance.codename|slice:":4" as perm_type_list %}
                                                
                                                {% for perm_type, checkboxes in perm_type_list %}
                                                <div class="permission-section">
                                                    <div class="permission-section-title">
                                                        {% if "view" in perm_type %}
                                                        <span class="badge bg-success"><i class="fas fa-eye me-1"></i> عرض</span>
                                                        {% elif "add_" in perm_type %}
                                                        <span class="badge bg-primary"><i class="fas fa-plus me-1"></i> إضافة</span>
                                                        {% elif "chan" in perm_type %}
                                                        <span class="badge bg-warning"><i class="fas fa-edit me-1"></i> تعديل</span>
                                                        {% elif "dele" in perm_type %}
                                                        <span class="badge bg-danger"><i class="fas fa-trash-alt me-1"></i> حذف</span>
                                                        {% else %}
                                                        <span class="badge bg-secondary">{{ perm_type }}</span>
                                                        {% endif %}
                                                    </div>
                                                    
                                    <div class="permission-items">
                                        {% for checkbox in checkboxes %}
<div class="permission-checkbox {% if 'view' in checkbox.choice_label.instance.codename %}type-view{% elif 'add_' in checkbox.choice_label.instance.codename %}type-add{% elif 'change' in checkbox.choice_label.instance.codename %}type-change{% elif 'delete' in checkbox.choice_label.instance.codename %}type-delete{% endif %}">
    {{ checkbox }}
    <label for="{{ checkbox.id_for_label }}" class="ms-2">
        {{ checkbox.choice_label.instance.name|title }}
    </label>
</div>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                
                <!-- Cards View -->
                <div class="cards-view">
                    <div class="category-cards">
                        {% regroup form.permissions|dictsort:"choice_label" by choice_label.instance.content_type.app_label as app_list %}
                        
                        {% for app, perm_list in app_list %}
                        <div class="category-card">
                            <div class="category-header bg-primary">
                                <span>{{ app }}</span>
                                <span class="badge bg-light text-dark">{{ perm_list|length }}</span>
                            </div>
                            <div class="category-body">
                                <div class="category-search-box mb-3">
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                                        <input type="text" class="form-control category-search" placeholder="بحث...">
                                    </div>
                                </div>
                                
                                <div class="d-flex mb-2">
                                    <button type="button" class="btn btn-sm btn-outline-primary me-2 select-card-all" data-app="{{ app }}">
                                        <i class="fas fa-check-square"></i> الكل
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary deselect-card-all" data-app="{{ app }}">
                                        <i class="fas fa-square"></i> لا شيء
                                    </button>
                                </div>
                                
                                <div class="card-checkbox-container">
                                    {% for checkbox in perm_list %}
                                    <div class="permission-checkbox card-checkbox {% if 'view' in checkbox.choice_label.instance.codename %}type-view{% elif 'add_' in checkbox.choice_label.instance.codename %}type-add{% elif 'change' in checkbox.choice_label.instance.codename %}type-change{% elif 'delete' in checkbox.choice_label.instance.codename %}type-delete{% endif %}">
                                        {{ checkbox }}
                                        <label for="{{ checkbox.id_for_label }}" class="ms-2">
                                            {{ checkbox.choice_label.instance.name|title }}
                                        </label>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <a href="{% url 'administrator:user_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-1"></i>
                        العودة للقائمة
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>
                        حفظ الصلاحيات
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
</form>

{% endblock %}
