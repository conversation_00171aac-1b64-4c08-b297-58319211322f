# Generated by Django 5.0.14 on 2025-05-19 20:35

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0002_localsystemsettings_compact_tables_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='product',
            options={'permissions': [('view_dashboard', 'Can view inventory dashboard'), ('view_stockreport', 'Can view stock reports'), ('export_stockreport', 'Can export stock reports'), ('view_settings', 'Can view inventory settings'), ('view_department', 'Can view inventory departments'), ('view_purchaserequest', 'Can view purchase requests'), ('view_voucher', 'Can view vouchers')], 'verbose_name': 'صنف', 'verbose_name_plural': 'الأصناف'},
        ),
    ]
