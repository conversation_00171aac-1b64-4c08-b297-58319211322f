{% extends 'Hr/base_hr.html' %}
{% load static %}

{% block title %}تعديل المهمة - {{ task.title }} - نظام الدولية{% endblock %}

{% block page_title %}{{ title }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموارد البشرية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:tasks:list' %}">مهام الموظفين</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:tasks:detail' task.pk %}">{{ task.title|truncatechars:30 }}</a></li>
<li class="breadcrumb-item active">تعديل</li>
{% endblock %}

{% block content %}
<div class="card shadow-sm">
    <div class="card-header bg-white py-3">
        <h5 class="card-title mb-0">
            <i class="fas fa-edit me-2 text-primary"></i>
            تعديل المهمة: {{ task.title }}
        </h5>
    </div>
    <div class="card-body">
        <form method="post" class="needs-validation" novalidate>
            {% csrf_token %}

            <div class="row g-3">
                <!-- عنوان المهمة -->
                <div class="col-md-12 mb-3">
                    <label for="{{ form.title.id_for_label }}" class="form-label">عنوان المهمة <span class="text-danger">*</span></label>
                    {{ form.title }}
                    {% if form.title.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.title.errors %}
                        {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <!-- الموظف -->
                <div class="col-md-6 mb-3">
                    <label for="{{ form.employee.id_for_label }}" class="form-label">الموظف <span class="text-danger">*</span></label>
                    {{ form.employee }}
                    {% if form.employee.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.employee.errors %}
                        {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <!-- الحالة -->
                <div class="col-md-6 mb-3">
                    <label for="{{ form.status.id_for_label }}" class="form-label">الحالة <span class="text-danger">*</span></label>
                    {{ form.status }}
                    {% if form.status.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.status.errors %}
                        {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <!-- الأولوية -->
                <div class="col-md-6 mb-3">
                    <label for="{{ form.priority.id_for_label }}" class="form-label">الأولوية <span class="text-danger">*</span></label>
                    {{ form.priority }}
                    {% if form.priority.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.priority.errors %}
                        {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <!-- نسبة الإنجاز -->
                <div class="col-md-6 mb-3">
                    <label for="{{ form.progress.id_for_label }}" class="form-label">نسبة الإنجاز (%) <span class="text-danger">*</span></label>
                    {{ form.progress }}
                    {% if form.progress.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.progress.errors %}
                        {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <!-- تاريخ البداية -->
                <div class="col-md-6 mb-3">
                    <label for="{{ form.start_date.id_for_label }}" class="form-label">تاريخ البداية <span class="text-danger">*</span></label>
                    {{ form.start_date }}
                    {% if form.start_date.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.start_date.errors %}
                        {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <!-- تاريخ الاستحقاق -->
                <div class="col-md-6 mb-3">
                    <label for="{{ form.due_date.id_for_label }}" class="form-label">تاريخ الاستحقاق <span class="text-danger">*</span></label>
                    {{ form.due_date }}
                    {% if form.due_date.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.due_date.errors %}
                        {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <!-- وصف المهمة -->
                <div class="col-md-12 mb-3">
                    <label for="{{ form.description.id_for_label }}" class="form-label">وصف المهمة <span class="text-danger">*</span></label>
                    {{ form.description }}
                    {% if form.description.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.description.errors %}
                        {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <!-- ملاحظات -->
                <div class="col-md-12 mb-3">
                    <label for="{{ form.notes.id_for_label }}" class="form-label">ملاحظات</label>
                    {{ form.notes }}
                    {% if form.notes.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.notes.errors %}
                        {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>

            <div class="d-flex justify-content-between mt-4">
                <a href="{% url 'Hr:tasks:detail' task.pk %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-1"></i>
                    الرجوع
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i>
                    حفظ التغييرات
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Form validation
        const form = document.querySelector('form.needs-validation');
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });

        // Auto-update progress to 100% when status is completed
        const statusSelect = document.getElementById('{{ form.status.id_for_label }}');
        const progressInput = document.getElementById('{{ form.progress.id_for_label }}');
        
        statusSelect.addEventListener('change', function() {
            if (this.value === 'completed') {
                progressInput.value = 100;
            }
        });
    });
</script>
{% endblock %}
