{% extends 'tasks/base_tasks.html' %}
{% load static %}

{% block title %}المهام المكتملة - نظام الدولية{% endblock %}

{% block page_title %}المهام المكتملة{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'tasks:dashboard' %}">المهام</a></li>
<li class="breadcrumb-item active">المهام المكتملة</li>
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">قائمة المهام المكتملة</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>العنوان</th>
                        <th>تاريخ الإنشاء</th>
                        <th>تاريخ الإكمال</th>
                        <th>المسؤول</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for task in tasks %}
                    <tr>
                        <td>
                            <a href="{% url 'tasks:detail' task.id %}">{{ task.description }}</a>
                        </td>
                        <td>{{ task.created_at|date:"Y-m-d" }}</td>
                        <td>{{ task.updated_at|date:"Y-m-d" }}</td>
                        <td>{{ task.assigned_to.get_full_name|default:task.assigned_to.username }}</td>
                        <td>
                            <a href="{% url 'tasks:detail' task.id %}" class="btn btn-sm btn-info">
                                <i class="fas fa-eye"></i>
                            </a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5" class="text-center py-4">لا توجد مهام مكتملة</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
