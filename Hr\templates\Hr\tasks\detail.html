{% extends 'Hr/base_hr.html' %}
{% load static %}

{% block title %}تفاصيل المهمة - {{ task.title }} - نظام الدولية{% endblock %}

{% block page_title %}{{ title }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموارد البشرية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:tasks:list' %}">مهام الموظفين</a></li>
<li class="breadcrumb-item active">{{ task.title|truncatechars:30 }}</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-white d-flex justify-content-between align-items-center py-3">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tasks me-2 text-primary"></i>
                    تفاصيل المهمة
                </h5>
                <div>
                    <a href="{% url 'Hr:tasks:edit' task.pk %}" class="btn btn-primary btn-sm">
                        <i class="fas fa-edit me-1"></i>
                        تعديل
                    </a>
                    <a href="{% url 'Hr:tasks:delete' task.pk %}" class="btn btn-outline-danger btn-sm">
                        <i class="fas fa-trash-alt me-1"></i>
                        حذف
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-12">
                        <h4 class="mb-3">{{ task.title }}</h4>
                        <div class="d-flex flex-wrap gap-2 mb-3">
                            {% if task.status == 'pending' %}
                            <span class="badge bg-warning">قيد الانتظار</span>
                            {% elif task.status == 'in_progress' %}
                            <span class="badge bg-info">قيد التنفيذ</span>
                            {% elif task.status == 'completed' %}
                            <span class="badge bg-success">مكتملة</span>
                            {% elif task.status == 'cancelled' %}
                            <span class="badge bg-secondary">ملغاة</span>
                            {% endif %}

                            {% if task.priority == 'low' %}
                            <span class="badge bg-success">أولوية منخفضة</span>
                            {% elif task.priority == 'medium' %}
                            <span class="badge bg-info">أولوية متوسطة</span>
                            {% elif task.priority == 'high' %}
                            <span class="badge bg-warning">أولوية عالية</span>
                            {% elif task.priority == 'urgent' %}
                            <span class="badge bg-danger">أولوية عاجلة</span>
                            {% endif %}
                        </div>
                        <p class="text-muted mb-0">
                            <i class="fas fa-user me-1"></i>
                            تم تكليف: <strong>{{ task.employee.emp_name }}</strong>
                        </p>
                        <p class="text-muted mb-0">
                            <i class="fas fa-user-shield me-1"></i>
                            تم التكليف بواسطة: <strong>{{ task.assigned_by.get_full_name|default:task.assigned_by.username }}</strong>
                        </p>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <h6 class="fw-bold">تاريخ البداية</h6>
                            <p>{{ task.start_date|date:"Y-m-d" }}</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <h6 class="fw-bold">تاريخ الاستحقاق</h6>
                            <p>{{ task.due_date|date:"Y-m-d" }}</p>
                        </div>
                    </div>
                    {% if task.completion_date %}
                    <div class="col-md-6">
                        <div class="mb-3">
                            <h6 class="fw-bold">تاريخ الإنجاز</h6>
                            <p>{{ task.completion_date|date:"Y-m-d" }}</p>
                        </div>
                    </div>
                    {% endif %}
                    <div class="col-md-6">
                        <div class="mb-3">
                            <h6 class="fw-bold">نسبة الإنجاز</h6>
                            <div class="progress" style="height: 10px;">
                                <div class="progress-bar {% if task.progress < 30 %}bg-danger{% elif task.progress < 70 %}bg-warning{% else %}bg-success{% endif %}"
                                     role="progressbar"
                                     style="width: {{ task.progress }}%;"
                                     aria-valuenow="{{ task.progress }}"
                                     aria-valuemin="0"
                                     aria-valuemax="100">
                                </div>
                            </div>
                            <small class="text-muted">{{ task.progress }}%</small>
                        </div>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-12">
                        <h6 class="fw-bold">وصف المهمة</h6>
                        <div class="p-3 bg-light rounded">
                            {{ task.description|linebreaks }}
                        </div>
                    </div>
                </div>

                <!-- خطوات المهمة -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="fw-bold mb-0">خطوات المهمة</h6>
                            <span class="badge bg-primary">{{ steps|length }} خطوة</span>
                        </div>

                        {% if steps %}
                        <div class="list-group mb-3">
                            {% for step in steps %}
                            <div class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between align-items-center">
                                    <div class="form-check">
                                        <input class="form-check-input step-checkbox" type="checkbox" value="{{ step.pk }}"
                                               id="step-{{ step.pk }}" {% if step.completed %}checked{% endif %}
                                               data-task-id="{{ task.pk }}" data-step-id="{{ step.pk }}">
                                        <label class="form-check-label {% if step.completed %}text-decoration-line-through text-muted{% endif %}"
                                               for="step-{{ step.pk }}">
                                            {{ step.description }}
                                        </label>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        {% if step.completed %}
                                        <small class="text-muted me-3">
                                            <i class="fas fa-calendar-check me-1"></i>
                                            {{ step.completion_date|date:"Y-m-d" }}
                                        </small>
                                        {% endif %}
                                        <button type="button" class="btn btn-sm btn-outline-danger delete-step"
                                                data-task-id="{{ task.pk }}" data-step-id="{{ step.pk }}"
                                                data-bs-toggle="modal" data-bs-target="#deleteStepModal">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </div>
                                <small class="text-muted d-block mt-1">
                                    <i class="fas fa-user me-1"></i>
                                    {{ step.created_by.get_full_name|default:step.created_by.username }} -
                                    <i class="fas fa-clock me-1"></i>
                                    {{ step.created_at|date:"Y-m-d H:i" }}
                                </small>
                            </div>
                            {% endfor %}
                        </div>
                        {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            لم يتم إضافة أي خطوات لهذه المهمة بعد.
                        </div>
                        {% endif %}

                        <!-- نموذج إضافة خطوة جديدة -->
                        {% if task.status != 'completed' and task.status != 'cancelled' %}
                        <div class="card mt-3">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">إضافة خطوة جديدة</h6>
                            </div>
                            <div class="card-body">
                                <form method="post" id="stepForm">
                                    {% csrf_token %}
                                    <div class="mb-3">
                                        <label for="{{ form.description.id_for_label }}" class="form-label">وصف الخطوة <span class="text-danger">*</span></label>
                                        {{ form.description }}
                                        {% if form.description.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.description.errors %}
                                            {{ error }}
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                    </div>
                                    <div class="mb-3 form-check">
                                        {{ form.completed }}
                                        <label class="form-check-label" for="{{ form.completed.id_for_label }}">
                                            مكتملة
                                        </label>
                                    </div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-plus-circle me-1"></i>
                                        إضافة الخطوة
                                    </button>
                                </form>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>

                {% if task.notes %}
                <div class="row">
                    <div class="col-md-12">
                        <h6 class="fw-bold">ملاحظات</h6>
                        <div class="p-3 bg-light rounded">
                            {{ task.notes|linebreaks }}
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
            <div class="card-footer bg-white">
                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        تم الإنشاء: {{ task.created_at|date:"Y-m-d H:i" }}
                    </small>
                    <small class="text-muted">
                        <i class="fas fa-edit me-1"></i>
                        آخر تحديث: {{ task.updated_at|date:"Y-m-d H:i" }}
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-white py-3">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2 text-primary"></i>
                    معلومات المهمة
                </h5>
            </div>
            <div class="card-body">
                {% if task.status != 'completed' and task.status != 'cancelled' %}
                    {% if task.due_date < now.date %}
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>متأخرة!</strong> تجاوزت هذه المهمة تاريخ الاستحقاق.
                    </div>
                    {% elif task.due_date == now.date %}
                    <div class="alert alert-warning">
                        <i class="fas fa-clock me-2"></i>
                        <strong>اليوم!</strong> تاريخ استحقاق هذه المهمة هو اليوم.
                    </div>
                    {% endif %}
                {% endif %}

                {% if task.status == 'completed' %}
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>مكتملة!</strong> تم إنجاز هذه المهمة بنجاح.
                </div>
                {% elif task.status == 'cancelled' %}
                <div class="alert alert-secondary">
                    <i class="fas fa-ban me-2"></i>
                    <strong>ملغاة!</strong> تم إلغاء هذه المهمة.
                </div>
                {% endif %}

                <div class="list-group">
                    <div class="list-group-item">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">الحالة</h6>
                            {% if task.status == 'pending' %}
                            <span class="badge bg-warning">قيد الانتظار</span>
                            {% elif task.status == 'in_progress' %}
                            <span class="badge bg-info">قيد التنفيذ</span>
                            {% elif task.status == 'completed' %}
                            <span class="badge bg-success">مكتملة</span>
                            {% elif task.status == 'cancelled' %}
                            <span class="badge bg-secondary">ملغاة</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="list-group-item">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">الأولوية</h6>
                            {% if task.priority == 'low' %}
                            <span class="badge bg-success">منخفضة</span>
                            {% elif task.priority == 'medium' %}
                            <span class="badge bg-info">متوسطة</span>
                            {% elif task.priority == 'high' %}
                            <span class="badge bg-warning">عالية</span>
                            {% elif task.priority == 'urgent' %}
                            <span class="badge bg-danger">عاجلة</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="list-group-item">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">الموظف</h6>
                            <span>{{ task.employee.emp_name }}</span>
                        </div>
                    </div>
                    <div class="list-group-item">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">تم التكليف بواسطة</h6>
                            <span>{{ task.assigned_by.get_full_name|default:task.assigned_by.username }}</span>
                        </div>
                    </div>
                </div>

                {% if task.status != 'completed' and task.status != 'cancelled' %}
                <div class="mt-4">
                    <a href="{% url 'Hr:tasks:edit' task.pk %}" class="btn btn-primary w-100">
                        <i class="fas fa-edit me-1"></i>
                        تحديث حالة المهمة
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Modal for Delete Confirmation -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف المهمة "<span id="itemName">{{ task.title }}</span>"؟</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تحذير: لا يمكن التراجع عن هذا الإجراء.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <a href="{% url 'Hr:tasks:delete' task.pk %}" id="confirmDelete" class="btn btn-danger">تأكيد الحذف</a>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Delete Step Confirmation -->
<div class="modal fade" id="deleteStepModal" tabindex="-1" aria-labelledby="deleteStepModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteStepModalLabel">تأكيد حذف الخطوة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف هذه الخطوة؟</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تحذير: لا يمكن التراجع عن هذا الإجراء.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" id="confirmDeleteStep" class="btn btn-danger">تأكيد الحذف</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle delete modal
        const deleteModal = document.getElementById('deleteModal');
        if (deleteModal) {
            deleteModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const itemName = button.getAttribute('data-name') || '{{ task.title }}';
                const url = button.getAttribute('data-url') || '{% url "Hr:tasks:delete" task.pk %}';

                document.getElementById('itemName').textContent = itemName;
                document.getElementById('confirmDelete').setAttribute('href', url);
            });
        }

        // Handle step checkboxes
        const stepCheckboxes = document.querySelectorAll('.step-checkbox');
        stepCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const taskId = this.dataset.taskId;
                const stepId = this.dataset.stepId;
                const url = `/Hr/tasks/${taskId}/steps/${stepId}/toggle/`;

                // تغيير مظهر النص
                const label = document.querySelector(`label[for="step-${stepId}"]`);
                if (this.checked) {
                    label.classList.add('text-decoration-line-through', 'text-muted');
                } else {
                    label.classList.remove('text-decoration-line-through', 'text-muted');
                }

                // إرسال طلب AJAX لتحديث حالة الخطوة
                fetch(url, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // تحديث شريط التقدم
                        const progressBar = document.querySelector('.progress-bar');
                        progressBar.style.width = `${data.task_progress}%`;
                        progressBar.setAttribute('aria-valuenow', data.task_progress);

                        // تحديث النص
                        const progressText = document.querySelector('.progress + small');
                        progressText.textContent = `${data.task_progress}%`;

                        // تغيير لون شريط التقدم
                        progressBar.classList.remove('bg-danger', 'bg-warning', 'bg-success');
                        if (data.task_progress < 30) {
                            progressBar.classList.add('bg-danger');
                        } else if (data.task_progress < 70) {
                            progressBar.classList.add('bg-warning');
                        } else {
                            progressBar.classList.add('bg-success');
                        }

                        // إضافة أو إزالة تاريخ الإكمال
                        const stepItem = checkbox.closest('.list-group-item');
                        const dateElement = stepItem.querySelector('.text-muted.me-3');

                        if (data.completed) {
                            if (!dateElement) {
                                const today = new Date().toISOString().split('T')[0];
                                const newDateElement = document.createElement('small');
                                newDateElement.className = 'text-muted me-3';
                                newDateElement.innerHTML = `<i class="fas fa-calendar-check me-1"></i>${today}`;
                                stepItem.querySelector('.d-flex.align-items-center').prepend(newDateElement);
                            }
                        } else {
                            if (dateElement) {
                                dateElement.remove();
                            }
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            });
        });

        // Handle delete step
        const deleteStepModal = document.getElementById('deleteStepModal');
        let currentStepId, currentTaskId;

        if (deleteStepModal) {
            deleteStepModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                currentTaskId = button.dataset.taskId;
                currentStepId = button.dataset.stepId;
            });

            const confirmDeleteStepBtn = document.getElementById('confirmDeleteStep');
            confirmDeleteStepBtn.addEventListener('click', function() {
                const url = `/Hr/tasks/${currentTaskId}/steps/${currentStepId}/delete/`;

                // إرسال طلب AJAX لحذف الخطوة
                fetch(url, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // إزالة الخطوة من واجهة المستخدم
                        const stepElement = document.getElementById(`step-${currentStepId}`).closest('.list-group-item');
                        stepElement.remove();

                        // تحديث شريط التقدم
                        const progressBar = document.querySelector('.progress-bar');
                        progressBar.style.width = `${data.task_progress}%`;
                        progressBar.setAttribute('aria-valuenow', data.task_progress);

                        // تحديث النص
                        const progressText = document.querySelector('.progress + small');
                        progressText.textContent = `${data.task_progress}%`;

                        // تغيير لون شريط التقدم
                        progressBar.classList.remove('bg-danger', 'bg-warning', 'bg-success');
                        if (data.task_progress < 30) {
                            progressBar.classList.add('bg-danger');
                        } else if (data.task_progress < 70) {
                            progressBar.classList.add('bg-warning');
                        } else {
                            progressBar.classList.add('bg-success');
                        }

                        // تحديث عدد الخطوات
                        const stepsCount = document.querySelector('.badge.bg-primary');
                        const currentCount = parseInt(stepsCount.textContent);
                        stepsCount.textContent = `${currentCount - 1} خطوة`;

                        // إغلاق النافذة المنبثقة
                        const bsModal = bootstrap.Modal.getInstance(deleteStepModal);
                        bsModal.hide();

                        // إظهار رسالة نجاح
                        const alertContainer = document.createElement('div');
                        alertContainer.className = 'alert alert-success alert-dismissible fade show';
                        alertContainer.innerHTML = `
                            <i class="fas fa-check-circle me-2"></i>
                            تم حذف الخطوة بنجاح
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        `;
                        document.querySelector('.card-body').prepend(alertContainer);

                        // إزالة التنبيه بعد 3 ثوانٍ
                        setTimeout(() => {
                            alertContainer.remove();
                        }, 3000);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            });
        }
    });
</script>
{% endblock %}
