# Generated by Django 5.0.14 on 2025-05-07 05:28

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255, verbose_name='عنوان التنبيه')),
                ('message', models.TextField(verbose_name='نص التنبيه')),
                ('notification_type', models.CharField(choices=[('hr', 'الموارد البشرية'), ('meetings', 'الاجتماعات'), ('inventory', 'المخزن'), ('purchase', 'المشتريات'), ('system', 'النظام')], max_length=20, verbose_name='نوع التنبيه')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('medium', 'متوسطة'), ('high', 'عالية'), ('urgent', 'عاجلة')], default='medium', max_length=20, verbose_name='الأولوية')),
                ('object_id', models.PositiveIntegerField(blank=True, null=True, verbose_name='معرف الكائن')),
                ('is_read', models.BooleanField(default=False, verbose_name='تمت القراءة')),
                ('read_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ القراءة')),
                ('url', models.CharField(blank=True, max_length=255, null=True, verbose_name='رابط التنبيه')),
                ('icon', models.CharField(default='fas fa-bell', max_length=50, verbose_name='أيقونة التنبيه')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('content_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype', verbose_name='نوع المحتوى')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'تنبيه',
                'verbose_name_plural': 'التنبيهات',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'is_read'], name='notificatio_user_id_427e4b_idx'), models.Index(fields=['notification_type'], name='notificatio_notific_f2898f_idx')],
            },
        ),
    ]
