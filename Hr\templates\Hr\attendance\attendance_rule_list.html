{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ page_title }} - نظام الدولية{% endblock %}

{% block page_title %}{{ page_title }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموظفين</a></li>
<li class="breadcrumb-item active">{{ page_title }}</li>
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">قواعد الحضور</h5>
        <a href="{% url 'Hr:attendance:attendance_rule_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> إنشاء قاعدة جديدة
        </a>
    </div>
    <div class="card-body">
        {% if attendance_rules %}
        <div class="table-responsive">
            <table class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>الاسم</th>
                        <th>الوصف</th>
                        <th>فترة السماح للتأخير</th>
                        <th>فترة السماح للمغادرة</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for rule in attendance_rules %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ rule.name }}</td>
                        <td>{{ rule.description|default:"-" }}</td>
                        <td>{{ rule.late_grace_minutes }} دقيقة</td>
                        <td>{{ rule.early_leave_grace_minutes }} دقيقة</td>
                        <td>
                            {% if rule.is_active %}
                            <span class="badge bg-success">نشط</span>
                            {% else %}
                            <span class="badge bg-danger">غير نشط</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'Hr:attendance:attendance_rule_edit' rule.pk %}" class="btn btn-primary">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                                <a href="{% url 'Hr:attendance:attendance_rule_delete' rule.pk %}" class="btn btn-danger">
                                    <i class="fas fa-trash"></i> حذف
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            لا توجد قواعد حضور مسجلة حتى الآن.
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
