{% extends 'Hr/base_hr.html' %}
{% load static %}

{% block title %}تعديل الوظيفة - {{ job.jop_name }} - نظام الدولية{% endblock %}

{% block page_title %}تعديل الوظيفة: {{ job.jop_name }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموارد البشرية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:jobs:list' %}">الوظائف</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:jobs:detail' job.jop_code %}">{{ job.jop_name }}</a></li>
<li class="breadcrumb-item active">تعديل</li>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow-sm">
            <div class="card-header bg-light py-3">
                <h5 class="mb-0 fw-semibold">
                    <i class="fas fa-edit text-primary me-2"></i>
                    تعديل بيانات الوظيفة
                </h5>
            </div>
            <div class="card-body p-4">
                <form method="post" novalidate>
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                    <div class="alert alert-danger mb-4">
                        {% for error in form.non_field_errors %}
                        <p class="mb-0">{{ error }}</p>
                        {% endfor %}
                    </div>
                    {% endif %}
                    
                    <div class="row g-3">
                        <!-- Job Code -->
                        <div class="col-md-6">
                            <label for="{{ form.jop_code.id_for_label }}" class="form-label">رمز الوظيفة <span class="text-danger">*</span></label>
                            {{ form.jop_code }}
                            {% if form.jop_code.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.jop_code.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            {% if form.jop_code.help_text %}
                            <div class="form-text">{{ form.jop_code.help_text }}</div>
                            {% endif %}
                        </div>
                        
                        <!-- Job Name -->
                        <div class="col-md-6">
                            <label for="{{ form.jop_name.id_for_label }}" class="form-label">اسم الوظيفة <span class="text-danger">*</span></label>
                            {{ form.jop_name }}
                            {% if form.jop_name.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.jop_name.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            {% if form.jop_name.help_text %}
                            <div class="form-text">{{ form.jop_name.help_text }}</div>
                            {% endif %}
                        </div>
                        
                        <!-- Department -->
                        <div class="col-md-12">
                            <label for="{{ form.department.id_for_label }}" class="form-label">القسم</label>
                            {{ form.department }}
                            {% if form.department.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.department.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            {% if form.department.help_text %}
                            <div class="form-text">{{ form.department.help_text }}</div>
                            {% endif %}
                        </div>

                        <!-- Submit Button Section -->
                        <div class="col-12 mt-4">
                            <hr>
                            <div class="d-flex justify-content-between align-items-center">
                                <a href="{% url 'Hr:jobs:detail' job.jop_code %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-right me-1"></i> عودة
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>
                                    حفظ التغييرات
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .form-control.is-invalid {
        border-color: #dc3545;
        padding-right: calc(1.5em + 0.75rem);
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right calc(0.375em + 0.1875rem) center;
        background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add custom class to form inputs
        document.querySelectorAll('form select, form input, form textarea').forEach(function(element) {
            // Skip checkboxes
            if (element.type !== 'checkbox') {
                element.classList.add('form-control');
                
                // Add is-invalid class to inputs with errors
                if (element.closest('.col-md-6, .col-md-12').querySelector('.invalid-feedback')) {
                    element.classList.add('is-invalid');
                }
            } else {
                // For checkboxes add form-check-input class
                element.classList.add('form-check-input');
            }
        });
        
        // Make select2 for dropdowns if select2 is available
        if (typeof $.fn.select2 !== 'undefined') {
            $('#{{ form.department.id_for_label }}').select2({
                placeholder: 'اختر القسم',
                allowClear: true,
                width: '100%',
                dir: 'rtl'
            });
        }
    });
</script>
{% endblock %}
