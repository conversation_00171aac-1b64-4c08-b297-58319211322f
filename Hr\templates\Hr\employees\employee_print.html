{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load django_permissions %}
{% load form_utils %}

{% block title %}{{ employee.emp_full_name }} - طباعة بيانات الموظف{% endblock %}

{% block page_title %}طباعة بيانات الموظف{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:employees:list' %}">الموظفين</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:employees:detail' emp_id=employee.emp_id %}">{{ employee.emp_full_name|default:"موظف" }}</a></li>
<li class="breadcrumb-item active">طباعة</li>
{% endblock %}

{% block content %}
<!-- شريط الإجراءات -->
<div class="d-flex justify-content-between align-items-center mb-4 print-hide">
    <div>
        <h4 class="mb-0">{{ employee.emp_full_name }}</h4>
        <p class="text-muted mb-0">{{ employee.jop_name|default:"No Job Assigned" }}</p>
    </div>
    <div class="btn-group">
        <button onclick="window.print();" class="btn btn-primary">
            <i class="fas fa-print me-1"></i> طباعة
        </button>
        <a href="{% url 'Hr:employees:detail' emp_id=employee.emp_id %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i> العودة
        </a>
    </div>
</div>

<!-- رأس الطباعة -->
<div class="print-header d-none d-print-block mb-4">
    <div class="text-center">
        <h2>شركة الدولية لتقنية المعلومات</h2>
        <h3>بيانات الموظف: {{ employee.emp_full_name }}</h3>
        <p>تاريخ الطباعة: {% now "Y-m-d" %}</p>
    </div>
</div>

<!-- المعلومات العامة -->
<div class="row">
    <!-- البيانات الشخصية -->
    <div class="col-lg-4 mb-4">
        <div class="card shadow-sm h-100">
            <div class="card-body text-center pt-4">
                {% if employee.emp_image %}
                <div class="employee-avatar mx-auto position-relative mb-3">
                    <img src="{{ employee.emp_image|binary_to_img }}" alt="{{ employee.emp_full_name }}" class="rounded-circle user-image">
                    {% if employee.working_condition == 'سارى' %}
                    <span class="status-badge bg-success"><i class="fas fa-check"></i></span>
                    {% elif employee.working_condition == 'استقالة' %}
                    <span class="status-badge bg-info"><i class="fas fa-pause"></i></span>
                    {% elif employee.working_condition == 'انقطاع عن العمل'%}
                    <span class="status-badge bg-danger"><i class="fas fa-times"></i></span>
                    {% endif %}
                </div>
                {% else %}
                <div class="employee-avatar-placeholder mx-auto position-relative mb-3">
                    <div class="avatar-placeholder display-1">
                        {{ employee.emp_first_name|slice:":1"|upper }}
                    </div>
                    {% if employee.working_condition == 'سارى' %}
                    <span class="status-badge bg-success"><i class="fas fa-check"></i></span>
                    {% elif employee.working_condition == 'استقالة' %}
                    <span class="status-badge bg-info"><i class="fas fa-pause"></i></span>
                    {% elif employee.working_condition == 'انقطاع عن العمل'%}
                    <span class="status-badge bg-danger"><i class="fas fa-times"></i></span>
                    {% endif %}
                </div>
                {% endif %}

                <h5 class="mt-3 mb-1">{{ employee.emp_full_name }}</h5>
                <p class="text-muted">{{ employee.job_name|default:"-" }}</p>

                <hr>

                <div class="row text-start g-3 mt-2">
                    <div class="col-6">
                        <h6 class="text-muted mb-1">رقم الموظف</h6>
                        <p class="mb-0">{{ employee.emp_id }}</p>
                    </div>
                    <div class="col-6">
                        <h6 class="text-muted mb-1">الحالة</h6>
                        <p class="mb-0">{{ employee.working_condition }}</p>
                    </div>
                    <div class="col-6">
                        <h6 class="text-muted mb-1">الرقم القومي</h6>
                        <p class="mb-0">{{ employee.national_id|default:"-" }}</p>
                    </div>
                    <div class="col-6">
                        <h6 class="text-muted mb-1">تاريخ التعيين</h6>
                        <p class="mb-0">{% if employee.emp_date_hiring %}{{ employee.emp_date_hiring|date:"d/m/Y" }}{% else %}-{% endif %}</p>
                    </div>
                </div>

                <div class="mt-3">
                    <h6 class="text-muted mb-2">بيانات الاتصال</h6>
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-phone-alt text-muted me-2"></i>
                        <span>{{ employee.emp_phone1|default:"-" }}</span>
                    </div>
                    {% if employee.emp_phone2 %}
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-phone-alt text-muted me-2"></i>
                        <span>{{ employee.emp_phone2 }}</span>
                    </div>
                    {% endif %}
                    <div class="d-flex align-items-center">
                        <i class="fas fa-map-marker-alt text-muted me-2"></i>
                        <span>{{ employee.emp_address|default:"-" }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- البيانات التفصيلية -->
    <div class="col-lg-8">
        <!-- بيانات العمل والتوظيف -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-white py-3">
                <h5 class="mb-0">
                    <i class="fas fa-briefcase me-2"></i>
                    معلومات العمل
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">القسم</h6>
                        <p>{{ employee.department.dept_name|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">الوظيفة</h6>
                        <p>{{ employee.job_name|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">نوع الموظف</h6>
                        <p>{{ employee.emp_type|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">تاريخ التعيين</h6>
                        <p>{% if employee.emp_date_hiring %}{{ employee.emp_date_hiring|date:"d/m/Y" }}{% else %}-{% endif %}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">تاريخ تجديد العقد</h6>
                        <p>{% if employee.contract_renewal_date %}{{ employee.contract_renewal_date|date:"d/m/Y" }}{% else %}-{% endif %}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">نوع الوردية</h6>
                        <p>{{ employee.shift_type|default:"-" }}</p>
                    </div>
                </div>

                {% if employee.working_condition == 'استقالة' %}
                <hr>
                <h6 class="text-danger mb-2">بيانات الاستقالة</h6>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <h6 class="text-muted mb-1">تاريخ الاستقالة</h6>
                        <p>{% if employee.date_resignation %}{{ employee.date_resignation|date:"d/m/Y" }}{% else %}-{% endif %}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <h6 class="text-muted mb-1">سبب الاستقالة</h6>
                        <p>{{ employee.reason_resignation|default:"-" }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- بيانات التأمين -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-white py-3">
                <h5 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    التأمين والبطاقة الصحية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">حالة التأمين</h6>
                        <p>{{ employee.insurance_status|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">وظيفة التأمين</h6>
                        <p>{{ employee.job_insurance.job_name_insurance|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">رقم التأمين</h6>
                        <p>{{ employee.number_insurance|default:"-" }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* صورة الموظف وبيانات البطاقة الشخصية */
    .employee-avatar {
        width: 150px;
        height: 150px;
        border-radius: 50%;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 4px solid #fff;
        margin-bottom: 1.5rem;
    }

    .employee-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .employee-avatar-placeholder {
        width: 150px;
        height: 150px;
        margin-bottom: 1.5rem;
    }

    .avatar-placeholder {
        width: 150px;
        height: 150px;
        border-radius: 50%;
        background: linear-gradient(45deg, #0062cc, #0096ff);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 3.5rem;
        font-weight: 300;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 4px solid #fff;
    }

    .status-badge {
        position: absolute;
        bottom: 5px;
        right: 5px;
        width: 35px;
        height: 35px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 3px solid white;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }
    
    /* تنسيقات البطاقات */
    .card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        margin-bottom: 1.5rem;
    }
    
    .card-header {
        border-top-left-radius: 10px !important;
        border-top-right-radius: 10px !important;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    /* تحسين مظهر العناوين والنصوص */
    h5.mb-0 {
        font-weight: 600;
        color: #2c3e50;
    }
    
    h6.text-muted {
        font-size: 0.85rem;
        color: #6c757d !important;
        font-weight: 500;
    }
    
    p {
        color: #343a40;
        margin-bottom: 0.75rem;
    }
    
    /* تنسيق أزرار الإجراءات */
    .btn-group .btn {
        padding: 0.5rem 1rem;
        border-radius: 5px;
        font-weight: 500;
        letter-spacing: 0.3px;
        margin-right: 0.25rem;
    }
    
    /* رأسية صفحة الطباعة */
    .print-header {
        padding: 20px;
        text-align: center;
        border-bottom: 2px solid #e9ecef;
        margin-bottom: 30px;
    }
    
    .print-header h2 {
        font-size: 1.75rem;
        font-weight: bold;
        margin-bottom: 10px;
        color: #212529;
    }
    
    .print-header h3 {
        font-size: 1.3rem;
        margin-bottom: 10px;
        color: #495057;
    }
    
    .print-header p {
        font-size: 0.9rem;
        color: #6c757d;
    }
    
    /* تحسينات أخرى */
    .text-muted {
        color: #6c757d !important;
    }
    
    hr {
        margin: 1.5rem 0;
        background-color: rgba(0, 0, 0, 0.1);
        opacity: 0.5;
    }
    
    i.fas {
        color: #0d6efd;
    }
    
    /* Print styles */
    @media print {
        .print-hide {
            display: none !important;
        }
        
        .print-header {
            display: block !important;
        }
        
        .card {
            border: 1px solid #ddd !important;
            break-inside: avoid;
            box-shadow: none !important;
            margin-bottom: 20px;
        }
        
        .container-fluid {
            width: 100% !important;
            padding: 0 !important;
        }
        
        body {
            background-color: white !important;
            font-size: 12pt;
            color: #000;
        }
        
        h5 {
            font-size: 14pt;
            font-weight: 600;
        }
        
        h6 {
            font-size: 12pt;
            font-weight: 500;
        }
        
        p {
            font-size: 10pt;
        }
        
        .card-header {
            background-color: #f1f1f1 !important;
            padding: 0.75rem 1rem !important;
        }
        
        .status-badge {
            border: 2px solid #fff !important;
            box-shadow: none !important;
        }
        
        .employee-avatar {
            box-shadow: none !important;
            border: 3px solid #ddd !important;
        }
        
        /* تحسين تنسيق الصفحة المطبوعة */
        @page {
            size: A4;
            margin: 1cm;
        }
        
        /* منع تقسيم العناصر بين الصفحات */
        .card, .row, .col-lg-4, .col-lg-8 {
            page-break-inside: avoid;
        }
    }
</style>
{% endblock %}
