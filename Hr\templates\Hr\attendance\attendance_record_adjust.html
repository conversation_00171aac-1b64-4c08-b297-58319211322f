{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ page_title }} - نظام الدولية{% endblock %}

{% block page_title %}{{ page_title }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموظفين</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:attendance:attendance_record_list' %}">سجلات الحضور</a></li>
<li class="breadcrumb-item active">{{ page_title }}</li>
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">تعديل سجل الحضور</h5>
    </div>
    <div class="card-body">
        <form method="post" novalidate>
            {% csrf_token %}

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">الموظف</label>
                        <p class="form-control-static">{{ record.employee.emp_full_name }}</p>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">التاريخ</label>
                        <p class="form-control-static">{{ record.date }}</p>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.check_in_time.id_for_label }}" class="form-label">{{ form.check_in_time.label }}</label>
                        {{ form.check_in_time }}
                        {% if form.check_in_time.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.check_in_time.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.check_out_time.id_for_label }}" class="form-label">{{ form.check_out_time.label }}</label>
                        {{ form.check_out_time }}
                        {% if form.check_out_time.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.check_out_time.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.notes.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                        <small class="form-text text-muted">أدخل سبب التعديل أو أي ملاحظات إضافية</small>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.adjustment_reason.id_for_label }}" class="form-label">{{ form.adjustment_reason.label }}</label>
                        {{ form.adjustment_reason }}
                        {% if form.adjustment_reason.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.adjustment_reason.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.is_approved }}
                            <label class="form-check-label" for="{{ form.is_approved.id_for_label }}">
                                {{ form.is_approved.label }}
                            </label>
                        </div>
                        {% if form.is_approved.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.is_approved.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-between">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ التعديل
                </button>
                <a href="{% url 'Hr:attendance:attendance_record_list' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
            </div>
        </form>

        {% if record.adjustments.exists %}
        <div class="mt-4">
            <h6>سجل التعديلات السابقة:</h6>
            <div class="table-responsive">
                <table class="table table-sm table-bordered">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>المستخدم</th>
                            <th>التعديل</th>
                            <th>السبب</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for adj in record.adjustments.all %}
                        <tr>
                            <td>{{ adj.created_at }}</td>
                            <td>{{ adj.created_by }}</td>
                            <td>{{ adj.get_changes_display }}</td>
                            <td>{{ adj.reason }}</td>
                            <td>
                                {% if adj.is_approved %}
                                <span class="badge bg-success">معتمد</span>
                                {% else %}
                                <span class="badge bg-warning">في انتظار الاعتماد</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Initialize time pickers
        $('#{{ form.check_in_time.id_for_label }}, #{{ form.check_out_time.id_for_label }}').flatpickr({
            enableTime: true,
            noCalendar: true,
            dateFormat: "H:i",
            time_24hr: true,
            minuteIncrement: 1
        });
        
        // Initialize select2 for adjustment reason dropdown
        $('#{{ form.adjustment_reason.id_for_label }}').select2({
            theme: 'bootstrap-5',
            language: "ar",
            dir: "rtl"
        });
    });
</script>
{% endblock %}