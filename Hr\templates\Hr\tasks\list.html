{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load django_permissions %}

{% block title %}مهام الموظفين - نظام الدولية{% endblock %}

{% block page_title %}{{ title }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموارد البشرية</a></li>
<li class="breadcrumb-item active">{{ title }}</li>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <!-- بطاقات الإحصائيات -->
    <div class="col-md-3">
        <div class="card bg-primary text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-0">إجمالي المهام</h6>
                        <h2 class="mt-2 mb-0">{{ tasks|length }}</h2>
                    </div>
                    <i class="fas fa-tasks fa-2x opacity-50"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-0">قيد الانتظار</h6>
                        <h2 class="mt-2 mb-0">{{ pending_tasks }}</h2>
                    </div>
                    <i class="fas fa-clock fa-2x opacity-50"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-0">قيد التنفيذ</h6>
                        <h2 class="mt-2 mb-0">{{ in_progress_tasks }}</h2>
                    </div>
                    <i class="fas fa-spinner fa-2x opacity-50"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-danger text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-0">متأخرة</h6>
                        <h2 class="mt-2 mb-0">{{ overdue_tasks }}</h2>
                    </div>
                    <i class="fas fa-exclamation-triangle fa-2x opacity-50"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card shadow-sm">
    <div class="card-header bg-white d-flex justify-content-between align-items-center py-3">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h4 class="mb-0">{{ title }}</h4>
            {% if perms.Hr.add_task or user|is_admin %}
                <a href="{% url 'Hr:tasks:create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i> إضافة مهمة جديدة
                </a>
            {% endif %}
        </div>
    </div>
    <div class="card-body">
        <!-- فلاتر البحث -->
        <div class="row mb-4">
            <div class="col-md-12">
                <form method="get" class="row g-3">
                    <div class="col-md-3">
                        <label for="status" class="form-label">الحالة</label>
                        <select name="status" id="status" class="form-select">
                            <option value="">الكل</option>
                            <option value="pending" {% if request.GET.status == 'pending' %}selected{% endif %}>قيد الانتظار</option>
                            <option value="in_progress" {% if request.GET.status == 'in_progress' %}selected{% endif %}>قيد التنفيذ</option>
                            <option value="completed" {% if request.GET.status == 'completed' %}selected{% endif %}>مكتملة</option>
                            <option value="cancelled" {% if request.GET.status == 'cancelled' %}selected{% endif %}>ملغاة</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="priority" class="form-label">الأولوية</label>
                        <select name="priority" id="priority" class="form-select">
                            <option value="">الكل</option>
                            <option value="low" {% if request.GET.priority == 'low' %}selected{% endif %}>منخفضة</option>
                            <option value="medium" {% if request.GET.priority == 'medium' %}selected{% endif %}>متوسطة</option>
                            <option value="high" {% if request.GET.priority == 'high' %}selected{% endif %}>عالية</option>
                            <option value="urgent" {% if request.GET.priority == 'urgent' %}selected{% endif %}>عاجلة</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="employee" class="form-label">الموظف</label>
                        <select name="employee" id="employee" class="form-select">
                            <option value="">الكل</option>
                            {% for task in tasks %}
                                {% if task.employee %}
                                <option value="{{ task.employee.emp_id }}" {% if request.GET.employee == task.employee.emp_id|stringformat:"s" %}selected{% endif %}>
                                    {{ task.employee.emp_name }}
                                </option>
                                {% endif %}
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-filter me-1"></i>
                            تصفية
                        </button>
                        <a href="{% url 'Hr:tasks:list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-redo me-1"></i>
                            إعادة ضبط
                        </a>
                    </div>
                </form>
            </div>
        </div>

        {% if tasks %}
        <div class="table-responsive">
            <table class="table table-hover align-middle">
                <thead class="table-light">
                    <tr>
                        <th>العنوان</th>
                        <th>الموظف</th>
                        <th>الحالة</th>
                        <th>الأولوية</th>
                        <th>تاريخ الاستحقاق</th>
                        <th>نسبة الإنجاز</th>
                        <th class="text-center">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for task in tasks %}
                    <tr>
                        <td>
                            <a href="{% url 'Hr:tasks:detail' task.pk %}" class="text-decoration-none">
                                {{ task.title }}
                            </a>
                        </td>
                        <td>{{ task.employee.emp_name }}</td>
                        <td>
                            {% if task.status == 'pending' %}
                            <span class="badge bg-warning">قيد الانتظار</span>
                            {% elif task.status == 'in_progress' %}
                            <span class="badge bg-info">قيد التنفيذ</span>
                            {% elif task.status == 'completed' %}
                            <span class="badge bg-success">مكتملة</span>
                            {% elif task.status == 'cancelled' %}
                            <span class="badge bg-secondary">ملغاة</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if task.priority == 'low' %}
                            <span class="badge bg-success">منخفضة</span>
                            {% elif task.priority == 'medium' %}
                            <span class="badge bg-info">متوسطة</span>
                            {% elif task.priority == 'high' %}
                            <span class="badge bg-warning">عالية</span>
                            {% elif task.priority == 'urgent' %}
                            <span class="badge bg-danger">عاجلة</span>
                            {% endif %}
                        </td>
                        <td>{{ task.due_date|date:"Y-m-d" }}</td>
                        <td>
                            <div class="progress" style="height: 10px;">
                                <div class="progress-bar {% if task.progress < 30 %}bg-danger{% elif task.progress < 70 %}bg-warning{% else %}bg-success{% endif %}" 
                                     role="progressbar" 
                                     style="width: {{ task.progress }}%;" 
                                     aria-valuenow="{{ task.progress }}" 
                                     aria-valuemin="0" 
                                     aria-valuemax="100">
                                </div>
                            </div>
                            <small class="text-muted">{{ task.progress }}%</small>
                        </td>
                        <td class="text-center">
                            <div class="btn-group">
                                {% if perms.Hr.change_task or user|is_admin %}
                                    <a href="{% url 'Hr:tasks:edit' task.id %}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i> تعديل
                                    </a>
                                {% endif %}
                                {% if perms.Hr.delete_task or user|is_admin %}
                                    <button type="button" class="btn btn-sm btn-danger delete-task" 
                                            data-task-id="{{ task.id }}"
                                            data-task-name="{{ task.title }}">
                                        <i class="fas fa-trash"></i> حذف
                                    </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-tasks text-muted fa-4x mb-3"></i>
            <h5>لا توجد مهام</h5>
            <p class="text-muted">
                {% if request.GET.status or request.GET.priority or request.GET.employee %}
                لا توجد مهام تطابق معايير البحث
                {% else %}
                لم يتم إنشاء أي مهام بعد
                {% endif %}
            </p>
            <a href="{% url 'Hr:tasks:create' %}" class="btn btn-primary mt-3">
                <i class="fas fa-plus-circle me-1"></i>
                إنشاء مهمة جديدة
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // تفعيل DataTables إذا كان هناك بيانات
        if ($('table tbody tr').length > 0) {
            $('table').DataTable({
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
                },
                "order": [[4, "asc"]], // ترتيب حسب تاريخ الاستحقاق
                "columnDefs": [
                    { "orderable": false, "targets": 6 } // عمود الإجراءات غير قابل للترتيب
                ]
            });
        }
    });
</script>
{% endblock %}
