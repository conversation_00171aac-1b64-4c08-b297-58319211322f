# Generated by Django 5.0.14 on 2025-06-11 15:05

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tasks', '0002_alter_task_options'),
    ]

    operations = [
        migrations.AddField(
            model_name='task',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now, verbose_name='تاريخ الإنشاء'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='task',
            name='title',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='عنوان المهمة'),
        ),
        migrations.AddField(
            model_name='task',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
        ),
        migrations.AlterField(
            model_name='task',
            name='description',
            field=models.TextField(verbose_name='وصف المهمة'),
        ),
        migrations.AlterField(
            model_name='task',
            name='end_date',
            field=models.DateTimeField(verbose_name='تاريخ الانتهاء'),
        ),
        migrations.AlterField(
            model_name='task',
            name='start_date',
            field=models.DateTimeField(verbose_name='تاريخ البدء'),
        ),
        migrations.AlterField(
            model_name='task',
            name='status',
            field=models.CharField(choices=[('pending', 'قيد الانتظار'), ('in_progress', 'يجرى العمل عليها'), ('completed', 'مكتملة'), ('canceled', 'ملغاة'), ('deferred', 'مؤجلة'), ('failed', 'فشلت')], default='pending', max_length=20, verbose_name='الحالة'),
        ),
    ]
