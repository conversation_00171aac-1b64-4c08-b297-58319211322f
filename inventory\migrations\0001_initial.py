# Generated by Django 5.0.14 on 2025-05-07 05:28

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم التصنيف')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
            ],
            options={
                'verbose_name': 'تصنيف',
                'verbose_name_plural': 'التصنيفات',
            },
        ),
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON><PERSON>(max_length=100, verbose_name='اسم العميل')),
                ('contact_person', models.CharField(blank=True, max_length=100, null=True, verbose_name='الشخص المسؤول')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('address', models.TextField(blank=True, null=True, verbose_name='العنوان')),
            ],
            options={
                'verbose_name': 'عميل',
                'verbose_name_plural': 'العملاء',
            },
        ),
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم القسم')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
            ],
            options={
                'verbose_name': 'قسم',
                'verbose_name_plural': 'الأقسام',
            },
        ),
        migrations.CreateModel(
            name='LocalSystemSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('company_name', models.CharField(max_length=100, verbose_name='اسم الشركة')),
                ('company_logo', models.ImageField(blank=True, null=True, upload_to='settings/', verbose_name='شعار الشركة')),
                ('company_address', models.TextField(blank=True, null=True, verbose_name='عنوان الشركة')),
                ('company_phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم هاتف الشركة')),
                ('company_email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني للشركة')),
            ],
            options={
                'verbose_name': 'إعدادات النظام المحلية',
                'verbose_name_plural': 'إعدادات النظام المحلية',
            },
        ),
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم المورد')),
                ('contact_person', models.CharField(blank=True, max_length=100, null=True, verbose_name='الشخص المسؤول')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('address', models.TextField(blank=True, null=True, verbose_name='العنوان')),
            ],
            options={
                'verbose_name': 'مورد',
                'verbose_name_plural': 'الموردين',
            },
        ),
        migrations.CreateModel(
            name='SystemSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('language', models.CharField(choices=[('ar', 'العربية'), ('en', 'English')], default='ar', max_length=2, verbose_name='لغة النظام')),
                ('font_family', models.CharField(choices=[('cairo', 'Cairo'), ('tajawal', 'Tajawal'), ('almarai', 'Almarai'), ('ibm-plex-sans-arabic', 'IBM Plex Sans Arabic'), ('noto-sans-arabic', 'Noto Sans Arabic')], default='cairo', max_length=50, verbose_name='الخط المستخدم')),
                ('text_direction', models.CharField(choices=[('rtl', 'من اليمين إلى اليسار'), ('ltr', 'من اليسار إلى اليمين')], default='rtl', max_length=3, verbose_name='اتجاه النص')),
            ],
            options={
                'verbose_name': 'إعدادات النظام',
                'verbose_name_plural': 'إعدادات النظام',
            },
        ),
        migrations.CreateModel(
            name='TblCategories',
            fields=[
                ('cat_id', models.IntegerField(db_column='CAT_ID', primary_key=True, serialize=False)),
                ('cat_name', models.CharField(blank=True, db_collation='Arabic_CI_AS', db_column='CAT_Name', max_length=100, null=True)),
            ],
            options={
                'db_table': 'Tbl_Categories',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='TblCustomers',
            fields=[
                ('customer_id', models.IntegerField(db_column='Customer_ID', primary_key=True, serialize=False)),
                ('customer_name', models.CharField(blank=True, db_collation='Arabic_CI_AS', db_column='Customer_Name', max_length=100, null=True)),
            ],
            options={
                'db_table': 'Tbl_Customers',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='TblInvoices',
            fields=[
                ('invoice_id', models.IntegerField(db_column='Invoice_ID')),
                ('invoice_number', models.CharField(db_collation='Arabic_CI_AS', db_column='Invoice_Number', max_length=255, primary_key=True, serialize=False)),
                ('invoice_date', models.DateField(blank=True, db_column='Invoice_Date', null=True)),
                ('invoice_type', models.CharField(blank=True, db_collation='Arabic_CI_AS', db_column='Invoice_Type', max_length=100, null=True)),
                ('numberofitems', models.IntegerField(blank=True, db_column='NumberOfItems', null=True)),
                ('recipient', models.CharField(blank=True, db_collation='Arabic_CI_AS', db_column='Recipient', max_length=100, null=True)),
                ('supplier_id', models.IntegerField(blank=True, db_column='Supplier_ID', null=True)),
                ('customer_id', models.IntegerField(blank=True, db_column='Customer_ID', null=True)),
                ('customer_invoice_number', models.CharField(blank=True, db_collation='Arabic_CI_AS', db_column='Customer_Invoice_Number', max_length=255, null=True)),
                ('supplier_invoice_number', models.CharField(blank=True, db_collation='Arabic_CI_AS', db_column='Supplier_Invoice_Number', max_length=255, null=True)),
                ('total_invoice_value', models.DecimalField(blank=True, db_column='Total_Invoice_Value', decimal_places=2, max_digits=18, null=True)),
            ],
            options={
                'db_table': 'Tbl_Invoices',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='TblSuppliers',
            fields=[
                ('supplier_id', models.IntegerField(db_column='Supplier_ID', primary_key=True, serialize=False)),
                ('supplier_name', models.CharField(blank=True, db_collation='Arabic_CI_AS', db_column='Supplier_Name', max_length=100, null=True)),
            ],
            options={
                'db_table': 'Tbl_Suppliers',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='TblUnitsSpareparts',
            fields=[
                ('unit_id', models.IntegerField(db_column='Unit_ID', primary_key=True, serialize=False)),
                ('unit_name', models.CharField(blank=True, db_collation='Arabic_CI_AS', db_column='Unit_Name', max_length=100, null=True)),
            ],
            options={
                'db_table': 'Tbl_Units_SpareParts',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='Unit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='اسم الوحدة')),
                ('symbol', models.CharField(blank=True, max_length=10, null=True, verbose_name='رمز الوحدة')),
            ],
            options={
                'verbose_name': 'وحدة قياس',
                'verbose_name_plural': 'وحدات القياس',
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('product_id', models.CharField(max_length=100, primary_key=True, serialize=False, verbose_name='رقم الصنف')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الصنف')),
                ('initial_quantity', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الرصيد الافتتاحي')),
                ('quantity', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الرصيد الحالي')),
                ('minimum_threshold', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الحد الأدنى')),
                ('maximum_threshold', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الحد الأقصى')),
                ('unit_price', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='سعر الوحدة')),
                ('location', models.CharField(blank=True, max_length=100, null=True, verbose_name='الموقع')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('image', models.ImageField(blank=True, null=True, upload_to='products/', verbose_name='الصورة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='products', to='inventory.category', verbose_name='التصنيف')),
                ('unit', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='products', to='inventory.unit', verbose_name='وحدة القياس')),
            ],
            options={
                'verbose_name': 'صنف',
                'verbose_name_plural': 'الأصناف',
            },
        ),
        migrations.CreateModel(
            name='PurchaseRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('requested_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الطلب')),
                ('status', models.CharField(choices=[('pending', 'قيد الانتظار'), ('approved', 'تمت الموافقة'), ('rejected', 'مرفوض')], default='pending', max_length=10, verbose_name='الحالة')),
                ('approved_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الموافقة')),
                ('approved_by', models.CharField(blank=True, max_length=100, null=True, verbose_name='تمت الموافقة بواسطة')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='purchase_requests', to='inventory.product', verbose_name='الصنف')),
            ],
            options={
                'verbose_name': 'طلب شراء',
                'verbose_name_plural': 'طلبات الشراء',
            },
        ),
        migrations.CreateModel(
            name='TblProducts',
            fields=[
                ('product_id', models.CharField(db_collation='Arabic_CI_AS', db_column='Product_ID', max_length=100, primary_key=True, serialize=False)),
                ('product_name', models.CharField(blank=True, db_collation='Arabic_CI_AS', db_column='Product_Name', max_length=100, null=True)),
                ('initial_balance', models.DecimalField(blank=True, db_column='Initial_Balance', decimal_places=2, max_digits=18, null=True)),
                ('elwarad', models.DecimalField(blank=True, db_column='ElWarad', decimal_places=2, max_digits=18, null=True)),
                ('mortagaaomalaa', models.DecimalField(blank=True, db_column='MortagaaOmalaa', decimal_places=2, max_digits=18, null=True)),
                ('elmonsarf', models.DecimalField(blank=True, db_column='ElMonsarf', decimal_places=2, max_digits=18, null=True)),
                ('mortagaaelmawarden', models.DecimalField(blank=True, db_column='MortagaaElMawarden', decimal_places=2, max_digits=18, null=True)),
                ('qte_in_stock', models.DecimalField(blank=True, db_column='QTE_IN_STOCK', decimal_places=2, max_digits=18, null=True)),
                ('cat_name', models.CharField(blank=True, db_collation='Arabic_CI_AS', db_column='CAT_Name', max_length=100, null=True)),
                ('unit_name', models.CharField(blank=True, db_collation='Arabic_CI_AS', db_column='Unit_Name', max_length=100, null=True)),
                ('minimum_threshold', models.DecimalField(blank=True, db_column='Minimum_Threshold', decimal_places=2, max_digits=18, null=True)),
                ('maximum_threshold', models.DecimalField(blank=True, db_column='Maximum_Threshold', decimal_places=2, max_digits=18, null=True)),
                ('image_product', models.BinaryField(blank=True, db_column='Image_Product', max_length='max', null=True)),
                ('unit_price', models.DecimalField(blank=True, db_column='Unit_Price', decimal_places=2, max_digits=18, null=True)),
                ('location', models.CharField(blank=True, db_collation='Arabic_CI_AS', db_column='Location', max_length=50, null=True)),
                ('expiry_warning', models.CharField(blank=True, db_collation='Arabic_CI_AS', db_column='Expiry_Warning', max_length=10, null=True)),
                ('cat', models.ForeignKey(blank=True, db_column='CAT_ID', null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='inventory.tblcategories')),
                ('unit', models.ForeignKey(blank=True, db_column='Unit_ID', null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='inventory.tblunitsspareparts')),
            ],
            options={
                'db_table': 'Tbl_Products',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='TblInvoiceitems',
            fields=[
                ('invoice_code_programing', models.AutoField(db_column='Invoice_Code_Programing', primary_key=True, serialize=False)),
                ('invoice_id', models.IntegerField(blank=True, db_column='Invoice_ID', null=True)),
                ('invoice_number', models.CharField(blank=True, db_collation='Arabic_CI_AS', db_column='Invoice_Number', max_length=255, null=True)),
                ('product_name', models.CharField(blank=True, db_collation='Arabic_CI_AS', db_column='Product_Name', max_length=100, null=True)),
                ('quantity_elwarad', models.DecimalField(blank=True, db_column='Quantity_ElWarad', decimal_places=2, max_digits=18, null=True)),
                ('quantity_elmonsarf', models.DecimalField(blank=True, db_column='Quantity_ElMonsarf', decimal_places=2, max_digits=18, null=True)),
                ('quantity_mortagaaelmawarden', models.DecimalField(blank=True, db_column='Quantity_MortagaaElMawarden', decimal_places=2, max_digits=18, null=True)),
                ('quantity_mortagaaomalaa', models.DecimalField(blank=True, db_column='Quantity_MortagaaOmalaa', decimal_places=2, max_digits=18, null=True)),
                ('unit_id', models.IntegerField(blank=True, db_column='Unit_ID', null=True)),
                ('unit_price', models.DecimalField(blank=True, db_column='Unit_Price', decimal_places=2, max_digits=18, null=True)),
                ('invoice_date', models.DateField(blank=True, db_column='Invoice_Date', null=True)),
                ('invoice_type', models.CharField(blank=True, db_collation='Arabic_CI_AS', db_column='Invoice_Type', max_length=100, null=True)),
                ('recipient', models.CharField(blank=True, db_collation='Arabic_CI_AS', db_column='Recipient', max_length=100, null=True)),
                ('received_machine', models.CharField(blank=True, db_collation='Arabic_CI_AS', db_column='Received_Machine', max_length=100, null=True)),
                ('machine_unit', models.CharField(blank=True, db_collation='Arabic_CI_AS', db_column='Machine_Unit', max_length=100, null=True)),
                ('returninvoicenumber', models.CharField(blank=True, db_collation='Arabic_CI_AS', db_column='ReturnInvoiceNumber', max_length=100, null=True)),
                ('total_invoice_value', models.DecimalField(blank=True, db_column='Total_Invoice_Value', decimal_places=2, max_digits=18, null=True)),
                ('balance_time_entry', models.DecimalField(blank=True, db_column='Balance_Time_Entry', decimal_places=2, max_digits=18, null=True)),
                ('data_entry_date', models.DateField(blank=True, db_column='Data_Entry_Date', null=True)),
                ('data_entry_by', models.CharField(blank=True, db_collation='Arabic_CI_AS', db_column='Data_Entry_By', max_length=100, null=True)),
                ('notes', models.CharField(blank=True, db_collation='Arabic_CI_AS', db_column='Notes', max_length=100, null=True)),
                ('product', models.ForeignKey(blank=True, db_column='Product_ID', null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='inventory.tblproducts')),
            ],
            options={
                'db_table': 'Tbl_InvoiceItems',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='Voucher',
            fields=[
                ('voucher_number', models.CharField(max_length=100, primary_key=True, serialize=False, verbose_name='رقم الإذن')),
                ('voucher_type', models.CharField(choices=[('إذن اضافة', 'إذن اضافة'), ('إذن صرف', 'إذن صرف'), ('اذن مرتجع عميل', 'اذن مرتجع عميل'), ('إذن مرتجع مورد', 'إذن مرتجع مورد')], max_length=20, verbose_name='نوع الإذن')),
                ('date', models.DateField(verbose_name='تاريخ الإذن')),
                ('supplier_voucher_number', models.CharField(blank=True, max_length=100, null=True, verbose_name='رقم إذن المورد')),
                ('recipient', models.CharField(blank=True, max_length=100, null=True, verbose_name='المستلم')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='vouchers', to='inventory.customer', verbose_name='العميل')),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='vouchers', to='inventory.department', verbose_name='القسم')),
                ('supplier', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='vouchers', to='inventory.supplier', verbose_name='المورد')),
            ],
            options={
                'verbose_name': 'إذن',
                'verbose_name_plural': 'الأذونات',
            },
        ),
        migrations.CreateModel(
            name='VoucherItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity_added', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='الكمية المضافة')),
                ('quantity_disbursed', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='الكمية المنصرفة')),
                ('machine', models.CharField(blank=True, max_length=100, null=True, verbose_name='الماكينة')),
                ('machine_unit', models.CharField(blank=True, max_length=100, null=True, verbose_name='وحدة الماكينة')),
                ('unit_price', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='سعر الوحدة')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='voucher_items', to='inventory.product', verbose_name='الصنف')),
                ('voucher', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='inventory.voucher', verbose_name='الإذن')),
            ],
            options={
                'verbose_name': 'عنصر الإذن',
                'verbose_name_plural': 'عناصر الإذن',
            },
        ),
    ]
