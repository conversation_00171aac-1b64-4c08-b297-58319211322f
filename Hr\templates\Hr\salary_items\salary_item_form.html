{% extends 'Hr/base_hr.html' %}
{% load static %}

{% block title %}{{ page_title }} - نظام الدولية{% endblock %}

{% block page_title %}{{ page_title }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموظفين</a></li>
<li class="breadcrumb-item"><a href="{% url 'employees:salary_item_list' %}">بنود الرواتب</a></li>
<li class="breadcrumb-item active">{{ page_title }}</li>
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">{{ page_title }}</h5>
    </div>
    <div class="card-body">
        <form method="post">
            {% csrf_token %}
            
            <div class="mb-3">
                <label for="{{ form.name.id_for_label }}" class="form-label">{{ form.name.label }}</label>
                {{ form.name }}
                {% if form.name.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.name.errors %}
                    {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            
            <div class="mb-3">
                <label for="{{ form.item_type.id_for_label }}" class="form-label">{{ form.item_type.label }}</label>
                {{ form.item_type }}
                {% if form.item_type.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.item_type.errors %}
                    {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            
            <div class="mb-3">
                <label for="{{ form.calculation_method.id_for_label }}" class="form-label">{{ form.calculation_method.label }}</label>
                {{ form.calculation_method }}
                {% if form.calculation_method.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.calculation_method.errors %}
                    {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            
            <div class="mb-3 form-check">
                {{ form.affects_total }}
                <label for="{{ form.affects_total.id_for_label }}" class="form-check-label">{{ form.affects_total.label }}</label>
                {% if form.affects_total.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.affects_total.errors %}
                    {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            
            <div class="mb-3">
                <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }}</label>
                {{ form.description }}
                {% if form.description.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.description.errors %}
                    {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            
            <div class="d-flex justify-content-between">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ
                </button>
                <a href="{% url 'employees:salary_item_list' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
