{% extends 'administrator/base_admin.html' %}
{% load static %}

{% block title %}{{ page_title }} - لوحة تحكم مدير النظام{% endblock %}

{% block page_icon %}link{% endblock %}
{% block page_header %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    .app-section {
        margin-bottom: 2rem;
        border-radius: 0.5rem;
        overflow: hidden;
    }

    .app-header {
        padding: 1rem;
        color: white;
        font-weight: 600;
        display: flex;
        align-items: center;
        background-color: #6c757d; /* Default grey if no specific color */
    }

    .app-header i {
        margin-left: 0.75rem;
        font-size: 1.25rem;
    }

    .path-list {
        max-height: 400px;
        overflow-y: auto;
    }

    .path-item {
        display: flex;
        align-items: center;
        padding: 0.75rem 1rem;
        border-bottom: 1px solid rgba(0,0,0,0.05);
        transition: all 0.2s;
    }

    .path-item:hover {
        background-color: rgba(0,0,0,0.02);
    }

    .path-name {
        flex: 1;
    }

    .path-url {
        font-family: monospace;
        background-color: #f8f9fa;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        color: #495057;
        direction: ltr;
        text-align: left;
        display: inline-block;
        min-width: 200px;
    }

    .copy-btn {
        margin-right: 0.5rem;
        cursor: pointer;
        color: #6c757d;
        transition: all 0.2s;
    }

    .copy-btn:hover {
        color: var(--bs-primary);
    }

    .search-container {
        margin-bottom: 1.5rem;
    }

    .app-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 50rem;
        margin-right: 0.5rem;
        color: white;
    }

    /* App specific colors */
    .app-hr {
        background-color: #28a745;
    }

    .app-inventory {
        background-color: #fd7e14;
    }

    .app-tasks {
        background-color: #6f42c1;
    }

    .app-meetings {
        background-color: #e83e8c;
    }

    .app-purchase, .app-purchase_orders {
        background-color: #20c997;
    }

    .app-administrator {
        background-color: #0d6efd;
    }

    .app-accounts {
        background-color: #6c757d;
    }
    
    /* Additional app colors for newly discovered apps */
    .app-common {
        background-color: #17a2b8; /* cyan */
    }
    
    .app-eldawliya_sys {
        background-color: #dc3545; /* red */
    }
    
    .app-admin_permissions {
        background-color: #ffc107; /* yellow with dark text */
        color: #212529 !important; 
    }

    /* Tooltip styles */
    .tooltip-inner {
        max-width: 300px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-link me-2"></i>
                    دليل المسارات المتاحة في النظام
                </h5>
                <div>
                    <form method="post" class="d-inline-block me-2">
                        {% csrf_token %}
                        <button type="submit" name="refresh_paths" class="btn btn-success btn-sm">
                            <i class="fas fa-sync-alt me-1"></i>
                            تحديث المسارات
                        </button>
                    </form>
                    <a href="{% url 'administrator:module_list' %}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-puzzle-piece me-1"></i>
                        العودة إلى قائمة الوحدات
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    استخدم هذه الصفحة للحصول على المسارات الصحيحة عند إنشاء وحدات جديدة. يمكنك البحث عن المسار المطلوب ونسخه مباشرة.
                    يمكنك أيضًا تحديث المسارات بالضغط على زر "تحديث المسارات" لاكتشاف المسارات الجديدة في النظام.
                </div>

                <div class="alert alert-success">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-magic me-2"></i>
                            يمكنك إنشاء وحدات تلقائيًا من المسارات المكتشفة. اضغط على زر "إنشاء الوحدات" بجانب كل قسم لإنشاء وحدات لهذا القسم فقط.
                        </div>
                        <form method="post" action="{% url 'administrator:create_modules_from_paths' %}">
                            {% csrf_token %}
                            <input type="hidden" name="app_name" value="all">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-magic me-1"></i>
                                إنشاء جميع الوحدات
                            </button>
                        </form>
                    </div>
                </div>

                {% if messages %}
                <div class="mb-3">
                    {% for message in messages %}
                    <div class="alert alert-{{ message.tags }}">
                        <i class="fas {% if message.tags == 'success' %}fa-check-circle{% elif message.tags == 'warning' %}fa-exclamation-triangle{% elif message.tags == 'error' %}fa-times-circle{% else %}fa-info-circle{% endif %} me-2"></i>
                        {{ message }}
                    </div>
                    {% endfor %}
                </div>
                {% endif %}

                <!-- Search Box -->
                <div class="search-container">
                    <div class="input-group">
                        <span class="input-group-text bg-white">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" id="searchPaths" placeholder="ابحث عن مسار...">
                    </div>
                </div>

                <!-- App Sections -->
                <div class="row" id="appSections">
                    {% for app_name, paths in app_paths.items %}
                    <div class="col-md-6 mb-4 app-container">
                        <div class="app-section shadow-sm">
                            <div class="app-header app-{{ app_name|lower }}">
                                <div class="d-flex align-items-center">
                                    {% if app_name == 'Hr' %}
                                    <i class="fas fa-users"></i>
                                    <span class="me-2">الموارد البشرية</span>
                                    {% elif app_name == 'inventory' %}
                                    <i class="fas fa-boxes"></i>
                                    <span class="me-2">المخزن</span>
                                    {% elif app_name == 'tasks' %}
                                    <i class="fas fa-tasks"></i>
                                    <span class="me-2">المهام</span>
                                    {% elif app_name == 'meetings' %}
                                    <i class="fas fa-handshake"></i>
                                    <span class="me-2">الاجتماعات</span>
                                    {% elif app_name == 'Purchase_orders' %}
                                    <i class="fas fa-shopping-cart"></i>
                                    <span class="me-2">طلبات الشراء</span>
                                    {% elif app_name == 'administrator' %}
                                    <i class="fas fa-cogs"></i>
                                    <span class="me-2">مدير النظام</span>
                                    {% elif app_name == 'accounts' %}
                                    <i class="fas fa-user-circle"></i>
                                    <span class="me-2">الحسابات</span>
                                    {% elif app_name == 'common' %}
                                    <i class="fas fa-th"></i>
                                    <span class="me-2">عام</span>
                                    {% elif app_name == 'ElDawliya_sys' or app_name == 'eldawliya_sys' %}
                                    <i class="fas fa-cog"></i>
                                    <span class="me-2">النظام الأساسي</span>
                                    {% elif app_name == 'admin_permissions' %}
                                    <i class="fas fa-lock"></i>
                                    <span class="me-2">صلاحيات المدير</span>
                                    {% else %}
                                    <i class="fas fa-folder"></i>
                                    <span class="me-2">{{ app_name }}</span>
                                    {% endif %}
                                </div>
                                <div class="ms-auto d-flex align-items-center">
                                    <form method="post" action="{% url 'administrator:create_modules_from_paths' %}" class="me-2">
                                        {% csrf_token %}
                                        <input type="hidden" name="app_name" value="{{ app_name }}">
                                        <button type="submit" class="btn btn-sm btn-success" title="إنشاء وحدات من هذا القسم">
                                            <i class="fas fa-magic me-1"></i>
                                            إنشاء الوحدات
                                        </button>
                                    </form>
                                    <span class="badge bg-light text-dark">{{ paths|length }} مسار</span>
                                </div>
                            </div>
                            <div class="path-list">
                                {% for path_item in paths %}
                                <div class="path-item">
                                    <div class="path-name">{{ path_item.name }}</div>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-copy copy-btn" data-path="{{ path_item.path }}" data-bs-toggle="tooltip" title="نسخ المسار"></i>
                                        <div class="path-url">{{ path_item.path }}</div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- No Results Message -->
                <div id="noResults" class="text-center py-5 d-none">
                    <i class="fas fa-search fa-3x mb-3 text-muted"></i>
                    <h5>لا توجد نتائج</h5>
                    <p class="text-muted">لم يتم العثور على مسارات تطابق بحثك</p>
                </div>
            </div>
        </div>
    </div>

    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5>
                    <i class="fas fa-info-circle me-2"></i>
                    إرشادات استخدام المسارات
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="mb-3">كيفية استخدام المسارات:</h6>
                        <ul>
                            <li>انسخ المسار المطلوب واستخدمه في حقل "رابط الوحدة" عند إنشاء وحدة جديدة.</li>
                            <li>تأكد من أن المسار يبدأ بـ / وينتهي بـ / إذا كان يشير إلى صفحة رئيسية.</li>
                            <li>استخدم المسارات الموجودة كمرجع لإنشاء مسارات جديدة.</li>
                            <li>تأكد من أن المسار يتوافق مع القسم الذي تنتمي إليه الوحدة.</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="mb-3">أمثلة على استخدام المسارات:</h6>
                        <ul>
                            <li>لإنشاء وحدة تعرض قائمة الموظفين، استخدم المسار <code>/Hr/employees/</code></li>
                            <li>لإنشاء وحدة لإضافة منتج جديد، استخدم المسار <code>/inventory/products/add/</code></li>
                            <li>لإنشاء وحدة لعرض تقرير المخزون، استخدم المسار <code>/inventory/stock/report/</code></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(function() {
        // تهيئة tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // وظيفة نسخ المسار
        $('.copy-btn').click(function() {
            var path = $(this).data('path');
            navigator.clipboard.writeText(path).then(function() {
                // رسالة النجاح
                Swal.fire({
                    title: 'تم النسخ!',
                    text: 'تم نسخ المسار: ' + path,
                    icon: 'success',
                    timer: 1500,
                    showConfirmButton: false
                });
            });
        });

        // وظيفة البحث
        $('#searchPaths').on('keyup', function() {
            var value = $(this).val().toLowerCase();
            var foundResults = false;

            // إخفاء/إظهار أقسام التطبيق بناءً على البحث
            $('.app-container').each(function() {
                var appHasResults = false;

                // التحقق من كل مسار في هذا التطبيق
                $(this).find('.path-item').each(function() {
                    var pathText = $(this).text().toLowerCase();
                    var pathMatch = pathText.indexOf(value) > -1;

                    $(this).toggle(pathMatch);

                    if (pathMatch) {
                        appHasResults = true;
                        foundResults = true;
                    }
                });

                // إظهار/إخفاء قسم التطبيق بالكامل
                $(this).toggle(appHasResults);
            });

            // إظهار/إخفاء رسالة عدم وجود نتائج
            $('#noResults').toggleClass('d-none', foundResults);
        });
    });
</script>
{% endblock %}
