{% load image_utils %}
{% load form_utils %}
{% load django_permissions %}

<!-- Modern Card View -->
<div id="cardView" class="employee-cards-container p-4" style="display: none;">
    <div class="row g-4">
        {% for employee in employees %}
        <div class="col-xl-4 col-lg-6 col-md-6">
            <div class="employee-card modern-employee-card h-100" 
                 data-emp-id="{{ employee.emp_id }}"
                 data-emp-name="{{ employee.emp_full_name|default:employee.emp_first_name }}"
                 data-dept="{{ employee.department.dept_name|default:'' }}"
                 data-condition="{{ employee.working_condition|default:'' }}">
                <div class="card border-0 shadow-sm h-100 hover-lift">
                    <div class="card-body p-4">
                        <!-- Employee Header -->
                        <div class="employee-header d-flex align-items-center mb-3">
                            <div class="employee-avatar me-3">
                                {% if employee.emp_image %}
                                <img src="{{ employee.emp_image|binary_to_img }}" 
                                     alt="{{ employee.emp_full_name }}"
                                     class="rounded-circle employee-card-img" 
                                     width="60" height="60">
                                {% else %}
                                <div class="avatar-placeholder bg-primary text-white rounded-circle d-flex align-items-center justify-content-center"
                                     style="width: 60px; height: 60px; font-size: 1.5rem; font-weight: bold;">
                                    {{ employee.emp_first_name|slice:":1"|upper }}
                                </div>
                                {% endif %}
                            </div>
                            <div class="employee-info flex-grow-1">
                                <h6 class="employee-name mb-1 fw-bold text-dark">
                                    {{ employee.emp_full_name|default:employee.emp_first_name }}
                                </h6>
                                <div class="employee-id">
                                    <span class="badge bg-light text-dark border">
                                        <i class="fas fa-hashtag me-1"></i>{{ employee.emp_id }}
                                    </span>
                                </div>
                            </div>
                            <div class="employee-status">
                                {% if employee.working_condition == 'سارى' %}
                                <span class="status-indicator bg-success"></span>
                                {% elif employee.working_condition == 'منقطع عن العمل' %}
                                <span class="status-indicator bg-warning"></span>
                                {% elif employee.working_condition == 'استقالة' %}
                                <span class="status-indicator bg-danger"></span>
                                {% else %}
                                <span class="status-indicator bg-secondary"></span>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Employee Details -->
                        <div class="employee-details">
                            <div class="detail-row d-flex align-items-center mb-2">
                                <i class="fas fa-building text-primary me-2" style="width: 16px;"></i>
                                <span class="text-muted small">القسم:</span>
                                <span class="ms-auto fw-medium">
                                    {{ employee.department.dept_name|default:"-" }}
                                </span>
                            </div>
                            <div class="detail-row d-flex align-items-center mb-2">
                                <i class="fas fa-briefcase text-primary me-2" style="width: 16px;"></i>
                                <span class="text-muted small">الوظيفة:</span>
                                <span class="ms-auto fw-medium">
                                    {{ employee.jop_name|default:"-" }}
                                </span>
                            </div>
                            {% if employee.emp_phone1 %}
                            <div class="detail-row d-flex align-items-center mb-2">
                                <i class="fas fa-phone text-primary me-2" style="width: 16px;"></i>
                                <span class="text-muted small">الهاتف:</span>
                                <a href="tel:{{ employee.emp_phone1 }}" 
                                   class="ms-auto text-decoration-none fw-medium">
                                    {{ employee.emp_phone1 }}
                                </a>
                            </div>
                            {% endif %}
                            {% if employee.national_id %}
                            <div class="detail-row d-flex align-items-center mb-3">
                                <i class="fas fa-id-card text-primary me-2" style="width: 16px;"></i>
                                <span class="text-muted small">الرقم القومي:</span>
                                <span class="ms-auto fw-medium small">
                                    {{ employee.national_id }}
                                </span>
                            </div>
                            {% endif %}
                        </div>

                        <!-- Working Status Badge -->
                        <div class="employee-status-badge mb-3">
                            {% if employee.working_condition == 'سارى' %}
                            <span class="badge bg-success-subtle text-success border border-success-subtle px-3 py-2 w-100">
                                <i class="fas fa-check-circle me-1"></i>نشط
                            </span>
                            {% elif employee.working_condition == 'منقطع عن العمل' %}
                            <span class="badge bg-warning-subtle text-warning border border-warning-subtle px-3 py-2 w-100">
                                <i class="fas fa-pause-circle me-1"></i>منقطع عن العمل
                            </span>
                            {% elif employee.working_condition == 'استقالة' %}
                            <span class="badge bg-danger-subtle text-danger border border-danger-subtle px-3 py-2 w-100">
                                <i class="fas fa-times-circle me-1"></i>استقالة
                            </span>
                            {% else %}
                            <span class="badge bg-secondary-subtle text-secondary border border-secondary-subtle px-3 py-2 w-100">
                                <i class="fas fa-question-circle me-1"></i>{{ employee.working_condition|default:"-" }}
                            </span>
                            {% endif %}
                        </div>

                        <!-- Action Buttons -->
                        <div class="employee-actions">
                            <div class="btn-group w-100" role="group">
                                <a href="{% url 'Hr:employees:detail' employee.emp_id %}"
                                   class="btn btn-outline-primary btn-sm flex-fill" 
                                   title="عرض التفاصيل">
                                    <i class="fas fa-eye me-1"></i>عرض
                                </a>
                                {% if perms.Hr.change_employee or user|is_admin %}
                                <a href="{% url 'Hr:employees:edit' employee.emp_id %}"
                                   class="btn btn-primary btn-sm flex-fill" 
                                   title="تعديل">
                                    <i class="fas fa-edit me-1"></i>تعديل
                                </a>
                                {% endif %}
                                <div class="btn-group" role="group">
                                    <button type="button" 
                                            class="btn btn-outline-secondary btn-sm dropdown-toggle"
                                            data-bs-toggle="dropdown" 
                                            aria-expanded="false" 
                                            title="المزيد">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        <li>
                                            <a class="dropdown-item" href="{% url 'Hr:employees:detail' employee.emp_id %}">
                                                <i class="fas fa-id-card me-2 text-primary"></i>البطاقة الشخصية
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="{% url 'Hr:employees:print' employee.emp_id %}">
                                                <i class="fas fa-print me-2 text-secondary"></i>طباعة البيانات
                                            </a>
                                        </li>
                                        {% if perms.Hr.delete_employee or user|is_admin %}
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <button class="dropdown-item text-danger delete-employee"
                                                    data-employee-id="{{ employee.emp_id }}"
                                                    data-employee-name="{{ employee.emp_full_name }}">
                                                <i class="fas fa-trash me-2"></i>حذف
                                            </button>
                                        </li>
                                        {% endif %}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <div class="empty-state">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد موظفين</h5>
                    <p class="text-muted">لم يتم العثور على أي موظفين مطابقين للمعايير المحددة</p>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<style>
.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
}

.hover-lift {
    transition: all 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

.employee-card {
    cursor: pointer;
    transition: all 0.3s ease;
}

.detail-row {
    font-size: 0.9rem;
}

.employee-actions .btn {
    font-size: 0.85rem;
}

.empty-state {
    padding: 3rem 1rem;
}
</style>
