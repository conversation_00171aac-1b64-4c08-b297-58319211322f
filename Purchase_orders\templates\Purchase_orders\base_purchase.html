{% load static %}
{% load i18n %}
{% load django_permissions %}
<!DOCTYPE html>
<html lang="{{ current_language }}" dir="{{ text_direction }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{% trans "نظام طلبات الشراء" %}{% endblock %}</title>

    <!-- Bootstrap RTL/LTR -->
    {% if text_direction == 'rtl' %}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    {% else %}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    {% endif %}

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/purchase_orders.css' %}">

    {% block extra_css %}{% endblock %}
</head>
<body class="bg-light">
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{% url 'Purchase_orders:dashboard' %}">
                <i class="fas fa-shopping-cart me-2"></i>{% trans "نظام طلبات الشراء" %}
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    {% if perms.Purchase_orders.view_dashboard or user|is_admin %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'Purchase_orders:dashboard' %}">
                            <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                        </a>
                    </li>
                    {% endif %}

                    {% if perms.Purchase_orders.view_purchaserequest or user|is_admin %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'Purchase_orders:purchase_request_list' %}">
                            <i class="fas fa-list"></i> قائمة الطلبات
                        </a>
                    </li>
                    {% endif %}

                    {% if perms.Purchase_orders.add_purchaserequest or user|is_admin %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'Purchase_orders:create_purchase_request' %}">
                            <i class="fas fa-plus"></i> طلب شراء جديد
                        </a>
                    </li>
                    {% endif %}
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'inventory:dashboard' %}">
                            <i class="fas fa-warehouse me-1"></i>{% trans "المخزون" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'accounts:logout' %}">
                            <i class="fas fa-sign-out-alt me-1"></i>{% trans "تسجيل الخروج" %}
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        {% if messages %}
        <div class="messages">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        {% block content %}{% endblock %}
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0">{% trans "حقوق النشر © 2025 نظام الدولية. جميع الحقوق محفوظة." %}</p>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- Custom JS -->
    <script src="{% static 'js/purchase_orders.js' %}"></script>

    {% block extra_js %}{% endblock %}
</body>
</html>
