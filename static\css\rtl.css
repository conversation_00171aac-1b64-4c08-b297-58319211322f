/* 
* ElDawliya System - RTL Specific CSS
* Estilos específicos para la dirección de derecha a izquierda (RTL)
*/

/* Ajustes generales para RTL */
body {
    direction: rtl;
    text-align: right;
}

/* Ajustes de márgenes y paddings */
.me-1, .mx-1 {
    margin-left: 0.25rem !important;
    margin-right: 0 !important;
}

.me-2, .mx-2 {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
}

.me-3, .mx-3 {
    margin-left: 1rem !important;
    margin-right: 0 !important;
}

.me-4, .mx-4 {
    margin-left: 1.5rem !important;
    margin-right: 0 !important;
}

.me-5, .mx-5 {
    margin-left: 3rem !important;
    margin-right: 0 !important;
}

.ms-1, .mx-1 {
    margin-right: 0.25rem !important;
    margin-left: 0 !important;
}

.ms-2, .mx-2 {
    margin-right: 0.5rem !important;
    margin-left: 0 !important;
}

.ms-3, .mx-3 {
    margin-right: 1rem !important;
    margin-left: 0 !important;
}

.ms-4, .mx-4 {
    margin-right: 1.5rem !important;
    margin-left: 0 !important;
}

.ms-5, .mx-5 {
    margin-right: 3rem !important;
    margin-left: 0 !important;
}

.pe-1, .px-1 {
    padding-left: 0.25rem !important;
    padding-right: 0 !important;
}

.pe-2, .px-2 {
    padding-left: 0.5rem !important;
    padding-right: 0 !important;
}

.pe-3, .px-3 {
    padding-left: 1rem !important;
    padding-right: 0 !important;
}

.pe-4, .px-4 {
    padding-left: 1.5rem !important;
    padding-right: 0 !important;
}

.pe-5, .px-5 {
    padding-left: 3rem !important;
    padding-right: 0 !important;
}

.ps-1, .px-1 {
    padding-right: 0.25rem !important;
    padding-left: 0 !important;
}

.ps-2, .px-2 {
    padding-right: 0.5rem !important;
    padding-left: 0 !important;
}

.ps-3, .px-3 {
    padding-right: 1rem !important;
    padding-left: 0 !important;
}

.ps-4, .px-4 {
    padding-right: 1.5rem !important;
    padding-left: 0 !important;
}

.ps-5, .px-5 {
    padding-right: 3rem !important;
    padding-left: 0 !important;
}

/* Ajustes para alineación de texto */
.text-start {
    text-align: right !important;
}

.text-end {
    text-align: left !important;
}

/* Ajustes para bordes */
.border-start {
    border-right: 1px solid #dee2e6 !important;
    border-left: 0 !important;
}

.border-end {
    border-left: 1px solid #dee2e6 !important;
    border-right: 0 !important;
}

/* Ajustes para formularios */
.form-check {
    padding-right: 1.5em;
    padding-left: 0;
}

.form-check .form-check-input {
    float: right;
    margin-right: -1.5em;
    margin-left: 0;
}

.form-select {
    background-position: left 0.75rem center;
    padding-right: 0.75rem;
    padding-left: 2.25rem;
}

/* Ajustes para grupos de entrada */
.input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
    margin-right: -1px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.input-group:not(.has-validation) > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

/* Ajustes para menús desplegables */
.dropdown-menu {
    text-align: right;
}

.dropdown-item {
    text-align: right;
}

/* Ajustes para iconos en botones */
.btn i {
    margin-left: 0.5rem;
    margin-right: 0;
}

/* Ajustes para tablas */
.table th, .table td {
    text-align: right;
}

/* Ajustes para tarjetas */
.card-header .float-end {
    float: left !important;
}

.card-header .float-start {
    float: right !important;
}

/* Ajustes para navegación */
.navbar-nav {
    padding-right: 0;
}

.navbar .dropdown-menu {
    right: 0;
    left: auto;
}

/* Ajustes para breadcrumbs */
.breadcrumb-item + .breadcrumb-item::before {
    float: right;
    padding-left: 0.5rem;
    padding-right: 0;
}

/* Ajustes para modales */
.modal-header .btn-close {
    margin: -0.5rem auto -0.5rem -0.5rem;
}

/* Ajustes para alertas */
.alert-dismissible {
    padding-right: 1rem;
    padding-left: 4rem;
}

.alert-dismissible .btn-close {
    right: auto;
    left: 0;
}

/* Ajustes para listas */
ul, ol {
    padding-right: 2rem;
    padding-left: 0;
}

/* Ajustes para acordeones */
.accordion-button::after {
    margin-left: 0;
    margin-right: auto;
}

/* Ajustes para paginación */
.pagination {
    padding-right: 0;
}

/* Ajustes para tooltips y popovers */
.tooltip, .popover {
    text-align: right;
}

/* Ajustes para barras de progreso */
.progress-bar {
    float: right;
}

/* Ajustes para spinners */
.spinner-border, .spinner-grow {
    margin-left: 0.5rem;
    margin-right: 0;
}

/* Ajustes para botones de cierre */
.btn-close {
    margin-left: 0;
    margin-right: auto;
}

/* Ajustes para componentes personalizados */
.timeline::before {
    right: calc(24px / 2);
    left: auto;
}

.timeline-item {
    padding-right: 40px;
    padding-left: 0;
}

.timeline-badge {
    right: 0;
    left: auto;
}

/* Ajustes para el botón de volver arriba */
#scrollToTop {
    left: 20px;
    right: auto;
}

/* Ajustes para tarjetas de tareas */
.task-card {
    border-right: 5px solid var(--pending);
    border-left: none;
}
