# Generated by Django 5.0.14 on 2025-05-07 05:28

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('Purchase_orders', '0001_initial'),
        ('inventory', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='purchaserequest',
            name='approved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_purchase_requests', to=settings.AUTH_USER_MODEL, verbose_name='تمت الموافقة بواسطة'),
        ),
        migrations.AddField(
            model_name='purchaserequest',
            name='requested_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='purchase_requests', to=settings.AUTH_USER_MODEL, verbose_name='مقدم الطلب'),
        ),
        migrations.AddField(
            model_name='purchaserequestitem',
            name='product',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.tblproducts', verbose_name='الصنف'),
        ),
        migrations.AddField(
            model_name='purchaserequestitem',
            name='purchase_request',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='Purchase_orders.purchaserequest', verbose_name='طلب الشراء'),
        ),
    ]
