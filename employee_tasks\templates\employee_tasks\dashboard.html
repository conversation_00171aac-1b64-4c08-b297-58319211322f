{% extends 'employee_tasks/base_employee_tasks.html' %}

{% block title %}لوحة تحكم مهام الموظفين - نظام الدولية{% endblock %}

{% block extra_css %}
<style>
    /* Clickable card styles */
    .stat-card {
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }
    
    /* Modal styles */
    .tasks-modal-title {
        font-weight: bold;
    }
    
    .modal-header.primary {
        background-color: var(--primary-color);
        color: white;
    }
    
    .modal-header.warning {
        background-color: var(--warning-color);
        color: white;
    }
    
    .modal-header.info {
        background-color: var(--info-color);
        color: white;
    }
    
    .modal-header.success {
        background-color: var(--success-color);
        color: white;
    }
    
    .modal-header.danger {
        background-color: var(--danger-color);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-0 text-gray-800">لوحة تحكم مهام الموظفين</h1>
            <p class="text-muted">مرحباً بك في نظام إدارة مهام الموظفين</p>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3 col-sm-6">
            <div class="stat-card primary" data-status="all" data-title="إجمالي المهام">
                <div class="stat-number">{{ total_tasks }}</div>
                <div class="stat-title">إجمالي المهام</div>
                <div class="stat-icon">
                    <i class="fas fa-tasks"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6">
            <div class="stat-card warning" data-status="pending" data-title="مهام قيد الانتظار">
                <div class="stat-number">{{ pending_tasks }}</div>
                <div class="stat-title">مهام قيد الانتظار</div>
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6">
            <div class="stat-card info" data-status="in_progress" data-title="مهام قيد التنفيذ">
                <div class="stat-number">{{ in_progress_tasks }}</div>
                <div class="stat-title">مهام قيد التنفيذ</div>
                <div class="stat-icon">
                    <i class="fas fa-spinner"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6">
            <div class="stat-card success" data-status="completed" data-title="مهام مكتملة">
                <div class="stat-number">{{ completed_tasks }}</div>
                <div class="stat-title">مهام مكتملة</div>
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Tasks -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">المهام الحديثة</h5>
                    <a href="{% url 'employee_tasks:task_list' %}" class="btn btn-sm btn-primary">
                        <i class="fas fa-list me-1"></i> عرض الكل
                    </a>
                </div>
                <div class="card-body">
                    {% if recent_tasks %}
                        <div class="list-group">
                            {% for task in recent_tasks %}
                                <a href="{% url 'employee_tasks:task_detail' task.pk %}" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">{{ task.title }}</h6>
                                        <small>{{ task.created_at|date:"Y-m-d" }}</small>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <small class="text-muted">
                                                <i class="fas fa-user me-1"></i> {{ task.created_by.username }}
                                            </small>
                                            {% if task.assigned_to %}
                                                <small class="text-muted ms-2">
                                                    <i class="fas fa-user-check me-1"></i> {{ task.assigned_to.username }}
                                                </small>
                                            {% endif %}
                                        </div>
                                        <span class="badge badge-{{ task.status }}">
                                            {{ task.get_status_display }}
                                        </span>
                                    </div>
                                    <div class="progress mt-2" style="height: 5px;">
                                        <div class="progress-bar bg-{{ task.priority }}" role="progressbar" style="width: {{ task.progress }}%;" aria-valuenow="{{ task.progress }}" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </a>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            لا توجد مهام حديثة.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Upcoming Tasks -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">المهام القادمة</h5>
                    <a href="{% url 'employee_tasks:calendar' %}" class="btn btn-sm btn-primary">
                        <i class="fas fa-calendar-alt me-1"></i> التقويم
                    </a>
                </div>
                <div class="card-body">
                    {% if upcoming_tasks %}
                        <div class="list-group">
                            {% for task in upcoming_tasks %}
                                <a href="{% url 'employee_tasks:task_detail' task.pk %}" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">{{ task.title }}</h6>
                                        <small>{{ task.due_date|date:"Y-m-d" }}</small>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <small class="text-muted">
                                                <i class="fas fa-user me-1"></i> {{ task.created_by.username }}
                                            </small>
                                            {% if task.assigned_to %}
                                                <small class="text-muted ms-2">
                                                    <i class="fas fa-user-check me-1"></i> {{ task.assigned_to.username }}
                                                </small>
                                            {% endif %}
                                        </div>
                                        <span class="badge badge-{{ task.priority }}">
                                            {{ task.get_priority_display }}
                                        </span>
                                    </div>
                                    <div class="progress mt-2" style="height: 5px;">
                                        <div class="progress-bar bg-{{ task.priority }}" role="progressbar" style="width: {{ task.progress }}%;" aria-valuenow="{{ task.progress }}" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </a>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            لا توجد مهام قادمة.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <!-- Overdue Tasks -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        المهام المتأخرة ({{ overdue_tasks }})
                    </h5>
                </div>
                <div class="card-body">
                    {% if overdue_tasks > 0 %}
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            لديك {{ overdue_tasks }} مهمة متأخرة. يرجى مراجعة المهام وتحديث حالتها.
                        </div>
                        <a href="{% url 'employee_tasks:task_list' %}?status=pending" class="btn btn-danger">
                            <i class="fas fa-search me-1"></i> عرض المهام المتأخرة
                        </a>
                    {% else %}
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            ليس لديك أي مهام متأخرة. أحسنت!
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Categories -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">التصنيفات</h5>
                    <a href="{% url 'employee_tasks:category_list' %}" class="btn btn-sm btn-primary">
                        <i class="fas fa-tags me-1"></i> عرض الكل
                    </a>
                </div>
                <div class="card-body">
                    {% if categories %}
                        <div class="list-group">
                            {% for category in categories %}
                                <a href="{% url 'employee_tasks:task_list' %}?category={{ category.id }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="{{ category.icon }}" style="color: {{ category.color }};"></i>
                                        <span class="ms-2">{{ category.name }}</span>
                                    </div>
                                    <span class="badge bg-primary rounded-pill">{{ category.task_count }}</span>
                                </a>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            لا توجد تصنيفات.
                        </div>
                        <a href="{% url 'employee_tasks:category_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i> إنشاء تصنيف جديد
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">إجراءات سريعة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="{% url 'employee_tasks:task_create' %}" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-plus-circle me-2"></i> إنشاء مهمة جديدة
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="{% url 'employee_tasks:my_tasks' %}" class="btn btn-info btn-lg w-100">
                                <i class="fas fa-user-check me-2"></i> عرض مهامي
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="{% url 'employee_tasks:calendar' %}" class="btn btn-success btn-lg w-100">
                                <i class="fas fa-calendar-alt me-2"></i> عرض التقويم
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="{% url 'employee_tasks:analytics' %}" class="btn btn-warning btn-lg w-100">
                                <i class="fas fa-chart-bar me-2"></i> عرض التحليلات
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Tasks Modal -->
    <div class="modal fade" id="tasksModal" tabindex="-1" aria-labelledby="tasksModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title tasks-modal-title" id="tasksModalLabel">قائمة المهام</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="tasks-loading" class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                        <p class="mt-2">جاري تحميل المهام...</p>
                    </div>
                    <div id="tasks-container" class="d-none">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>العنوان</th>
                                        <th>التصنيف</th>
                                        <th>الحالة</th>
                                        <th>الأولوية</th>
                                        <th>تاريخ الاستحقاق</th>
                                        <th>التقدم</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="tasks-table-body">
                                    <!-- سيتم ملء هذا الجزء ديناميكيًا -->
                                </tbody>
                            </table>
                        </div>
                        <div id="no-tasks-message" class="alert alert-info d-none">
                            لا توجد مهام للعرض
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // إعداد نافذة المهام
    const tasksModal = new bootstrap.Modal(document.getElementById('tasksModal'));
    let currentStatus = null;
    let modalTitle = document.getElementById('tasksModalLabel');
    let modalHeader = document.querySelector('.modal-header');
    const taskContainer = document.getElementById('tasks-container');
    const tasksLoading = document.getElementById('tasks-loading');
    const tasksTableBody = document.getElementById('tasks-table-body');
    const noTasksMessage = document.getElementById('no-tasks-message');
    
    // إضافة معالجات الأحداث لبطاقات الإحصاءات
    document.querySelectorAll('.stat-card').forEach(card => {
        card.addEventListener('click', function() {
            const status = this.getAttribute('data-status');
            const title = this.getAttribute('data-title');
            
            // تعيين العنوان وفئة الرأس للنافذة المنبثقة
            modalTitle.textContent = title;
            modalHeader.className = 'modal-header ' + this.classList[1]; // نفس لون البطاقة
            
            // إعادة تعيين المتغيرات
            currentStatus = status;
            
            // عرض النافذة المنبثقة وتحميل المهام
            tasksModal.show();
            loadTasks();
        });
    });
    
    // وظيفة لتحميل المهام بناءً على المعايير المحددة
    function loadTasks() {
        // إظهار شاشة التحميل
        tasksLoading.classList.remove('d-none');
        taskContainer.classList.add('d-none');
        tasksTableBody.innerHTML = '';
        
        // تحضير معلمات URL
        let url = '{% url "employee_tasks:task_list" %}?';
        let params = [];
        
        // إضافة معلمات البحث
        if (currentStatus === 'pending') {
            params.push('status=pending');
        } else if (currentStatus === 'in_progress') {
            params.push('status=in_progress');
        } else if (currentStatus === 'completed') {
            params.push('status=completed');
        }
        
        // إنشاء URL النهائي
        if (params.length > 0) {
            url += params.join('&');
        }
        
        // تحميل المهام باستخدام fetch
        fetch(url)
            .then(response => response.text())
            .then(html => {
                // إخفاء شاشة التحميل
                tasksLoading.classList.add('d-none');
                taskContainer.classList.remove('d-none');
                
                // تحليل HTML المستجاب
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                
                // البحث عن جدول المهام في الاستجابة
                const taskRows = doc.querySelectorAll('table tbody tr');
                
                if (taskRows.length > 0) {
                    // إضافة صفوف المهام إلى الجدول
                    taskRows.forEach(row => {
                        tasksTableBody.appendChild(row.cloneNode(true));
                    });
                    
                    // إظهار الجدول وإخفاء رسالة "لا توجد مهام"
                    noTasksMessage.classList.add('d-none');
                } else {
                    // إظهار رسالة "لا توجد مهام"
                    noTasksMessage.classList.remove('d-none');
                }
            })
            .catch(error => {
                console.error('Error loading tasks:', error);
                tasksLoading.classList.add('d-none');
                taskContainer.classList.remove('d-none');
                noTasksMessage.textContent = 'حدث خطأ أثناء تحميل المهام. يرجى المحاولة مرة أخرى.';
                noTasksMessage.classList.remove('d-none');
            });
    }
</script>
{% endblock %}
