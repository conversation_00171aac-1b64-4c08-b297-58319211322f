{% extends 'Hr/base_hr.html' %}
{% load static %}

{% block title %}{{ page_title }} - نظام الدولية{% endblock %}

{% block extra_css %}
<style>
    @media print {
        .no-print {
            display: none !important;
        }
        .print-area {
            padding: 20px;
        }
        .table td, .table th {
            border-color: #000 !important;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container print-area">
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <h4>قسيمة راتب</h4>
                <div class="no-print">
                    <button type="button" class="btn btn-primary" onclick="window.print()">
                        <i class="fas fa-print me-1"></i> طباعة
                    </button>
                    <a href="{% url 'Hr:payroll_entry_detail' payroll_entry.id %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-1"></i> عودة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-sm">
                        <tbody>
                            <tr>
                                <th style="width: 40%">اسم الموظف:</th>
                                <td>{{ payroll_entry.employee.emp_full_name }}</td>
                            </tr>
                            <tr>
                                <th>الرقم الوظيفي:</th>
                                <td>{{ payroll_entry.employee.emp_id }}</td>
                            </tr>
                            <tr>
                                <th>القسم:</th>
                                <td>{{ payroll_entry.employee.department.name }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-sm">
                        <tbody>
                            <tr>
                                <th style="width: 40%">فترة الراتب:</th>
                                <td>{{ payroll_entry.period.period|date:"Y-m" }}</td>
                            </tr>
                            <tr>
                                <th>تاريخ الإصدار:</th>
                                <td>{{ payroll_entry.created_at|date:"Y-m-d" }}</td>
                            </tr>
                            <tr>
                                <th>الحالة:</th>
                                <td>{{ payroll_entry.get_status_display }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header">
                    <h6 class="mb-0">المستحقات</h6>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tbody>
                            <tr>
                                <th>الراتب الأساسي</th>
                                <td class="text-end">{{ payroll_entry.basic_salary|floatformat:2 }}</td>
                            </tr>
                            {% for item in payroll_items %}
                                {% if item.salary_item.type == 'addition' %}
                                <tr>
                                    <th>{{ item.salary_item.name }}</th>
                                    <td class="text-end">{{ item.amount|floatformat:2 }}</td>
                                </tr>
                                {% endif %}
                            {% endfor %}
                            <tr>
                                <th>العمل الإضافي</th>
                                <td class="text-end">{{ payroll_entry.overtime|floatformat:2 }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header">
                    <h6 class="mb-0">الاستقطاعات</h6>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tbody>
                            {% for item in payroll_items %}
                                {% if item.salary_item.type == 'deduction' %}
                                <tr>
                                    <th>{{ item.salary_item.name }}</th>
                                    <td class="text-end">{{ item.amount|floatformat:2 }}</td>
                                </tr>
                                {% endif %}
                            {% endfor %}
                            <tr>
                                <th>الجزاءات</th>
                                <td class="text-end">{{ payroll_entry.penalties|floatformat:2 }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="card mt-4">
        <div class="card-body">
            <table class="table table-sm">
                <tbody>
                    <tr>
                        <th style="width: 30%">إجمالي المستحقات</th>
                        <td class="text-end">{{ payroll_entry.allowances|floatformat:2 }}</td>
                    </tr>
                    <tr>
                        <th>إجمالي الاستقطاعات</th>
                        <td class="text-end">{{ payroll_entry.deductions|floatformat:2 }}</td>
                    </tr>
                    <tr class="table-dark">
                        <th>صافي الراتب</th>
                        <td class="text-end fw-bold">{{ payroll_entry.total_salary|floatformat:2 }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="row mt-5">
        <div class="col-md-4 text-center">
            <p>_________________</p>
            <p>توقيع الموظف</p>
        </div>
        <div class="col-md-4 text-center">
            <p>_________________</p>
            <p>مدير الموارد البشرية</p>
        </div>
        <div class="col-md-4 text-center">
            <p>_________________</p>
            <p>المدير المالي</p>
        </div>
    </div>
</div>
{% endblock %}