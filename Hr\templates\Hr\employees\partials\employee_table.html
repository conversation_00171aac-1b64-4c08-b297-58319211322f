{% load image_utils %}
{% load form_utils %}
{% load django_permissions %}

<!-- Enhanced Table View -->
<div id="tableView" class="table-responsive">
    <table class="table table-hover align-middle mb-0 modern-table" id="employeesTable">
        <thead class="table-light sticky-top modern-table-header">
            <tr>
                <th class="py-4 px-4 sortable border-0" data-sort="emp_id">
                    <div class="d-flex align-items-center fw-semibold">
                        <i class="fas fa-hashtag me-2 text-primary"></i>
                        الرقم
                        <i class="fas fa-sort ms-2 text-muted"></i>
                    </div>
                </th>
                <th class="py-4 px-4 sortable border-0" data-sort="emp_full_name">
                    <div class="d-flex align-items-center fw-semibold">
                        <i class="fas fa-user me-2 text-primary"></i>
                        الموظف
                        <i class="fas fa-sort ms-2 text-muted"></i>
                    </div>
                </th>
                <th class="py-4 px-4 sortable border-0" data-sort="department">
                    <div class="d-flex align-items-center fw-semibold">
                        <i class="fas fa-building me-2 text-primary"></i>
                        القسم
                        <i class="fas fa-sort ms-2 text-muted"></i>
                    </div>
                </th>
                <th class="py-4 px-4 border-0">
                    <div class="d-flex align-items-center fw-semibold">
                        <i class="fas fa-briefcase me-2 text-primary"></i>
                        الوظيفة
                    </div>
                </th>
                <th class="py-4 px-4 border-0">
                    <div class="d-flex align-items-center fw-semibold">
                        <i class="fas fa-phone me-2 text-primary"></i>
                        الهاتف
                    </div>
                </th>
                <th class="py-4 px-4 sortable border-0" data-sort="working_condition">
                    <div class="d-flex align-items-center fw-semibold">
                        <i class="fas fa-user-check me-2 text-primary"></i>
                        الحالة
                        <i class="fas fa-sort ms-2 text-muted"></i>
                    </div>
                </th>
                <th class="py-4 px-4 border-0 text-center">
                    <div class="d-flex align-items-center justify-content-center fw-semibold">
                        <i class="fas fa-cogs me-2 text-primary"></i>
                        العمليات
                    </div>
                </th>
            </tr>
        </thead>
        <tbody>
            {% for employee in employees %}
            <tr class="employee-row modern-table-row" 
                data-emp-id="{{ employee.emp_id }}"
                data-emp-name="{{ employee.emp_full_name|default:employee.emp_first_name }}"
                data-dept="{{ employee.department.dept_name|default:'' }}"
                data-condition="{{ employee.working_condition|default:'' }}">
                
                <!-- Employee ID -->
                <td class="py-3 px-4">
                    <div class="d-flex align-items-center">
                        <span class="badge bg-primary-subtle text-primary fw-bold px-3 py-2 rounded-pill">
                            #{{ employee.emp_id }}
                        </span>
                    </div>
                </td>

                <!-- Employee Info -->
                <td class="py-3 px-4">
                    <div class="d-flex align-items-center">
                        <div class="employee-avatar me-3">
                            {% if employee.emp_image %}
                            <img src="{{ employee.emp_image|binary_to_img }}" 
                                 alt="{{ employee.emp_full_name }}"
                                 class="rounded-circle employee-table-img" 
                                 width="45" height="45">
                            {% else %}
                            <div class="avatar-placeholder bg-gradient-primary text-white rounded-circle d-flex align-items-center justify-content-center"
                                 style="width: 45px; height: 45px; font-size: 1.2rem; font-weight: bold;">
                                {{ employee.emp_first_name|slice:":1"|upper }}
                            </div>
                            {% endif %}
                        </div>
                        <div class="employee-info">
                            <div class="employee-name fw-bold text-dark mb-1">
                                {{ employee.emp_full_name|default:employee.emp_first_name }}
                            </div>
                            {% if employee.national_id %}
                            <small class="text-muted">
                                <i class="fas fa-id-card me-1"></i>
                                {{ employee.national_id }}
                            </small>
                            {% endif %}
                        </div>
                    </div>
                </td>

                <!-- Department -->
                <td class="py-3 px-4">
                    {% if employee.department %}
                    <div class="department-info">
                        <span class="badge bg-info-subtle text-info px-3 py-2 rounded-pill">
                            <i class="fas fa-building me-1"></i>
                            {{ employee.department.dept_name }}
                        </span>
                    </div>
                    {% else %}
                    <span class="text-muted">-</span>
                    {% endif %}
                </td>

                <!-- Job -->
                <td class="py-3 px-4">
                    {% if employee.jop_name %}
                    <div class="job-info">
                        <span class="text-dark fw-medium">{{ employee.jop_name }}</span>
                    </div>
                    {% else %}
                    <span class="text-muted">-</span>
                    {% endif %}
                </td>

                <!-- Phone -->
                <td class="py-3 px-4">
                    {% if employee.emp_phone1 %}
                    <a href="tel:{{ employee.emp_phone1 }}" class="text-decoration-none text-primary">
                        <i class="fas fa-phone me-1"></i>
                        {{ employee.emp_phone1 }}
                    </a>
                    {% else %}
                    <span class="text-muted">-</span>
                    {% endif %}
                </td>

                <!-- Working Condition -->
                <td class="py-3 px-4">
                    {% if employee.working_condition == 'سارى' %}
                    <span class="badge bg-success-subtle text-success px-3 py-2 rounded-pill">
                        <i class="fas fa-check-circle me-1"></i>نشط
                    </span>
                    {% elif employee.working_condition == 'منقطع عن العمل' %}
                    <span class="badge bg-warning-subtle text-warning px-3 py-2 rounded-pill">
                        <i class="fas fa-pause-circle me-1"></i>منقطع
                    </span>
                    {% elif employee.working_condition == 'استقالة' %}
                    <span class="badge bg-danger-subtle text-danger px-3 py-2 rounded-pill">
                        <i class="fas fa-times-circle me-1"></i>استقالة
                    </span>
                    {% else %}
                    <span class="badge bg-secondary-subtle text-secondary px-3 py-2 rounded-pill">
                        <i class="fas fa-question-circle me-1"></i>{{ employee.working_condition|default:"-" }}
                    </span>
                    {% endif %}
                </td>

                <!-- Actions -->
                <td class="py-3 px-4 text-center">
                    <div class="btn-group" role="group">
                        <a href="{% url 'Hr:employees:detail' employee.emp_id %}" 
                           class="btn btn-outline-primary btn-sm action-btn" 
                           title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </a>
                        {% if perms.Hr.change_employee or user|is_admin %}
                        <a href="{% url 'Hr:employees:edit' employee.emp_id %}" 
                           class="btn btn-primary btn-sm action-btn" 
                           title="تعديل">
                            <i class="fas fa-edit"></i>
                        </a>
                        {% endif %}
                        <div class="btn-group" role="group">
                            <button type="button" 
                                    class="btn btn-outline-secondary btn-sm dropdown-toggle action-btn" 
                                    data-bs-toggle="dropdown" 
                                    aria-expanded="false" 
                                    title="المزيد">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end shadow">
                                <li>
                                    <a class="dropdown-item" href="{% url 'Hr:employees:detail' employee.emp_id %}">
                                        <i class="fas fa-id-card me-2 text-primary"></i>البطاقة الشخصية
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{% url 'Hr:employees:print' employee.emp_id %}">
                                        <i class="fas fa-print me-2 text-secondary"></i>طباعة البيانات
                                    </a>
                                </li>
                                {% if perms.Hr.delete_employee or user|is_admin %}
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <button class="dropdown-item text-danger delete-employee"
                                            data-employee-id="{{ employee.emp_id }}"
                                            data-employee-name="{{ employee.emp_full_name }}">
                                        <i class="fas fa-trash me-2"></i>حذف
                                    </button>
                                </li>
                                {% endif %}
                            </ul>
                        </div>
                    </div>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="text-center py-5">
                    <div class="empty-state">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد موظفين</h5>
                        <p class="text-muted">لم يتم العثور على أي موظفين مطابقين للمعايير المحددة</p>
                    </div>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
