/*
  HR UI/UX Redesign - Main Stylesheet
  - Responsive Material Design-inspired system
  - WCAG 2.1 accessibility
  - Mobile-first breakpoints
  - Modern color palette and typography
*/
:root {
  --primary: #1976d2;
  --primary-dark: #115293;
  --secondary: #f5f5f5;
  --accent: #ffb300;
  --success: #43a047;
  --danger: #e53935;
  --warning: #fbc02d;
  --info: #0288d1;
  --text: #222;
  --text-light: #fff;
  --border: #e0e0e0;
  --radius: 8px;
  --shadow: 0 2px 8px rgba(0,0,0,0.07);
  --font-main: 'Roboto', 'Cairo', Arial, sans-serif;
}

html {
  font-size: 16px;
  font-family: var(--font-main);
  background: var(--secondary);
  color: var(--text);
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background: var(--secondary);
  color: var(--text);
}

/* Grid System */
.container {
  width: 100%;
  max-width: 1440px;
  margin: 0 auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}
.grid {
  display: grid;
  gap: 1.5rem;
}
@media (min-width: 768px) {
  .grid-2 { grid-template-columns: 1fr 1fr; }
  .grid-3 { grid-template-columns: 1fr 1fr 1fr; }
}
@media (min-width: 1024px) {
  .grid-4 { grid-template-columns: 1fr 1fr 1fr 1fr; }
}

/* Card Component */
.card {
  background: #fff;
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  padding: 1.5rem;
  transition: box-shadow 0.2s;
  border: 1px solid var(--border);
}
.card:focus-within, .card:hover {
  box-shadow: 0 4px 16px rgba(25, 118, 210, 0.12);
}

/* Typography */
h1, .h1 { font-size: 2.25rem; font-weight: 700; margin: 0 0 1rem; }
h2, .h2 { font-size: 1.5rem; font-weight: 600; margin: 0 0 0.75rem; }
h3, .h3 { font-size: 1.15rem; font-weight: 500; margin: 0 0 0.5rem; }
p, .p { font-size: 1rem; line-height: 1.7; margin: 0 0 1rem; }
.label { font-size: 0.95rem; font-weight: 500; color: var(--primary-dark); }

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5em;
  padding: 0.6em 1.2em;
  border-radius: var(--radius);
  border: none;
  background: var(--primary);
  color: var(--text-light);
  font-weight: 500;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.2s, box-shadow 0.2s;
  box-shadow: var(--shadow);
}
.btn:focus, .btn:hover {
  background: var(--primary-dark);
  outline: 2px solid var(--accent);
}
.btn-secondary {
  background: var(--secondary);
  color: var(--primary);
  border: 1px solid var(--primary);
}
.btn-danger {
  background: var(--danger);
}
.btn-success {
  background: var(--success);
}

/* Forms */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1.25rem;
}
input, select, textarea {
  padding: 0.7em 1em;
  border-radius: var(--radius);
  border: 1px solid var(--border);
  font-size: 1rem;
  font-family: var(--font-main);
  background: #fff;
  color: var(--text);
  transition: border 0.2s, box-shadow 0.2s;
}
input:focus, select:focus, textarea:focus {
  border: 1.5px solid var(--primary);
  outline: none;
  box-shadow: 0 0 0 2px var(--accent);
}
label { font-weight: 500; margin-bottom: 0.25rem; }

/* Table */
.table {
  width: 100%;
  border-collapse: collapse;
  background: #fff;
  border-radius: var(--radius);
  overflow: hidden;
  box-shadow: var(--shadow);
}
.table th, .table td {
  padding: 0.9em 1em;
  border-bottom: 1px solid var(--border);
  text-align: left;
}
.table th {
  background: var(--secondary);
  font-weight: 600;
  color: var(--primary-dark);
}
.table tr:last-child td { border-bottom: none; }

/* Animations */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: none; }
}

/* Accessibility */
:focus-visible {
  outline: 2px solid var(--accent);
  outline-offset: 2px;
}
[aria-live] { position: absolute; left: -9999px; top: auto; width: 1px; height: 1px; overflow: hidden; }

/* Responsive Breakpoints */
@media (max-width: 1024px) {
  html { font-size: 15px; }
  .container { padding: 0.5rem; }
}
@media (max-width: 768px) {
  html { font-size: 14px; }
  .grid-2, .grid-3, .grid-4 { grid-template-columns: 1fr; }
  .card { padding: 1rem; }
}
@media (max-width: 480px) {
  html { font-size: 13px; }
  .container { padding: 0.25rem; }
  .card { padding: 0.7rem; }
}

/* Utility */
.mt-2 { margin-top: 2rem; }
.mb-2 { margin-bottom: 2rem; }
.mx-auto { margin-left: auto; margin-right: auto; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-success { color: var(--success); }
.text-danger { color: var(--danger); }
.text-primary { color: var(--primary); }
.text-muted { color: #888; }
