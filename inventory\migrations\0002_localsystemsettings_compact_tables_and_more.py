# Generated by Django 5.0.14 on 2025-05-16 03:02

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='localsystemsettings',
            name='compact_tables',
            field=models.BooleanField(default=False, verbose_name='وضع العرض المضغوط'),
        ),
        migrations.AddField(
            model_name='localsystemsettings',
            name='currency',
            field=models.CharField(default='EGP', max_length=10, verbose_name='العملة'),
        ),
        migrations.AddField(
            model_name='localsystemsettings',
            name='default_min_stock_percentage',
            field=models.IntegerField(default=20, verbose_name='نسبة الحد الأدنى الافتراضية'),
        ),
        migrations.AddField(
            model_name='localsystemsettings',
            name='enable_stock_alerts',
            field=models.BooleanField(default=True, verbose_name='تفعيل تنبيهات المخزون'),
        ),
        migrations.AddField(
            model_name='localsystemsettings',
            name='invoice_in_prefix',
            field=models.CharField(default='IN-', max_length=10, verbose_name='بادئة أرقام فواتير التوريد'),
        ),
        migrations.AddField(
            model_name='localsystemsettings',
            name='invoice_out_prefix',
            field=models.CharField(default='OUT-', max_length=10, verbose_name='بادئة أرقام فواتير الصرف'),
        ),
        migrations.AddField(
            model_name='localsystemsettings',
            name='items_per_page',
            field=models.IntegerField(default=25, verbose_name='عدد العناصر في الصفحة'),
        ),
        migrations.AddField(
            model_name='localsystemsettings',
            name='prevent_editing_completed_invoices',
            field=models.BooleanField(default=True, verbose_name='منع تعديل الفواتير المكتملة'),
        ),
        migrations.AddField(
            model_name='localsystemsettings',
            name='primary_color',
            field=models.CharField(default='#3f51b5', max_length=20, verbose_name='اللون الأساسي'),
        ),
        migrations.AddField(
            model_name='localsystemsettings',
            name='secondary_color',
            field=models.CharField(default='#ff4081', max_length=20, verbose_name='اللون الثانوي'),
        ),
    ]
