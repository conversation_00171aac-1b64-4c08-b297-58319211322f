{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ page_title }} - نظام الدولية{% endblock %}

{% block page_title %}{{ page_title }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموظفين</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:attendance:attendance_machine_list' %}">أجهزة البصمة</a></li>
<li class="breadcrumb-item active">{{ page_title }}</li>
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">جلب بيانات الحضور من جهاز البصمة</h5>
    </div>
    <div class="card-body">
        <form method="post" novalidate>
            {% csrf_token %}

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.machine.id_for_label }}" class="form-label">{{ form.machine.label }}</label>
                        {{ form.machine }}
                        {% if form.machine.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.machine.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.start_date.id_for_label }}" class="form-label">{{ form.start_date.label }}</label>
                        {{ form.start_date }}
                        {% if form.start_date.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.start_date.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.end_date.id_for_label }}" class="form-label">{{ form.end_date.label }}</label>
                        {{ form.end_date }}
                        {% if form.end_date.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.end_date.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.clear_existing }}
                            <label class="form-check-label" for="{{ form.clear_existing.id_for_label }}">
                                {{ form.clear_existing.label }}
                            </label>
                        </div>
                        {% if form.clear_existing.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.clear_existing.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                        <small class="form-text text-muted">{{ form.clear_existing.help_text }}</small>
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-between">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-sync"></i> جلب البيانات
                </button>
                <a href="{% url 'Hr:attendance:attendance_machine_list' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
