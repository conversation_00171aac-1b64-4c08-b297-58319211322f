{% extends 'Hr/base_hr.html' %}
{% load static %}

{% block title %}{{ page_title }} - نظام الدولية{% endblock %}

{% block page_title %}{{ page_title }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموظفين</a></li>
<li class="breadcrumb-item"><a href="{% url 'employees:payroll_period_list' %}">فترات الرواتب</a></li>
<li class="breadcrumb-item active">{{ page_title }}</li>
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">سجلات الرواتب</h5>
    </div>
    <div class="card-body">
        <div class="row mb-4">
            <div class="col-md-6">
                <form method="get" class="row g-3">
                    <div class="col-md-5">
                        <label for="payroll_period" class="form-label">فترة الراتب</label>
                        <select name="payroll_period" id="payroll_period" class="form-select">
                            <option value="">-- الكل --</option>
                            {% for period in payroll_periods %}
                            <option value="{{ period.id }}" {% if selected_period == period.id|stringformat:"i" %}selected{% endif %}>
                                {{ period.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-5">
                        <label for="employee" class="form-label">الموظف</label>
                        <select name="employee" id="employee" class="form-select">
                            <option value="">-- الكل --</option>
                            {% for emp in employees %}
                            <option value="{{ emp.emp_id }}" {% if selected_employee == emp.emp_id|stringformat:"i" %}selected{% endif %}>
                                {{ emp.emp_full_name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-filter"></i> تصفية
                        </button>
                    </div>
                </form>
            </div>
            <div class="col-md-6 text-end d-flex align-items-end justify-content-end">
                <a href="{% url 'employees:payroll_calculate' %}" class="btn btn-success">
                    <i class="fas fa-calculator"></i> حساب الرواتب
                </a>
            </div>
        </div>

        {% if payroll_entries %}
        <div class="table-responsive">
            <table class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>الموظف</th>
                        <th>فترة الراتب</th>
                        <th>الراتب الأساسي</th>
                        <th>البدلات</th>
                        <th>الاستقطاعات</th>
                        <th>الإجمالي</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for entry in payroll_entries %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>
                            <a href="{% url 'employees:detail' entry.employee.emp_id %}">
                                {{ entry.employee.emp_full_name }}
                            </a>
                        </td>
                        <td>{{ entry.payroll_period.name }}</td>
                        <td>{{ entry.basic_salary|floatformat:2 }}</td>
                        <td>{{ entry.allowances|floatformat:2 }}</td>
                        <td>{{ entry.deductions|floatformat:2 }}</td>
                        <td class="fw-bold">{{ entry.total_salary|floatformat:2 }}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'employees:payroll_entry_detail' entry.id %}" class="btn btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <button type="button" class="btn btn-primary" onclick="printPayslip({{ entry.id }})">
                                    <i class="fas fa-print"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot>
                    <tr class="table-dark">
                        <td colspan="3" class="text-end fw-bold">الإجمالي:</td>
                        <td class="fw-bold">
                            {% with basic_sum=0 %}
                                {% for entry in payroll_entries %}
                                    {% with basic_sum=basic_sum|add:entry.basic_salary %}{% endwith %}
                                {% endfor %}
                                {{ basic_sum|floatformat:2 }}
                            {% endwith %}
                        </td>
                        <td class="fw-bold">
                            {% with allowances_sum=0 %}
                                {% for entry in payroll_entries %}
                                    {% with allowances_sum=allowances_sum|add:entry.allowances %}{% endwith %}
                                {% endfor %}
                                {{ allowances_sum|floatformat:2 }}
                            {% endwith %}
                        </td>
                        <td class="fw-bold">
                            {% with deductions_sum=0 %}
                                {% for entry in payroll_entries %}
                                    {% with deductions_sum=deductions_sum|add:entry.deductions %}{% endwith %}
                                {% endfor %}
                                {{ deductions_sum|floatformat:2 }}
                            {% endwith %}
                        </td>
                        <td class="fw-bold">
                            {% with total_sum=0 %}
                                {% for entry in payroll_entries %}
                                    {% with total_sum=total_sum|add:entry.total_salary %}{% endwith %}
                                {% endfor %}
                                {{ total_sum|floatformat:2 }}
                            {% endwith %}
                        </td>
                        <td></td>
                    </tr>
                </tfoot>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            لا توجد سجلات رواتب مطابقة للمعايير المحددة.
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function printPayslip(entryId) {
        // Simulate printing a payslip
        alert('جاري طباعة قسيمة الراتب للموظف رقم ' + entryId);
    }
</script>
{% endblock %}
