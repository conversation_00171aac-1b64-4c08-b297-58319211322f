{% extends 'administrator/base_admin.html' %}
{% load static %}

{% block title %}تعديل المستخدم - لوحة تحكم مدير النظام{% endblock %}

{% block page_icon %}user-edit{% endblock %}
{% block page_header %}تعديل المستخدم {{ user_obj.username }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-user-edit me-2"></i>
                    تعديل بيانات المستخدم
                </h5>
                <div>
                    <a href="{% url 'administrator:user_list' %}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-arrow-right me-1"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.username.id_for_label }}" class="form-label">اسم المستخدم</label>
                            {{ form.username }}
                            {% if form.username.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.username.errors }}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.email.id_for_label }}" class="form-label">البريد الإلكتروني</label>
                            {{ form.email }}
                            {% if form.email.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.email.errors }}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.first_name.id_for_label }}" class="form-label">الاسم الأول</label>
                            {{ form.first_name }}
                            {% if form.first_name.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.first_name.errors }}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.last_name.id_for_label }}" class="form-label">الاسم الأخير</label>
                            {{ form.last_name }}
                            {% if form.last_name.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.last_name.errors }}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-check">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    حساب نشط
                                </label>
                                {% if form.is_active.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.is_active.errors }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a href="{% url 'administrator:user_permissions' user_obj.id %}" class="btn btn-outline-primary">
                                    <i class="fas fa-key me-1"></i>
                                    إدارة الصلاحيات
                                </a>
                                
                                <div>
                                    <a href="{% url 'administrator:user_list' %}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-1"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary ms-2">
                                        <i class="fas fa-save me-1"></i>
                                        حفظ التغييرات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header bg-light">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    إدارة مجموعات المستخدم
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    يمكنك إدارة مجموعات المستخدم من خلال الضغط على الزر أدناه.
                </div>
                
                <div class="text-center">
                    <a href="{% url 'administrator:user_groups' user_obj.id %}" class="btn btn-primary">
                        <i class="fas fa-users me-1"></i>
                        إدارة مجموعات المستخدم
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Additional JavaScript if needed
    });
</script>
{% endblock %}
