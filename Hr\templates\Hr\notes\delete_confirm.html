{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load image_utils %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .delete-warning {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        border-radius: 15px;
        color: white;
    }
    
    .note-preview-card {
        border: 2px solid #dc3545;
        border-radius: 15px;
        background: #fff5f5;
    }
    
    .employee-info-card {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: none;
        border-radius: 15px;
    }
    
    .employee-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid rgba(255, 255, 255, 0.3);
    }
    
    .danger-icon {
        font-size: 4rem;
        color: #dc3545;
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }
    
    .btn-delete-confirm {
        background: linear-gradient(45deg, #dc3545, #c82333);
        border: none;
        border-radius: 25px;
        padding: 12px 30px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        transition: all 0.3s ease;
    }
    
    .btn-delete-confirm:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(220, 53, 69, 0.4);
    }
    
    .warning-list {
        background: rgba(220, 53, 69, 0.1);
        border-left: 4px solid #dc3545;
        border-radius: 5px;
        padding: 1rem;
    }
    
    .note-meta {
        background: rgba(0, 123, 255, 0.1);
        border-radius: 10px;
        padding: 1rem;
    }
</style>
{% endblock %}

{% block page_title %}
<div class="d-flex align-items-center justify-content-between">
    <div>
        <h1 class="h3 mb-1 text-gray-800">
            <i class="fas fa-trash text-danger me-2"></i>
            {{ title }}
        </h1>
        <p class="text-muted mb-0">تأكيد حذف الملاحظة</p>
    </div>
    <div>
        <a href="{% url 'Hr:notes:detail' note.id %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للملاحظة
        </a>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <!-- Warning Header -->
        <div class="delete-warning p-4 mb-4 text-center">
            <div class="danger-icon mb-3">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h2 class="mb-2 text-white">تحذير: حذف الملاحظة</h2>
            <p class="mb-0 text-white opacity-75">
                أنت على وشك حذف ملاحظة مهمة. هذا الإجراء لا يمكن التراجع عنه.
            </p>
        </div>
        
        <!-- Note Preview -->
        <div class="card note-preview-card mb-4">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="fas fa-sticky-note me-2"></i>
                    الملاحظة المراد حذفها
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <div class="flex-grow-1">
                        <h4 class="mb-2">{{ note.title }}</h4>
                        <div class="d-flex gap-2 flex-wrap mb-3">
                            <span class="badge bg-{{ note.get_note_type_display_color }} fs-6">
                                {{ note.get_note_type_display }}
                            </span>
                            <span class="badge bg-{{ note.get_priority_display_color }} fs-6">
                                {{ note.get_priority_display }}
                            </span>
                            {% if note.is_important %}
                            <span class="badge bg-warning text-dark fs-6">مهم</span>
                            {% endif %}
                            {% if note.is_confidential %}
                            <span class="badge bg-dark fs-6">سري</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="note-content mb-3" style="white-space: pre-wrap; line-height: 1.6;">
                    {{ note.content }}
                </div>
                
                {% if note.evaluation_score or note.tags %}
                <hr>
                <div class="row g-3">
                    {% if note.evaluation_score %}
                    <div class="col-md-6">
                        <strong>درجة التقييم:</strong>
                        <span class="badge bg-primary ms-2">{{ note.evaluation_score }}/100</span>
                    </div>
                    {% endif %}
                    {% if note.tags %}
                    <div class="col-md-6">
                        <strong>العلامات:</strong>
                        {% for tag in note.tags|split:"," %}
                        <span class="badge bg-light text-dark border ms-1">#{{ tag|trim }}</span>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
                {% endif %}
                
                <!-- Note Meta Information -->
                <div class="note-meta mt-3">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <small class="text-muted d-block">تم الإنشاء بواسطة</small>
                            <span>
                                <i class="fas fa-user me-1"></i>
                                {{ note.created_by.get_full_name|default:note.created_by.username }}
                            </span>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted d-block">تاريخ الإنشاء</small>
                            <span>
                                <i class="fas fa-clock me-1"></i>
                                {{ note.created_at|date:"Y/m/d H:i" }}
                            </span>
                        </div>
                        {% if note.last_modified_by and note.updated_at != note.created_at %}
                        <div class="col-md-6">
                            <small class="text-muted d-block">آخر تعديل بواسطة</small>
                            <span>
                                <i class="fas fa-edit me-1"></i>
                                {{ note.last_modified_by.get_full_name|default:note.last_modified_by.username }}
                            </span>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted d-block">تاريخ آخر تعديل</small>
                            <span>
                                <i class="fas fa-clock me-1"></i>
                                {{ note.updated_at|date:"Y/m/d H:i" }}
                            </span>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Employee Information -->
        <div class="card employee-info-card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    معلومات الموظف المرتبط
                </h6>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        {% if note.employee.emp_image %}
                        <img src="{{ note.employee.emp_image|binary_to_img }}" 
                             alt="{{ note.employee.emp_full_name }}"
                             class="employee-avatar">
                        {% else %}
                        <div class="employee-avatar bg-primary text-white d-flex align-items-center justify-content-center"
                             style="font-size: 1.5rem; font-weight: bold;">
                            {{ note.employee.emp_first_name|slice:":1"|upper }}
                        </div>
                        {% endif %}
                    </div>
                    <div>
                        <h5 class="mb-1">{{ note.employee.emp_full_name|default:note.employee.emp_first_name }}</h5>
                        <p class="text-muted mb-1">{{ note.employee.jop_name|default:"غير محدد" }}</p>
                        <p class="text-muted mb-0">
                            <i class="fas fa-building me-1"></i>
                            {{ note.employee.department.dept_name|default:"غير محدد" }}
                            <span class="ms-3">
                                <i class="fas fa-id-badge me-1"></i>
                                {{ note.employee.emp_id }}
                            </span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Warning Information -->
        <div class="warning-list mb-4">
            <h6 class="text-danger mb-3">
                <i class="fas fa-exclamation-circle me-2"></i>
                تحذيرات مهمة قبل الحذف:
            </h6>
            <ul class="mb-0">
                <li class="mb-2">
                    <strong>الحذف نهائي:</strong> لن تتمكن من استرداد هذه الملاحظة بعد الحذف
                </li>
                <li class="mb-2">
                    <strong>فقدان البيانات:</strong> ستفقد جميع المعلومات والتفاصيل المرتبطة بهذه الملاحظة
                </li>
                <li class="mb-2">
                    <strong>تأثير على التقارير:</strong> قد تتأثر التقارير والإحصائيات المستقبلية
                </li>
                {% if note.is_important %}
                <li class="mb-2">
                    <strong class="text-warning">ملاحظة مهمة:</strong> هذه الملاحظة مصنفة كمهمة
                </li>
                {% endif %}
                {% if note.follow_up_required %}
                <li class="mb-2">
                    <strong class="text-info">متابعة مطلوبة:</strong> هذه الملاحظة تتطلب متابعة
                </li>
                {% endif %}
            </ul>
        </div>
        
        <!-- Confirmation Form -->
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h6 class="mb-0">
                    <i class="fas fa-check-circle me-2"></i>
                    تأكيد الحذف
                </h6>
            </div>
            <div class="card-body">
                <p class="mb-4">
                    للمتابعة مع حذف هذه الملاحظة، يرجى تأكيد العملية بالنقر على الزر أدناه.
                    تذكر أن هذا الإجراء لا يمكن التراجع عنه.
                </p>
                
                <form method="post" id="deleteForm">
                    {% csrf_token %}
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <a href="{% url 'Hr:notes:detail' note.id %}" class="btn btn-outline-secondary btn-lg me-3">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                            <a href="{% url 'Hr:notes:edit' note.id %}" class="btn btn-outline-primary btn-lg">
                                <i class="fas fa-edit me-2"></i>
                                تعديل بدلاً من الحذف
                            </a>
                        </div>
                        <div>
                            <button type="submit" class="btn btn-delete-confirm text-white btn-lg" id="confirmDeleteBtn">
                                <i class="fas fa-trash me-2"></i>
                                تأكيد الحذف
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const deleteForm = document.getElementById('deleteForm');
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    
    // Add double confirmation
    deleteForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // First confirmation
        if (confirm('هل أنت متأكد من حذف هذه الملاحظة؟\n\nهذا الإجراء لا يمكن التراجع عنه.')) {
            // Second confirmation for important notes
            {% if note.is_important %}
            if (confirm('تحذير: هذه ملاحظة مهمة!\n\nهل تريد المتابعة مع الحذف؟')) {
                this.submit();
            }
            {% else %}
            this.submit();
            {% endif %}
        }
    });
    
    // Add loading state to button
    confirmDeleteBtn.addEventListener('click', function() {
        this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحذف...';
        this.disabled = true;
    });
    
    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Escape key to cancel
        if (e.key === 'Escape') {
            window.location.href = '{% url "Hr:notes:detail" note.id %}';
        }
        
        // Ctrl+Enter to confirm delete (with confirmation)
        if (e.ctrlKey && e.key === 'Enter') {
            confirmDeleteBtn.click();
        }
    });
    
    // Auto-focus on cancel button for safety
    document.querySelector('a[href*="detail"]').focus();
});
</script>
{% endblock %}
