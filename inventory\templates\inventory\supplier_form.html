<!-- templates/inventory/supplier_form.html -->
{% extends 'inventory/base_inventory.html' %}
{% load static %}

{% block title %}{% if form.instance.pk %}تعديل المورد{% else %}إضافة مورد جديد{% endif %} - نظام إدارة المخزن{% endblock %}

{% block content %}
<div class="row ps-4">
    <div class="col-12 mb-4">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-truck{% if form.instance.pk %}-loading{% else %}-plus{% endif %} me-2"></i>
                    {% if form.instance.pk %}تعديل المورد{% else %}إضافة مورد جديد{% endif %}
                </h4>
            </div>
            <div class="card-body p-4">
                <form method="post" class="needs-validation" novalidate>
                    {% csrf_token %}
                    <div class="row g-4">
                        <!-- اسم المورد -->
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="id_name" name="name" value="{{ form.name.value|default:'' }}" required>
                                <label for="id_name">اسم المورد</label>
                            </div>
                        </div>

                        <!-- الشخص المسؤول -->
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="id_contact_person" name="contact_person" value="{{ form.contact_person.value|default:'' }}">
                                <label for="id_contact_person">الشخص المسؤول</label>
                            </div>
                        </div>

                        <!-- رقم الهاتف -->
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="id_phone" name="phone" value="{{ form.phone.value|default:'' }}">
                                <label for="id_phone">رقم الهاتف</label>
                            </div>
                        </div>

                        <!-- البريد الإلكتروني -->
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="email" class="form-control" id="id_email" name="email" value="{{ form.email.value|default:'' }}">
                                <label for="id_email">البريد الإلكتروني</label>
                            </div>
                        </div>

                        <!-- العنوان -->
                        <div class="col-12">
                            <div class="form-floating">
                                <textarea class="form-control" id="id_address" name="address" style="height: 100px">{{ form.address.value|default:'' }}</textarea>
                                <label for="id_address">العنوان</label>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار التحكم -->
                    <div class="mt-4 d-flex justify-content-between">
                        <a href="{% url 'inventory:supplier_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right me-1"></i>
                            العودة للقائمة
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.form-floating {
    margin-bottom: 0.5rem;
}

.form-floating > .form-control,
.form-floating > .form-select {
    height: calc(3.5rem + 2px);
    line-height: 1.25;
}

.form-floating > label {
    padding: 1rem 0.75rem;
}

.card {
    border-radius: 10px;
    border: none;
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
}

.btn-primary {
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}
</style>
{% endblock %}