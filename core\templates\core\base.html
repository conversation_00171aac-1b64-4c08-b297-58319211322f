{% load static %}
{% load i18n %}
<!DOCTYPE html>
<html lang="{{ LANGUAGE_CODE }}" dir="{{ TEXT_DIRECTION }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}ElDawliya System{% endblock %}</title>
    <!-- Bootstrap CSS -->
    {% if TEXT_DIRECTION == 'rtl' %}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    {% else %}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    {% endif %}
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    <!-- Direction-specific CSS -->
    {% if TEXT_DIRECTION == 'rtl' %}
    <link rel="stylesheet" href="{% static 'css/rtl.css' %}">
    {% endif %}
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family={{ CURRENT_FONT|default:'Cairo' }}:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            --font-family: '{{ CURRENT_FONT|default:"Cairo" }}', sans-serif;
        }

        body {
            font-family: var(--font-family), system-ui, -apple-system, sans-serif;
            {% if TEXT_DIRECTION == 'rtl' %}
            text-align: right;
            {% endif %}
        }

        /* Direction-specific styles */
        {% if TEXT_DIRECTION == 'rtl' %}
        .dropdown-menu {
            text-align: right;
        }

        .form-check {
            padding-right: 1.5em;
            padding-left: 0;
        }

        .form-check .form-check-input {
            float: right;
            margin-right: -1.5em;
            margin-left: 0;
        }

        .input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
            margin-right: -1px;
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
            border-top-left-radius: 0.375rem;
            border-bottom-left-radius: 0.375rem;
        }

        .input-group:not(.has-validation) > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating) {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            border-top-right-radius: 0.375rem;
            border-bottom-right-radius: 0.375rem;
        }
        {% endif %}
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="{% url 'accounts:home' %}">{% trans "ElDawliya System" %}</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    {% if user.is_authenticated %}
                    <li class="nav-item"><a class="nav-link" href="{% url 'meetings:list' %}">{% trans "Meetings" %}</a></li>
                    <li class="nav-item"><a class="nav-link" href="{% url 'tasks:list' %}">{% trans "Tasks" %}</a></li>
                    <li class="nav-item"><a class="nav-link" href="{% url 'inventory:dashboard' %}">{% trans "Inventory" %}</a></li>
                    <li class="nav-item"><a class="nav-link" href="{% url 'attendance:dashboard' %}">{% trans "Attendance" %}</a></li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle"></i> {{ user.get_full_name|default:user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="{% url 'accounts:dashboard' %}">{% trans "Profile" %}</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'accounts:logout' %}">{% trans "Logout" %}</a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item"><a class="nav-link" href="{% url 'accounts:login' %}">{% trans "Login" %}</a></li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Messages -->
    <div class="container mt-3">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}
    </div>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                {% block sidebar %}
                <!-- Default sidebar content -->
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'accounts:home' %}">
                                <i class="fas fa-home"></i> {% trans "Dashboard" %}
                            </a>
                        </li>
                    </ul>
                </div>
                {% endblock %}
            </div>

            <!-- Main Content Area -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                {% block content %}
                {% endblock %}
            </main>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer mt-5 py-3 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; {% trans "ElDawliya International Printing System. All rights reserved." %}</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p>{% trans "Powered by" %} <a href="https://www.eldawliya.com">ElDawliya</a></p>
                </div>
            </div>
        </div>
    </footer>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JavaScript -->
    <script>
        // Apply direction-specific JavaScript adjustments
        document.addEventListener('DOMContentLoaded', function() {
            {% if TEXT_DIRECTION == 'rtl' %}
            // Set RTL for all elements that need it
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                menu.style.textAlign = 'right';
            });

            // Adjust data tables for RTL if you're using them
            if (typeof $.fn.DataTable !== 'undefined') {
                $.extend(true, $.fn.DataTable.defaults, {
                    language: {
                        url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json'
                    }
                });
            }

            // Fix any RTL issues with third-party plugins
            document.querySelectorAll('.fc-header-toolbar').forEach(toolbar => {
                toolbar.style.direction = 'rtl';
            });
            {% endif %}

            // Initialize tooltips
            const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
            if (tooltipTriggerList.length > 0) {
                tooltipTriggerList.forEach(tooltipTriggerEl => {
                    new bootstrap.Tooltip(tooltipTriggerEl, {
                        placement: 'auto',
                        container: 'body'
                    });
                });
            }
        });
    </script>
    {% block extra_js %}{% endblock %}
</body>
</html>