{% extends 'cars/base.html' %}

{% block title %}{{ title }} - نظام إدارة نشاط النقل{% endblock %}

{% block header %}{{ title }}{% endblock %}

{% block content %}
    <div class="row">
        <div class="col-md-8 mx-auto">
            <form method="post" novalidate>
                {% csrf_token %}
                
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">بيانات المورد</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label for="{{ form.name.id_for_label }}" class="form-label">اسم المورد <span class="text-danger">*</span></label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="text-danger">
                                        {% for error in form.name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="{{ form.contact_person.id_for_label }}" class="form-label">الشخص المسؤول</label>
                                {{ form.contact_person }}
                                {% if form.contact_person.errors %}
                                    <div class="text-danger">
                                        {% for error in form.contact_person.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <small class="text-muted">اسم الشخص المسؤول في شركة المورد</small>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="{{ form.phone.id_for_label }}" class="form-label">رقم الهاتف</label>
                                {{ form.phone }}
                                {% if form.phone.errors %}
                                    <div class="text-danger">
                                        {% for error in form.phone.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="{{ form.email.id_for_label }}" class="form-label">البريد الإلكتروني</label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="text-danger">
                                        {% for error in form.email.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <label for="{{ form.address.id_for_label }}" class="form-label">العنوان</label>
                                {{ form.address }}
                                {% if form.address.errors %}
                                    <div class="text-danger">
                                        {% for error in form.address.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="{% url 'cars:supplier_list' %}" class="btn btn-secondary me-md-2">
                        <i class="fas fa-times-circle"></i> إلغاء
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ المورد
                    </button>
                </div>
            </form>
        </div>
    </div>
{% endblock %}
