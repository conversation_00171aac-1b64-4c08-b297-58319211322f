{% extends 'Hr/base_hr.html' %}
{% load static %}

{% block title %}الهيكل التنظيمي للشركة - الموارد البشرية{% endblock %}

{% block page_title %}الهيكل التنظيمي للشركة{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموارد البشرية</a></li>
<li class="breadcrumb-item active">الهيكل التنظيمي</li>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light py-3 d-flex justify-content-between align-items-center">
                <h5 class="mb-0 fw-semibold">
                    <i class="fas fa-sitemap text-primary me-2"></i>الهيكل التنظيمي للشركة
                </h5>
                <div class="chart-actions">
                    <button class="btn btn-sm btn-outline-primary zoom-in-btn">
                        <i class="fas fa-search-plus"></i> تكبير
                    </button>
                    <button class="btn btn-sm btn-outline-primary zoom-out-btn">
                        <i class="fas fa-search-minus"></i> تصغير
                    </button>
                    <button class="btn btn-sm btn-outline-primary reset-btn">
                        <i class="fas fa-redo"></i> إعادة ضبط
                    </button>
                    <button class="btn btn-sm btn-outline-primary print-btn">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                    <button class="btn btn-sm btn-outline-primary export-btn">
                        <i class="fas fa-file-export"></i> تصدير
                    </button>
                    <div class="dropdown d-inline-block">
                        <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" id="departmentFilterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-filter"></i> تصفية
                        </button>
                        <ul class="dropdown-menu department-filter-menu" aria-labelledby="departmentFilterDropdown">
                            <li>
                                <a class="dropdown-item" href="#" data-dept-filter="all">
                                    <i class="fas fa-building me-2"></i>جميع الأقسام
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            {% for dept in departments %}
                            <li>
                                <a class="dropdown-item" href="#" data-dept-filter="{{ dept.dept_code }}">
                                    {{ dept.dept_name }}
                                </a>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="alert alert-info m-3 d-flex align-items-center">
                    <i class="fas fa-info-circle me-2 fa-lg"></i>
                    <div>
                        <p class="mb-0">يعرض الهيكل التنظيمي للشركة بناءً على التسلسل الهرمي للإدارة. استخدم أدوات التكبير والتصغير للتنقل في الهيكل.</p>
                    </div>
                    <button type="button" class="btn-close ms-auto" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <div class="chart-container overflow-auto p-4" style="height: 800px;">
                    <div id="org-chart-container" class="d-flex justify-content-center">
                        <div id="org-chart" class="orgchart">
                            {% if nodes %}
                            <!-- سيتم إنشاء الهيكل التنظيمي ديناميكيًا عن طريق JavaScript -->
                            {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-sitemap fa-3x mb-3 text-muted"></i>
                                <p class="mb-0">لم يتم العثور على بيانات للهيكل التنظيمي.</p>
                                <p class="small text-muted">تأكد من إعداد العلاقات التسلسلية بين الموظفين.</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Employee Details Modal -->
<div class="modal fade" id="employeeDetailsModal" tabindex="-1" aria-labelledby="employeeDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-light">
                <h5 class="modal-title" id="employeeDetailsModalLabel">
                    <i class="fas fa-user-circle me-2"></i>
                    <span class="employee-name">اسم الموظف</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-4 text-center mb-3 mb-md-0">
                        <div class="employee-image-container mb-3">
                            <img id="employeeImage" class="img-thumbnail rounded-circle" style="width: 150px; height: 150px; object-fit: cover;" src="" alt="صورة الموظف">
                        </div>
                        <div class="badge bg-primary mb-2 d-inline-block" id="employeeStatus">نشط</div>
                        <a href="#" id="employeeProfileLink" class="btn btn-sm btn-primary d-block mx-auto" style="width: fit-content;">
                            <i class="fas fa-id-card me-1"></i> عرض الملف الكامل
                        </a>
                    </div>
                    <div class="col-md-8">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label class="text-muted small d-block">الرقم الوظيفي</label>
                                    <div id="employeeId" class="fw-bold">-</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label class="text-muted small d-block">القسم</label>
                                    <div id="employeeDepartment" class="fw-bold">-</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label class="text-muted small d-block">المسمى الوظيفي</label>
                                    <div id="employeeTitle" class="fw-bold">-</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label class="text-muted small d-block">تاريخ التعيين</label>
                                    <div id="employeeHireDate" class="fw-bold">-</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label class="text-muted small d-block">البريد الإلكتروني</label>
                                    <div id="employeeEmail" class="fw-bold">-</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-group">
                                    <label class="text-muted small d-block">رقم الهاتف</label>
                                    <div id="employeePhone" class="fw-bold">-</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- المعلومات الإضافية -->
                <hr>
                <div class="row g-3">
                    <div class="col-md-4">
                        <div class="info-group">
                            <label class="text-muted small d-block">المرؤوسين المباشرين</label>
                            <div id="directReports" class="fw-bold">-</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="info-group">
                            <label class="text-muted small d-block">إجمالي المرؤوسين</label>
                            <div id="totalReports" class="fw-bold">-</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="info-group">
                            <label class="text-muted small d-block">المستوى الإداري</label>
                            <div id="managementLevel" class="fw-bold">-</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer bg-light">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <a id="editEmployeeLink" href="#" class="btn btn-primary">
                    <i class="fas fa-edit me-1"></i> تعديل
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/orgchart@3.1.1/dist/css/jquery.orgchart.min.css" rel="stylesheet">
<style>
    /* Card & Container Styles */
    .card {
        border: none;
        border-radius: 8px;
        overflow: hidden;
        transition: all 0.3s ease;
    }
    .card:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1) !important;
    }
    .chart-container {
        position: relative;
    }

    /* OrgChart Customization */
    .orgchart {
        background-image: linear-gradient(90deg, rgba(200, 200, 200, 0.1) 10%, rgba(0, 0, 0, 0) 10%),
                          linear-gradient(rgba(200, 200, 200, 0.1) 10%, rgba(0, 0, 0, 0) 10%);
        background-size: 10px 10px;
        background-color: #f8f9fa;
        padding: 20px;
    }

    .orgchart .node {
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .orgchart .node:hover {
        background-color: #e6f6ff;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        transform: translateY(-3px);
    }

    .orgchart .node .title {
        background-color: #0d6efd;
        padding: 8px;
        border-radius: 5px 5px 0 0;
    }

    .orgchart .node .content {
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 0 0 5px 5px;
        border-top: none;
    }

    /* OrgChart Node Status Colors */
    .orgchart .node.active .title {
        background-color: #198754;
    }

    .orgchart .node.on-leave .title {
        background-color: #0dcaf0;
    }

    .orgchart .node.resigned .title {
        background-color: #dc3545;
    }

    .orgchart .node.terminated .title {
        background-color: #6c757d;
    }

    /* Toolbar Buttons */
    .chart-actions .btn {
        margin-left: 5px;
        font-size: 0.85rem;
    }

    /* Department Filter Menu */
    .department-filter-menu {
        max-height: 300px;
        overflow-y: auto;
    }

    /* Employee Details Modal */
    .info-group {
        margin-bottom: 12px;
    }

    .employee-image-container {
        position: relative;
        display: inline-block;
    }
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/orgchart@3.1.1/dist/js/jquery.orgchart.min.js"></script>
<script>
$(function() {
    // Org Chart Data - This would normally be fetched from the backend
    const orgData = {{ org_chart_data|safe }};

    if (orgData) {
        // Initialize the organization chart
        $('#org-chart').orgchart({
            'data': orgData,
            'nodeContent': 'title',
            'direction': 'b2t', // bottom to top
            'pan': true,
            'zoom': true,
            'nodeTemplate': function(data) {
                // Custom node template - creates each box in the org chart
                let statusClass = 'active';
                if (data.status === 'إجازة') statusClass = 'on-leave';
                else if (data.status === 'استقالة') statusClass = 'resigned';
                else if (data.status === 'انقطاع عن العمل') statusClass = 'terminated';

                return `
                <div class="node ${statusClass}" data-emp-id="${data.id}">
                    <div class="title">${data.name}</div>
                    <div class="content">
                        <div class="position">${data.title || 'بدون مسمى وظيفي'}</div>
                        <div class="department small">${data.department || ''}</div>
                    </div>
                </div>`;
            },
            'createNode': function($node, data) {
                $node.on('click', function() {
                    showEmployeeDetails(data);
                });
            }
        });

        // Initial chart settings
        const $chart = $('#org-chart');
        const $container = $('.chart-container');

        // Zoom controls
        $('.zoom-in-btn').on('click', function() {
            $chart.orgchart('zoomIn');
        });

        $('.zoom-out-btn').on('click', function() {
            $chart.orgchart('zoomOut');
        });

        $('.reset-btn').on('click', function() {
            $chart.orgchart('resetView');
        });

        // Department filter
        $('.department-filter-menu a').on('click', function(e) {
            e.preventDefault();
            const deptId = $(this).data('dept-filter');

            if (deptId === 'all') {
                // Show all departments
                $chart.find('.node').removeClass('filtered-out');
            } else {
                // Filter by department
                $chart.find('.node').each(function() {
                    const nodeData = $(this).data('nodeData');
                    if (nodeData && nodeData.department_id === deptId) {
                        $(this).removeClass('filtered-out');
                    } else {
                        $(this).addClass('filtered-out');
                    }
                });
            }
        });

        // Print functionality
        $('.print-btn').on('click', function() {
            // Prepare chart for printing
            $('body').addClass('print-orgchart');
            window.print();
            $('body').removeClass('print-orgchart');
        });

        // Export functionality
        $('.export-btn').on('click', function() {
            // Convert chart to image and download
            html2canvas($chart[0]).then(canvas => {
                const imgData = canvas.toDataURL('image/png');
                const a = document.createElement('a');
                a.href = imgData;
                a.download = 'الهيكل_التنظيمي_' + new Date().toISOString().slice(0, 10) + '.png';
                a.click();
            });
        });
    }

    // Function to display employee details modal
    function showEmployeeDetails(employee) {
        // Set employee details in modal
        $('.employee-name').text(employee.name);
        $('#employeeId').text(employee.id);
        $('#employeeDepartment').text(employee.department || '-');
        $('#employeeTitle').text(employee.title || '-');
        $('#employeeHireDate').text(employee.hire_date || '-');
        $('#employeeEmail').text(employee.email || '-');
        $('#employeePhone').text(employee.phone || '-');
        $('#directReports').text(employee.direct_reports || '0');
        $('#totalReports').text(employee.total_reports || '0');
        $('#managementLevel').text(employee.level || '-');

        // Set employee status
        if (employee.status === 'سارى') {
            $('#employeeStatus').removeClass().addClass('badge bg-success').text('سارى');
        } else if (employee.status === 'إجازة') {
            $('#employeeStatus').removeClass().addClass('badge bg-info').text('إجازة');
        } else if (employee.status === 'استقالة') {
            $('#employeeStatus').removeClass().addClass('badge bg-danger').text('استقالة');
        } else if (employee.status === 'انقطاع عن العمل') {
            $('#employeeStatus').removeClass().addClass('badge bg-secondary').text('انقطاع عن العمل');
        }

        // Set employee image
        if (employee.image) {
            $('#employeeImage').attr('src', employee.image);
        } else {
            // Default image if not available
            $('#employeeImage').attr('src', '/static/img/default-avatar.png');
        }

        // Set links
        const profileBaseUrl = '{{ employee_profile_url|default:"/hr/employees/" }}';
        const editBaseUrl = '{{ employee_edit_url|default:"/hr/employees/edit/" }}';
        $('#employeeProfileLink').attr('href', profileBaseUrl + employee.id + '/');
        $('#editEmployeeLink').attr('href', editBaseUrl + employee.id + '/');

        // Show modal
        const employeeModal = new bootstrap.Modal(document.getElementById('employeeDetailsModal'));
        employeeModal.show();
    }
});
</script>
<style>
/* Print styles */
@media print {
    body * {
        visibility: hidden;
    }
    .orgchart * {
        visibility: visible;
    }
    .orgchart {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
    }
    .node.filtered-out {
        display: none !important;
    }
}

/* Node filtering */
.node.filtered-out {
    opacity: 0.3;
    pointer-events: none;
}
</style>
{% endblock %}
