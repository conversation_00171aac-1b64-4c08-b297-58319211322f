{% extends 'administrator/base_admin.html' %}
{% load static %}

{% block title %}{{ page_title }} | {{ system_settings.system_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="page-title">{{ page_title }}</h2>
                <div>
                    <a href="{% url 'administrator:permission_dashboard' %}" class="btn btn-info me-2">
                        <i class="fas fa-question-circle me-1"></i>
                        لوحة الصلاحيات
                    </a>
                    <a href="{% url 'administrator:permission_dashboard' %}" class="btn btn-outline-primary me-2">
                        <i class="fas fa-arrow-right ml-1"></i>
                        العودة إلى لوحة الصلاحيات
                    </a>
                    <a href="{% url 'administrator:group_add' %}" class="btn btn-success">
                        <i class="fas fa-plus-circle me-1"></i>
                        إضافة مجموعة جديدة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">قائمة مجموعات المستخدمين</h5>
                </div>
                <div class="card-body">
                    {% if groups %}
                    <div class="table-responsive">
                        <table class="table table-hover table-striped">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>اسم المجموعة</th>
                                    <th>عدد المستخدمين</th>
                                    <th>آخر تعديل</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for group in groups %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>{{ group.name }}</td>
                                    <td>
                                        <span class="badge bg-secondary">{{ group.user_set.count }}</span>
                                    </td>
                                    <td>
                                        {% if group.last_modified %}
                                            {{ group.last_modified|date:"Y-m-d H:i" }}
                                        {% else %}
                                            ---
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'administrator:group_detail' group.id %}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'administrator:group_edit' group.id %}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'administrator:group_permissions' group.id %}" class="btn btn-sm btn-warning">
                                                <i class="fas fa-user-shield"></i>
                                            </a>
                                            <a href="{% url 'administrator:group_delete' group.id %}" class="btn btn-sm btn-danger">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    {% if is_paginated %}
                    <div class="pagination mt-4 d-flex justify-content-center">
                        <ul class="pagination">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1"><i class="fas fa-angle-double-right"></i></a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}"><i class="fas fa-angle-right"></i></a>
                                </li>
                            {% endif %}

                            <li class="page-item active">
                                <span class="page-link">{{ page_obj.number }} من {{ page_obj.paginator.num_pages }}</span>
                            </li>

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}"><i class="fas fa-angle-left"></i></a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}"><i class="fas fa-angle-double-left"></i></a>
                                </li>
                            {% endif %}
                        </ul>
                    </div>
                    {% endif %}
                    
                    {% else %}
                    <div class="alert alert-info text-center">
                        <i class="fas fa-info-circle me-2"></i>
                        لا توجد مجموعات مسجلة حالياً. قم بإضافة مجموعة جديدة.
                    </div>
                    
                    <div class="text-center mt-4">
                        <a href="{% url 'administrator:group_add' %}" class="btn btn-success">
                            <i class="fas fa-plus-circle me-1"></i>
                            إضافة مجموعة جديدة
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
