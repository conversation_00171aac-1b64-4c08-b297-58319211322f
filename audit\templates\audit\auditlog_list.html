{% extends 'administrator/base_admin.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans 'سجلات التدقيق' %}{% endblock %}

{% block extra_css %}
<style>
    .audit-filters {
        background-color: #f8f9fa;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 20px;
    }
    .audit-table th, .audit-table td {
        vertical-align: middle;
    }
    .action-create { color: green; }
    .action-update { color: orange; }
    .action-delete { color: red; }
    .action-view { color: blue; }
    .action-login, .action-logout { color: purple; }
    
    .audit-filters .form-group {
        margin-bottom: 10px;
    }
    
    .pagination {
        justify-content: center;
        margin-top: 20px;
    }
    
    .export-button {
        margin-bottom: 15px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col">
            <h1>{% trans 'سجلات التدقيق' %}</h1>
            <p class="text-muted">{% trans 'عرض تاريخ العمليات والأنشطة في النظام' %}</p>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-12">
            <!-- Filter Form -->
            <div class="audit-filters">
                <h5>{% trans 'تصفية السجلات' %}</h5>
                <form method="get" class="row">
                    <div class="form-group col-md-2">
                        <label for="action">{% trans 'نوع الإجراء' %}</label>
                        <select name="action" id="action" class="form-control">
                            <option value="">{% trans 'الكل' %}</option>
                            {% for action_value, action_name in action_choices %}
                                <option value="{{ action_value }}" {% if current_filters.action == action_value %}selected{% endif %}>
                                    {{ action_name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="form-group col-md-2">
                        <label for="app_name">{% trans 'التطبيق' %}</label>
                        <select name="app_name" id="app_name" class="form-control">
                            <option value="">{% trans 'الكل' %}</option>
                            {% for app in app_names %}
                                {% if app %}
                                    <option value="{{ app }}" {% if current_filters.app_name == app %}selected{% endif %}>
                                        {{ app }}
                                    </option>
                                {% endif %}
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="form-group col-md-2">
                        <label for="date_from">{% trans 'من تاريخ' %}</label>
                        <input type="date" name="date_from" id="date_from" class="form-control"
                               value="{{ current_filters.date_from|default:'' }}">
                    </div>
                    
                    <div class="form-group col-md-2">
                        <label for="date_to">{% trans 'إلى تاريخ' %}</label>
                        <input type="date" name="date_to" id="date_to" class="form-control"
                               value="{{ current_filters.date_to|default:'' }}">
                    </div>
                    
                    <div class="form-group col-md-4">
                        <label for="search">{% trans 'بحث' %}</label>
                        <div class="input-group">
                            <input type="text" name="search" id="search" class="form-control"
                                   placeholder="{% trans 'بحث في العمليات...' %}" 
                                   value="{{ current_filters.search|default:'' }}">
                            <div class="input-group-append">
                                <button class="btn btn-primary" type="submit">
                                    <i class="fas fa-search"></i> {% trans 'بحث' %}
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
                
                <!-- Export Button -->
                <div class="row mt-3">
                    <div class="col">
                        <a href="{% url 'audit:audit_export' %}?{{ request.GET.urlencode }}" 
                           class="btn btn-outline-secondary export-button">
                            <i class="fas fa-download"></i> {% trans 'تصدير النتائج' %}
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Audit Logs Table -->
            <div class="table-responsive">
                <table class="table table-striped table-hover audit-table">
                    <thead>
                        <tr>
                            <th>{% trans 'الوقت' %}</th>
                            <th>{% trans 'المستخدم' %}</th>
                            <th>{% trans 'الإجراء' %}</th>
                            <th>{% trans 'التطبيق' %}</th>
                            <th>{% trans 'التفاصيل' %}</th>
                            <th>{% trans 'عنوان IP' %}</th>
                            <th>{% trans 'عرض' %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for log in audit_logs %}
                            <tr>
                                <td>{{ log.timestamp|date:"Y-m-d H:i:s" }}</td>
                                <td>{{ log.user.username|default:_('مستخدم غير معروف') }}</td>
                                <td class="action-{{ log.action|lower }}">{{ log.get_action_display }}</td>
                                <td>{{ log.app_name|default:'-' }}</td>
                                <td>
                                    {% if log.object_repr %}
                                        {{ log.object_repr }} ({{ log.action_details|default:'-' }})
                                    {% else %}
                                        {{ log.action_details|default:'-' }}
                                    {% endif %}
                                </td>
                                <td>{{ log.ip_address|default:'-' }}</td>
                                <td>
                                    <a href="{% url 'audit:audit_detail' log.id %}" class="btn btn-sm btn-outline-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                        {% empty %}
                            <tr>
                                <td colspan="7" class="text-center">{% trans 'لا توجد سجلات تدقيق لعرضها.' %}</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if is_paginated %}
                <div class="pagination">
                    <ul class="pagination">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% for key, value in current_filters.items %}{% if value %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                                    <i class="fas fa-angle-double-left"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in current_filters.items %}{% if value %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                                    <i class="fas fa-angle-left"></i>
                                </a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">
                                {{ page_obj.number }} {% trans 'من' %} {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in current_filters.items %}{% if value %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                                    <i class="fas fa-angle-right"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% for key, value in current_filters.items %}{% if value %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                                    <i class="fas fa-angle-double-right"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    $(document).ready(function() {
        // Auto-submit form when select fields change
        $('select[name="action"], select[name="app_name"]').change(function() {
            $(this).closest('form').submit();
        });
        
        // Date picker configuration
        $('#date_from, #date_to').change(function() {
            if ($('#date_from').val() || $('#date_to').val()) {
                $(this).closest('form').submit();
            }
        });
    });
</script>
{% endblock %}
