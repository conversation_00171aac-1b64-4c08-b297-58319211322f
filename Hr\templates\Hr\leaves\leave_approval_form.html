{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}{{ title }} - نظام الدولية{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'Hr/css/leaves.css' %}">
{% endblock %}

{% block page_title %}{{ title }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:employees:list' %}">الموظفين</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:leaves:list' %}">إجازات الموظفين</a></li>
<li class="breadcrumb-item active">{{ title }}</li>
{% endblock %}

{% block content %}
<div class="card shadow-sm">
    <div class="card-header bg-white d-flex justify-content-between align-items-center py-3">
        <h5 class="mb-0">
            <i class="fas fa-check-circle me-2"></i>
            {{ title }}
        </h5>
        <a href="{% url 'Hr:leaves:list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة للقائمة
        </a>
    </div>
    <div class="card-body">
        <div class="row mb-4">
            <div class="col-md-6">
                <h6 class="mb-3">معلومات الموظف</h6>
                <table class="table table-bordered">
                    <tr>
                        <th style="width: 40%">اسم الموظف</th>
                        <td>{{ leave.employee.emp_full_name }}</td>
                    </tr>
                    <tr>
                        <th>نوع الإجازة</th>
                        <td>{{ leave.leave_type.name }}</td>
                    </tr>
                    <tr>
                        <th>الفترة</th>
                        <td>{{ leave.start_date }} إلى {{ leave.end_date }}</td>
                    </tr>
                    <tr>
                        <th>عدد الأيام</th>
                        <td>{{ leave.total_days }}</td>
                    </tr>
                </table>
            </div>
        </div>

        <form method="post" class="needs-validation" novalidate>
            {% csrf_token %}
            
            {% if form.non_field_errors %}
            <div class="alert alert-danger" role="alert">
                {% for error in form.non_field_errors %}
                <p class="mb-0">{{ error }}</p>
                {% endfor %}
            </div>
            {% endif %}
            
            <div class="row">
                <div class="col-md-12 mb-3">
                    {{ form.status|as_crispy_field }}
                </div>
            </div>

            <div class="row">
                <div class="col-md-12 mb-3">
                    {{ form.rejection_reason|as_crispy_field }}
                </div>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="{% url 'Hr:leaves:list' %}" class="btn btn-light me-md-2">إلغاء</a>
                <button type="submit" class="btn btn-success px-4">
                    <i class="fas fa-check me-1"></i>
                    حفظ القرار
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // عرض/إخفاء حقل سبب الرفض حسب الحالة
        var statusSelect = document.getElementById('id_status');
        var rejectionReasonDiv = document.getElementById('div_id_rejection_reason');

        function toggleRejectionReason() {
            if (statusSelect.value === 'rejected') {
                rejectionReasonDiv.style.display = 'block';
            } else {
                rejectionReasonDiv.style.display = 'none';
            }
        }

        toggleRejectionReason();
        statusSelect.addEventListener('change', toggleRejectionReason);

        // تحقق من صحة النماذج
        (function () {
            'use strict'
            var forms = document.querySelectorAll('.needs-validation')
            Array.prototype.slice.call(forms)
                .forEach(function (form) {
                    form.addEventListener('submit', function (event) {
                        if (!form.checkValidity()) {
                            event.preventDefault()
                            event.stopPropagation()
                        }
                        form.classList.add('was-validated')
                    }, false)
                })
        })()
    });
</script>
{% endblock %}
