# Generated by Django 5.0.14 on 2025-05-07 05:28

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='TaskCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم التصنيف')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف التصنيف')),
                ('color', models.CharField(default='#3498db', max_length=20, verbose_name='اللون')),
                ('icon', models.CharField(default='fas fa-tasks', max_length=50, verbose_name='الأيقونة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'تصنيف المهام',
                'verbose_name_plural': 'تصنيفات المهام',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='EmployeeTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان المهمة')),
                ('description', models.TextField(verbose_name='وصف المهمة')),
                ('status', models.CharField(choices=[('pending', 'قيد الانتظار'), ('in_progress', 'قيد التنفيذ'), ('completed', 'مكتملة'), ('cancelled', 'ملغاة'), ('deferred', 'مؤجلة')], default='pending', max_length=20, verbose_name='الحالة')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('medium', 'متوسطة'), ('high', 'عالية'), ('urgent', 'عاجلة')], default='medium', max_length=20, verbose_name='الأولوية')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('due_date', models.DateField(verbose_name='تاريخ الاستحقاق')),
                ('completion_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإنجاز')),
                ('progress', models.PositiveIntegerField(default=0, help_text='من 0 إلى 100', verbose_name='نسبة الإنجاز (%)')),
                ('is_private', models.BooleanField(default=True, help_text='إذا كان خاصًا، فلن يراه إلا المنشئ والمشرف', verbose_name='خاص')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='assigned_employee_tasks', to=settings.AUTH_USER_MODEL, verbose_name='تم التكليف إلى')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_employee_tasks', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='tasks', to='employee_tasks.taskcategory', verbose_name='التصنيف')),
            ],
            options={
                'verbose_name': 'مهمة الموظف',
                'verbose_name_plural': 'مهام الموظفين',
                'ordering': ['-created_at', 'priority'],
            },
        ),
        migrations.CreateModel(
            name='TaskReminder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reminder_date', models.DateTimeField(verbose_name='تاريخ التذكير')),
                ('is_sent', models.BooleanField(default=False, verbose_name='تم الإرسال')),
                ('sent_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الإرسال')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('task', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reminders', to='employee_tasks.employeetask', verbose_name='المهمة')),
            ],
            options={
                'verbose_name': 'تذكير المهمة',
                'verbose_name_plural': 'تذكيرات المهام',
                'ordering': ['reminder_date'],
            },
        ),
        migrations.CreateModel(
            name='TaskStep',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField(verbose_name='وصف الخطوة')),
                ('completed', models.BooleanField(default=False, verbose_name='مكتملة')),
                ('completion_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإنجاز')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_employee_task_steps', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('task', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='steps', to='employee_tasks.employeetask', verbose_name='المهمة')),
            ],
            options={
                'verbose_name': 'خطوة المهمة',
                'verbose_name_plural': 'خطوات المهام',
                'ordering': ['created_at'],
            },
        ),
    ]
