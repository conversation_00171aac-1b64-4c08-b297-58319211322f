{% load static %}
{% load i18n %}
<!DOCTYPE html>
<html lang="{{ current_language }}" dir="{{ text_direction }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام واجهة API{% endblock %}</title>

    <!-- Bootstrap RTL/LTR -->
    {% if text_direction == 'rtl' %}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    {% else %}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    {% endif %}

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <!-- Google Fonts -->
    {% if system_settings.font_family == 'cairo' or current_font == 'Cairo' %}
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    {% elif system_settings.font_family == 'tajawal' or current_font == 'Tajawal' %}
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    {% elif system_settings.font_family == 'almarai' or current_font == 'Almarai' %}
    <link href="https://fonts.googleapis.com/css2?family=Almarai:wght@400;700&display=swap" rel="stylesheet">
    {% elif system_settings.font_family == 'ibm-plex-sans-arabic' or current_font == 'IBM Plex Sans Arabic' %}
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@400;500;600&display=swap" rel="stylesheet">
    {% elif system_settings.font_family == 'noto-sans-arabic' or current_font == 'Noto Sans Arabic' %}
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    {% else %}
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    {% endif %}

    <style>
        :root {
            --font-family: {% if system_settings.font_family %}
                {% if system_settings.font_family == 'cairo' %}'Cairo'
                {% elif system_settings.font_family == 'tajawal' %}'Tajawal'
                {% elif system_settings.font_family == 'almarai' %}'Almarai'
                {% elif system_settings.font_family == 'ibm-plex-sans-arabic' %}'IBM Plex Sans Arabic'
                {% elif system_settings.font_family == 'noto-sans-arabic' %}'Noto Sans Arabic'
                {% else %}'Cairo'
                {% endif %}
                {% else %}{{ current_font|default:'Cairo' }}{% endif %}, sans-serif;
            --primary-color: #00b4d8;
            --secondary-color: #0096c7;
            --success-color: #4caf50;
            --info-color: #00bcd4;
            --warning-color: #ff9800;
            --danger-color: #f44336;
            --light-color: #f5f5f5;
            --dark-color: #212121;
            --body-bg: #f5f7fa;
            --card-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        body {
            font-family: var(--font-family);
            background-color: var(--body-bg);
        }

        .navbar {
            background-color: var(--primary-color);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand {
            font-weight: bold;
            color: white !important;
        }
        
        .navbar-light .navbar-nav .nav-link {
            color: rgba(255, 255, 255, 0.85);
        }

        .navbar-light .navbar-nav .nav-link:hover {
            color: white;
        }

        .with-sidebar {
            padding-left: 0;
            padding-right: 0;
        }

        .page-wrapper {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .content-wrapper {
            display: flex;
            flex: 1;
        }

        .sidebar {
            width: 250px;
            background-color: var(--secondary-color);
            color: white;
            padding-top: 20px;
            transition: all 0.3s;
            position: fixed;
            height: 100%;
            z-index: 100;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }

        .sidebar-header h3 {
            margin: 0;
            font-size: 1.5rem;
            color: white;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-menu-item {
            margin-bottom: 5px;
        }

        .sidebar-menu-item a {
            display: flex;
            align-items: center;
            padding: 10px 20px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s;
        }

        .sidebar-menu-item a:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .sidebar-menu-item a i {
            margin-left: 10px;
            width: 20px;
            text-align: center;
        }

        .sidebar-menu-item.active a {
            background-color: var(--primary-color);
            color: white;
            font-weight: bold;
        }

        .main-content {
            flex: 1;
            padding: 20px;
            margin-right: 250px;
            transition: all 0.3s;
        }

        .sidebar-collapsed .sidebar {
            width: 70px;
        }

        .sidebar-collapsed .sidebar-header h3,
        .sidebar-collapsed .sidebar-menu-item a span {
            display: none;
        }

        .sidebar-collapsed .main-content {
            margin-right: 70px;
        }

        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            border: none;
        }

        .card-header {
            background-color: white;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            border-radius: 10px 10px 0 0 !important;
            padding: 15px 20px;
        }

        .card-title {
            margin-bottom: 0;
            font-weight: 600;
            color: var(--secondary-color);
        }

        .card-body {
            padding: 20px;
        }

        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #eaeaea;
            overflow-x: auto;
        }

        code {
            color: #e83e8c;
        }

        .endpoint-card {
            border-radius: 5px;
            padding: 10px 15px;
            margin-bottom: 10px;
            background-color: rgba(0, 180, 216, 0.05);
            border-right: 3px solid var(--primary-color);
        }

        .endpoint-method {
            font-weight: 600;
            padding: 2px 6px;
            border-radius: 4px;
            color: white;
            font-size: 0.8rem;
            display: inline-block;
            margin-left: 8px;
        }

        .method-get {
            background-color: #61affe;
        }

        .method-post {
            background-color: #49cc90;
        }

        .method-put {
            background-color: #fca130;
        }

        .method-delete {
            background-color: #f93e3e;
        }

        .method-patch {
            background-color: #50e3c2;
        }

        .api-key {
            font-family: monospace;
            padding: 8px 12px;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .copy-btn {
            cursor: pointer;
            color: #6c757d;
        }

        .copy-btn:hover {
            color: var(--primary-color);
        }

        /* Charts */
        .chart-container {
            position: relative;
            height: 300px;
            margin-bottom: 20px;
        }

        /* Chat styles */
        .chat-container {
            height: 70vh;
            display: flex;
            flex-direction: column;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .chat-message {
            margin-bottom: 15px;
            display: flex;
        }

        .chat-message.user {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 10px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .user .message-content {
            background-color: var(--primary-color);
            color: white;
            border-bottom-right-radius: 0;
        }

        .ai .message-content {
            background-color: white;
            border-bottom-left-radius: 0;
        }

        .chat-input-container {
            display: flex;
        }

        .chat-input {
            flex: 1;
            border-radius: 30px !important;
            padding-right: 15px;
            padding-left: 15px;
        }

        .send-button {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            padding: 0;
            margin-right: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Responsive Adjustments */
        @media (max-width: 768px) {
            .sidebar {
                width: 0;
                padding: 0;
                transform: translateX(100%);
                right: -250px;
                z-index: 1050;
            }

            .main-content {
                margin-right: 0;
            }

            .sidebar.show {
                width: 250px;
                padding-top: 20px;
                transform: translateX(0);
                right: 0;
            }

            /* Overlay for mobile sidebar */
            .sidebar-overlay {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 1040;
            }

            .sidebar-overlay.show {
                display: block;
            }

            .chat-container {
                height: 60vh;
            }

            .message-content {
                max-width: 85%;
            }
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="page-wrapper">
        <!-- Navbar -->
        <nav class="navbar navbar-expand-lg navbar-light with-sidebar">
            <div class="container-fluid">
                <div class="d-flex align-items-center">
                    <button id="sidebarToggle" class="btn btn-light me-2">
                        <i class="fas fa-bars"></i>
                    </button>
                    <a class="navbar-brand" href="{% url 'api:dashboard' %}">
                        <i class="fas fa-code me-2"></i>
                        <span>واجهة API</span>
                    </a>
                </div>
                <div class="d-flex align-items-center">
                    <div class="user-info me-3 d-flex align-items-center">
                        <i class="fas fa-user-circle text-light me-2 fs-5"></i>
                        <span class="text-light fw-bold">{{ request.user.username }}</span>
                    </div>
                    <a href="{% url 'accounts:logout' %}" class="btn btn-outline-light btn-sm">
                        <i class="fas fa-sign-out-alt me-1"></i> تسجيل الخروج
                    </a>
                </div>
            </div>
        </nav>

        <div class="content-wrapper">
            <!-- Sidebar -->
            <aside class="sidebar">
                <div class="sidebar-header">
                    <h3><i class="fas fa-code me-2"></i> واجهة API</h3>
                </div>
                <ul class="sidebar-menu">
                    <li class="sidebar-menu-item {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}">
                        <a href="{% url 'api:dashboard' %}">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>لوحة التحكم</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item {% if request.resolver_match.url_name == 'ai_chat' %}active{% endif %}">
                        <a href="{% url 'api:ai_chat' %}">
                            <i class="fas fa-robot"></i>
                            <span>المحادثة الذكية</span>
                        </a>
                    </li>
                    {% if perms.api.ai_configuration %}
                    <li class="sidebar-menu-item {% if request.resolver_match.url_name == 'ai_settings' %}active{% endif %}">
                        <a href="{% url 'api:ai_settings' %}">
                            <i class="fas fa-cogs"></i>
                            <span>إعدادات الذكاء الاصطناعي</span>
                        </a>
                    </li>
                    {% endif %}
                    <li class="sidebar-menu-item {% if request.resolver_match.url_name == 'data_analysis' %}active{% endif %}">
                        <a href="{% url 'api:data_analysis' %}">
                            <i class="fas fa-chart-line"></i>
                            <span>تحليل البيانات</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item {% if request.resolver_match.url_name == 'usage_stats' %}active{% endif %}">
                        <a href="{% url 'api:usage_stats' %}">
                            <i class="fas fa-chart-bar"></i>
                            <span>إحصائيات الاستخدام</span>
                        </a>
                    </li>
                    {% if perms.api.api_keys_management %}
                    <li class="sidebar-menu-item {% if request.resolver_match.url_name == 'create_key' %}active{% endif %}">
                        <a href="{% url 'api:create_key' %}">
                            <i class="fas fa-key"></i>
                            <span>إدارة مفاتيح API</span>
                        </a>
                    </li>
                    {% endif %}
                    <li class="sidebar-menu-item">
                        <a href="{% url 'accounts:home' %}">
                            <i class="fas fa-home"></i>
                            <span>الرئيسية</span>
                        </a>
                    </li>
                </ul>
            </aside>

            <!-- Main Content -->
            <div class="main-content">
                {% if messages %}
                    <div class="messages">
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}

                <!-- Page Header -->
                <div class="page-header mb-4">
                    <h2 class="mb-0">{% block page_title %}واجهة API{% endblock %}</h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            {% block breadcrumb %}
                            <li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
                            <li class="breadcrumb-item active">واجهة API</li>
                            {% endblock %}
                        </ol>
                    </nav>
                </div>

                {% block content %}{% endblock %}
            </div>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Sidebar Toggle
            const sidebarToggle = document.getElementById('sidebarToggle');
            const pageWrapper = document.querySelector('.page-wrapper');
            const sidebar = document.querySelector('.sidebar');

            // Create overlay element for mobile
            const overlay = document.createElement('div');
            overlay.className = 'sidebar-overlay';
            document.body.appendChild(overlay);

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    pageWrapper.classList.toggle('sidebar-collapsed');

                    // For mobile devices
                    if (window.innerWidth <= 768) {
                        sidebar.classList.toggle('show');
                        overlay.classList.toggle('show');
                    }
                });
            }

            // Close sidebar when clicking on overlay
            overlay.addEventListener('click', function() {
                sidebar.classList.remove('show');
                overlay.classList.remove('show');
                pageWrapper.classList.remove('sidebar-collapsed');
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth > 768) {
                    overlay.classList.remove('show');
                }
            });

            // Auto-dismiss alerts after 5 seconds
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert');
                alerts.forEach(function(alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                });
            }, 5000);

            // Highlight active menu item
            const currentPath = window.location.pathname;
            const menuItems = document.querySelectorAll('.sidebar-menu-item');

            menuItems.forEach(item => {
                const link = item.querySelector('a');
                if (link && currentPath.includes(link.getAttribute('href'))) {
                    item.classList.add('active');
                }
            });

            // Copy API key functionality
            const copyButtons = document.querySelectorAll('.copy-btn');
            copyButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    const keyElement = this.closest('.api-key').querySelector('.key-value');
                    const tempInput = document.createElement('input');
                    tempInput.value = keyElement.textContent.trim();
                    document.body.appendChild(tempInput);
                    tempInput.select();
                    document.execCommand('copy');
                    document.body.removeChild(tempInput);
                    
                    // Show feedback
                    this.innerHTML = '<i class="fas fa-check"></i>';
                    setTimeout(() => {
                        this.innerHTML = '<i class="fas fa-copy"></i>';
                    }, 2000);
                });
            });
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
