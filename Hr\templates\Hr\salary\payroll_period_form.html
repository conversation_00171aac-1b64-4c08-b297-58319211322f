{% extends "Hr/base_hr.html" %}
{% load crispy_forms_tags %}

{% block title %}{{ title }}{% endblock title %}

{% block content %}
<div class="container mt-4">
    <h2>{{ title }}</h2>

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }}">{{ message }}</div>
        {% endfor %}
    {% endif %}

    <form method="post" novalidate>
        {% csrf_token %}
        {{ form|crispy }}
        <button type="submit" class="btn btn-success">Save</button>
        <a href="{% url 'Hr:payroll_period_list' %}" class="btn btn-secondary">Cancel</a>
    </form>
</div>
{% endblock content %}
