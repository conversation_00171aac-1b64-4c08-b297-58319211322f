<!-- templates/inventory/product_movements.html -->
{% extends 'inventory/base_inventory.html' %}
{% load inventory_permission_tags %}

{% block title %}حركات الصنف: {{ product.name }} - نظام إدارة المخزن{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 mb-4">
        <div class="card shadow">
            <!-- Card Header -->
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="fas fa-exchange-alt me-2"></i>
                    حركات الصنف: {{ product.name }}
                </h4>
                <div>
                    <a href="{% url 'inventory:product_movement_list' %}" class="btn btn-light">
                        <i class="fas fa-arrow-right me-1"></i> العودة إلى القائمة
                    </a>
                </div>
            </div>

            <!-- Product Information -->
            <div class="card-body border-bottom">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-sm border-0">
                            <tr>
                                <th style="width: 150px;">رقم الصنف:</th>
                                <td>{{ product.product_id }}</td>
                            </tr>
                            <tr>
                                <th>الاسم:</th>
                                <td>{{ product.name }}</td>
                            </tr>
                            <tr>
                                <th>وحدة القياس:</th>
                                <td>{{ product.unit|default:"-" }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-sm border-0">
                            <tr>
                                <th style="width: 150px;">الرصيد الافتتاحي:</th>
                                <td>{{ product.initial_quantity }}</td>
                            </tr>
                            <tr>
                                <th>الرصيد الحالي:</th>
                                <td>{{ product.quantity }}</td>
                            </tr>
                            <tr>
                                <th>الموقع:</th>
                                <td>{{ product.location|default:"-" }}</td>
                            </tr>
                        </table>
                        
                        <!-- مجاميع الحركات -->
                        <div class="card mt-3">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">
                                    <i class="fas fa-calculator me-2"></i>
                                    مجاميع الحركات
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-6">
                                        <div class="d-flex flex-column">
                                            <span class="fw-bold">مجموع الإضافة:</span>
                                            <span class="fs-5 text-success">{{ total_additions }}</span>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="d-flex flex-column">
                                            <span class="fw-bold">مجموع الصرف:</span>
                                            <span class="fs-5 text-danger">{{ total_disbursements }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-6">
                                        <div class="d-flex flex-column">
                                            <span class="fw-bold">مجموع مرتجع المورد:</span>
                                            <span class="fs-5 text-warning">{{ total_supplier_returns }}</span>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="d-flex flex-column">
                                            <span class="fw-bold">مجموع مرتجع العميل:</span>
                                            <span class="fs-5 text-info">{{ total_customer_returns }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Transaction History -->
            <div class="card-body">
                <h5 class="mb-3">سجل الحركات</h5>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>#</th>
                                <th>تاريخ الحركة</th>
                                <th>نوع الحركة</th>
                                <th>رقم الإذن</th>
                                <th>إضافة</th>
                                <th>صرف</th>
                                <th>مرتجع عميل</th>
                                <th>مرتجع مورد</th>
                                <th>الجهة</th>
                                <th>ملاحظات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in movements %}
                            <tr>
                                <td>{{ forloop.counter }}</td>
                                <td>{{ item.voucher.date|date:"Y-m-d" }}</td>
                                <td>
                                    {% if item.voucher.voucher_type == 'إذن اضافة' %}
                                        <span class="badge bg-success">إضافة</span>
                                    {% elif item.voucher.voucher_type == 'إذن صرف' %}
                                        <span class="badge bg-danger">صرف</span>
                                    {% elif item.voucher.voucher_type == 'اذن مرتجع عميل' %}
                                        <span class="badge bg-info">مرتجع عميل</span>
                                    {% elif item.voucher.voucher_type == 'إذن مرتجع مورد' %}
                                        <span class="badge bg-warning">مرتجع مورد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'inventory:voucher_detail' item.voucher.voucher_number %}">
                                        {{ item.voucher.voucher_number }}
                                    </a>
                                </td>
                                <td>
                                    {% if item.quantity_added and item.voucher.voucher_type == 'إذن اضافة' %}
                                        {{ item.quantity_added }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if item.quantity_disbursed and item.voucher.voucher_type == 'إذن صرف' %}
                                        {{ item.quantity_disbursed }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if item.quantity_added and item.voucher.voucher_type == 'اذن مرتجع عميل' %}
                                        {{ item.quantity_added }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if item.quantity_disbursed and item.voucher.voucher_type == 'إذن مرتجع مورد' %}
                                        {{ item.quantity_disbursed }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if item.voucher.supplier %}
                                        <i class="fas fa-truck-loading"></i> {{ item.voucher.supplier }}
                                    {% elif item.voucher.department %}
                                        <i class="fas fa-building"></i> {{ item.voucher.department }}
                                    {% elif item.voucher.customer %}
                                        <i class="fas fa-user"></i> {{ item.voucher.customer }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {{ item.voucher.notes|truncatechars:30|default:"-" }}
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="10" class="text-center py-4">
                                    <i class="fas fa-exclamation-circle text-muted mb-2" style="font-size: 2rem;"></i>
                                    <p class="mb-0 text-muted">لا توجد حركات مسجلة لهذا الصنف</p>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
