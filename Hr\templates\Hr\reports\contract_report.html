{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load django_permissions %}

{% block content %}
<div class="container">
    <h1 class="my-4">تقرير العقود</h1>

    <form method="get" class="mb-4">
        <div class="row">
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="contract_id" class="form-label">رقم العقد</label>
                    <input type="text" class="form-control" id="contract_id" name="contract_id" value="{{ request.GET.contract_id }}">
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="employee_name" class="form-label">اسم الموظف</label>
                    <input type="text" class="form-control" id="employee_name" name="employee_name" value="{{ request.GET.employee_name }}">
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="department" class="form-label">القسم</label>
                    <input type="text" class="form-control" id="department" name="department" value="{{ request.GET.department }}">
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="start_date" class="form-label">تاريخ البدء</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="{{ request.GET.start_date }}">
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="end_date" class="form-label">تاريخ الانتهاء</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="{{ request.GET.end_date }}">
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="status" class="form-label">الحالة</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="active" {% if request.GET.status == 'active' %}selected{% endif %}>نشط</option>
                        <option value="inactive" {% if request.GET.status == 'inactive' %}selected{% endif %}>غير نشط</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="actions text-end">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-search me-1"></i>
                بحث
            </button>
            <a href="{% url 'Hr:reports:report_detail' 'contracts' %}" class="btn btn-secondary">
                <i class="fas fa-redo me-1"></i>
                إعادة تعيين
            </a>
            {% if perms.Hr.export_contract_data or user|is_admin %}
            <div class="btn-group ms-2">
                <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-download me-1"></i>
                    تصدير
                </button>
                <ul class="dropdown-menu">
                    <li>
                        <a class="dropdown-item" href="?{{ request.GET.urlencode }}&export=excel">
                            <i class="fas fa-file-excel me-1 text-success"></i>
                            Excel
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="?{{ request.GET.urlencode }}&export=csv">
                            <i class="fas fa-file-csv me-1 text-info"></i>
                            CSV
                        </a>
                    </li>
                </ul>
            </div>
            {% endif %}
        </div>
    </form>

    <div class="table-responsive">
        <table class="table table-bordered table-hover">
            <thead>
                <tr>
                    <th>#</th>
                    <th>رقم العقد</th>
                    <th>اسم الموظف</th>
                    <th>القسم</th>
                    <th>تاريخ البدء</th>
                    <th>تاريخ الانتهاء</th>
                    <th>الحالة</th>
                    <th class="text-center">العمليات</th>
                </tr>
            </thead>
            <tbody>
                {% for contract in contracts %}
                <tr>
                    <td>{{ forloop.counter }}</td>
                    <td>{{ contract.contract_id }}</td>
                    <td>{{ contract.employee_name }}</td>
                    <td>{{ contract.department }}</td>
                    <td>{{ contract.start_date }}</td>
                    <td>{{ contract.end_date }}</td>
                    <td>{{ contract.get_status_display }}</td>
                    <td class="text-center">
                        {% if perms.Hr.view_contract_detail or user|is_admin %}
                        <a href="{% url 'Hr:contracts:detail' contract.id %}" class="btn btn-sm btn-primary">
                            <i class="fas fa-eye"></i>
                        </a>
                        {% endif %}
                        {% if perms.Hr.print_contract or user|is_admin %}
                        <a href="{% url 'Hr:contracts:print' contract.id %}" class="btn btn-sm btn-info">
                            <i class="fas fa-print"></i>
                        </a>
                        {% endif %}
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="8" class="text-center">لا توجد بيانات لعرضها</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}