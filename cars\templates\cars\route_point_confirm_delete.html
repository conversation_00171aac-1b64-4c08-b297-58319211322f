{% extends 'cars\base.html' %}

{% block title %}حذف نقطة خط سير - نظام إدارة نشاط النقل{% endblock %}

{% block header %}تأكيد حذف نقطة خط سير{% endblock %}

{% block content %}
    <div class="card">
        <div class="card-body">
            <div class="alert alert-danger">
                <h5 class="alert-heading">تحذير!</h5>
                <p>هل أنت متأكد من حذف نقطة خط السير "{{ route_point.point_name }}"؟</p>
                <p>هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            
            <div class="card mb-3">
                <div class="card-header bg-light">
                    <h5 class="mb-0">بيانات نقطة خط السير</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>اسم النقطة:</strong> {{ route_point.point_name }}</p>
                            <p><strong>وقت المغادرة:</strong> {{ route_point.departure_time }}</p>
                            <p><strong>الترتيب:</strong> {{ route_point.order }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>السيارة:</strong> {{ car.car_name }} ({{ car.car_code }})</p>
                            <p><strong>عدد الموظفين:</strong> {{ route_point.employees.count }}</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <form method="post">
                {% csrf_token %}
                <div class="d-flex justify-content-between">
                    <a href="{% url 'cars:route_point_list' car.id %}" class="btn btn-secondary">
                        <i class="bi bi-x-circle"></i> إلغاء
                    </a>
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash"></i> تأكيد الحذف
                    </button>
                </div>
            </form>
        </div>
    </div>
{% endblock %}