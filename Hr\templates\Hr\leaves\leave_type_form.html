{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}{{ title }} - نظام الدولية{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'Hr/css/leaves.css' %}">
{% endblock %}

{% block page_title %}{{ title }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:employees:list' %}">الموظفين</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:leave_types:list' %}">أنواع الإجازات</a></li>
<li class="breadcrumb-item active">{{ title }}</li>
{% endblock %}

{% block content %}
<div class="card shadow-sm">
    <div class="card-header bg-white d-flex justify-content-between align-items-center py-3">
        <h5 class="mb-0">
            <i class="fas fa-edit me-2"></i>
            {{ title }}
        </h5>
        <a href="{% url 'Hr:leave_types:list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة للقائمة
        </a>
    </div>
    <div class="card-body">
        <form method="post" class="needs-validation" novalidate>
            {% csrf_token %}
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    {{ form.name|as_crispy_field }}
                </div>
                <div class="col-md-6 mb-3">
                    {{ form.max_days_per_year|as_crispy_field }}
                </div>
            </div>

            <div class="row">
                <div class="col-md-12 mb-3">
                    {{ form.description|as_crispy_field }}
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <div class="form-check form-switch">
                        {{ form.paid }}
                        <label class="form-check-label" for="{{ form.paid.id_for_label }}">
                            {{ form.paid.label }}
                        </label>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="form-check form-switch">
                        {{ form.requires_approval }}
                        <label class="form-check-label" for="{{ form.requires_approval.id_for_label }}">
                            {{ form.requires_approval.label }}
                        </label>
                    </div>
                </div>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="{% url 'Hr:leave_types:list' %}" class="btn btn-light me-md-2">إلغاء</a>
                <button type="submit" class="btn btn-primary px-4">
                    <i class="fas fa-save me-1"></i>
                    حفظ
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تحقق من صحة النماذج
        (function () {
            'use strict'
            var forms = document.querySelectorAll('.needs-validation')
            Array.prototype.slice.call(forms)
                .forEach(function (form) {
                    form.addEventListener('submit', function (event) {
                        if (!form.checkValidity()) {
                            event.preventDefault()
                            event.stopPropagation()
                        }
                        form.classList.add('was-validated')
                    }, false)
                })
        })()
    });
</script>
{% endblock %}
