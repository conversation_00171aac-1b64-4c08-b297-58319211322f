# Generated by Django 5.0.14 on 2025-05-25 00:04

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AIProvider',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(choices=[('gemini', 'Google Gemini'), ('openai', 'OpenAI GPT'), ('claude', 'Anthropic Claude'), ('huggingface', 'Hugging Face'), ('ollama', 'Ollama (Local)'), ('custom', 'مخصص')], max_length=100, unique=True)),
                ('display_name', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('api_endpoint', models.URLField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('requires_api_key', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'مقدم خدمة الذكاء الاصطناعي',
                'verbose_name_plural': 'مقدمو خدمات الذكاء الاصطناعي',
                'ordering': ['display_name'],
            },
        ),
        migrations.CreateModel(
            name='AIConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('api_key', models.CharField(help_text='مفتاح API', max_length=500)),
                ('model_name', models.CharField(default='gemini-1.5-flash', help_text='اسم النموذج', max_length=200)),
                ('is_default', models.BooleanField(default=False, help_text='الإعداد الافتراضي')),
                ('is_active', models.BooleanField(default=True)),
                ('max_tokens', models.IntegerField(default=1000, help_text='الحد الأقصى للرموز')),
                ('temperature', models.FloatField(default=0.7, help_text='درجة الإبداع (0-1)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ai_configurations', to=settings.AUTH_USER_MODEL)),
                ('provider', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='api.aiprovider')),
            ],
            options={
                'verbose_name': 'إعداد الذكاء الاصطناعي',
                'verbose_name_plural': 'إعدادات الذكاء الاصطناعي',
                'ordering': ['-is_default', 'provider__display_name'],
                'unique_together': {('user', 'provider')},
            },
        ),
    ]
