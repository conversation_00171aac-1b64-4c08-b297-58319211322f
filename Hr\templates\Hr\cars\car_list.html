{% extends 'Hr/base_hr.html' %}
{% load static %}

{% block title %}قائمة السيارات - نظام الدولية{% endblock %}

{% block page_title %}قائمة سيارات نقل الموظفين{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموارد البشرية</a></li>
<li class="breadcrumb-item active">قائمة السيارات</li>
{% endblock %}

{% block content %}
<div class="card shadow-sm">
    <div class="card-header bg-white d-flex justify-content-between align-items-center py-3">
        <h5 class="mb-0">
            <i class="fas fa-car me-2 text-primary"></i>
            سيارات نقل الموظفين
        </h5>
        <a href="{% url 'Hr:cars:create' %}" class="btn btn-primary">
            <i class="fas fa-plus-circle me-1"></i>
            إضافة سيارة جديدة
        </a>
    </div>
    <div class="card-body">
        {% if cars %}
        <div class="table-responsive">
            <table class="table table-hover align-middle">
                <thead class="table-light">
                    <tr>
                        <th>رقم السيارة</th>
                        <th>الاسم/الوصف</th>
                        <th>رقم اللوحة</th>
                        <th>السائق</th>
                        <th>تاريخ انتهاء الرخصة</th>
                        <th class="text-center">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for car in cars %}
                    <tr>
                        <td>{{ car.car_id }}</td>
                        <td>{{ car.car_name|default:"-" }}</td>
                        <td>{{ car.car_plate_number|default:"-" }}</td>
                        <td>{{ car.driver_name|default:"-" }}</td>
                        <td>
                            {% if car.car_license_expiration_date %}
                            {{ car.car_license_expiration_date|date:"Y-m-d" }}
                            {% if car.days_to_license_expiry < 30 %}
                            <span class="badge bg-danger ms-1">متبقي {{ car.days_to_license_expiry }} يوم</span>
                            {% endif %}
                            {% else %}
                            -
                            {% endif %}
                        </td>
                        <td class="text-center">
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'Hr:cars:detail' car.car_id %}" class="btn btn-outline-primary" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'Hr:cars:edit' car.car_id %}" class="btn btn-outline-secondary" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'Hr:cars:delete' car.car_id %}" class="btn btn-outline-danger" title="حذف">
                                    <i class="fas fa-trash-alt"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-4">
            <p class="mb-0">لا توجد سيارات مسجلة حالياً.</p>
            <a href="{% url 'Hr:cars:create' %}" class="btn btn-primary mt-3">
                <i class="fas fa-plus-circle me-1"></i>
                إضافة سيارة
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
