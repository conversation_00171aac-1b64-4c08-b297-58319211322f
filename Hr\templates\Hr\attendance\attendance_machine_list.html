{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ page_title }} - نظام الدولية{% endblock %}

{% block page_title %}{{ page_title }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموظفين</a></li>
<li class="breadcrumb-item active">{{ page_title }}</li>
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">أجهزة البصمة</h5>
        <a href="{% url 'Hr:attendance:attendance_machine_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة جهاز جديد
        </a>
    </div>
    <div class="card-body">
        {% if machines %}
        <div class="table-responsive">
            <table class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>الاسم</th>
                        <th>عنوان IP</th>
                        <th>المنفذ</th>
                        <th>نوع الجهاز</th>
                        <th>الموقع</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for machine in machines %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ machine.name }}</td>
                        <td>{{ machine.ip_address }}</td>
                        <td>{{ machine.port }}</td>
                        <td>{{ machine.get_machine_type_display }}</td>
                        <td>{{ machine.location }}</td>
                        <td>
                            {% if machine.is_active %}
                            <span class="badge bg-success">نشط</span>
                            {% else %}
                            <span class="badge bg-danger">غير نشط</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'Hr:attendance:attendance_machine_edit' machine.pk %}" class="btn btn-primary">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                                <a href="{% url 'Hr:attendance:attendance_machine_delete' machine.pk %}" class="btn btn-danger">
                                    <i class="fas fa-trash"></i> حذف
                                </a>
                                <a href="{% url 'Hr:attendance:fetch_attendance_data' %}?machine={{ machine.pk }}" class="btn btn-info">
                                    <i class="fas fa-sync"></i> جلب البيانات
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            لا توجد أجهزة بصمة مسجلة حتى الآن.
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
