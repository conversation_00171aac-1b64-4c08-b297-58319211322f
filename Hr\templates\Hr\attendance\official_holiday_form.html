{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ page_title }} - نظام الدولية{% endblock %}

{% block page_title %}{{ page_title }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموظفين</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:attendance:official_holiday_list' %}">الإجازات الرسمية</a></li>
<li class="breadcrumb-item active">{{ page_title }}</li>
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">{{ page_title }}</h5>
    </div>
    <div class="card-body">
        <form method="post" novalidate>
            {% csrf_token %}

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.name.id_for_label }}" class="form-label">{{ form.name.label }}</label>
                        {{ form.name }}
                        {% if form.name.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.name.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.date.id_for_label }}" class="form-label">{{ form.date.label }}</label>
                        {{ form.date }}
                        {% if form.date.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.date.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }}</label>
                        {{ form.description }}
                        {% if form.description.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.description.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.is_recurring }}
                            <label class="form-check-label" for="{{ form.is_recurring.id_for_label }}">
                                {{ form.is_recurring.label }}
                            </label>
                        </div>
                        {% if form.is_recurring.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.is_recurring.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                        <small class="form-text text-muted">حدد إذا كانت هذه الإجازة تتكرر سنوياً في نفس التاريخ</small>
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-between">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ
                </button>
                <a href="{% url 'Hr:attendance:official_holiday_list' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}