{% extends 'inventory/base_inventory.html' %}
{% load static %}
{% load inventory_permission_tags %}

{% block title %}لوحة التحكم - نظام إدارة المخزن{% endblock %}

{% block extra_css %}
<style>
    /* Enhanced Dashboard Styles */
    .stats-card {
        border-radius: 15px;
        border: none;
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        overflow: hidden;
        height: 100%;
    }
    
    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 24px rgba(0, 0, 0, 0.12);
    }
    
    .stats-icon-container {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 15px;
    }
    
    .stats-content {
        flex: 1;
    }
    
    .stats-number {
        font-size: 2rem;
        font-weight: 700;
        margin: 0;
        line-height: 1;
    }
    
    .stats-label {
        color: #6c757d;
        font-size: 0.9rem;
        margin: 0;
        margin-top: 5px;
    }
    
    .stats-footer {
        padding: 12px 15px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: rgba(0, 0, 0, 0.03);
    }
    
    .stats-footer a {
        display: flex;
        align-items: center;
        font-weight: 500;
        transition: transform 0.2s;
    }
    
    .stats-footer a:hover {
        transform: translateX(-5px);
    }
    
    /* Quick Action Cards */
    .action-card {
        border-radius: 12px;
        transition: all 0.3s ease;
        background-color: #f8f9fa;
        padding: 20px;
        text-align: center;
        cursor: pointer;
        height: 100%;
    }
    
    .action-card:hover {
        background-color: #fff;
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
    
    .action-icon {
        width: 65px;
        height: 65px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 15px auto;
        border-radius: 50%;
    }
    
    .action-card h6 {
        margin-top: 10px;
        margin-bottom: 5px;
        font-weight: 600;
    }
    
    /* Low Stock Table */
    .table-container {
        border-radius: 10px;
        overflow: hidden;
    }
    
    .stock-table {
        margin-bottom: 0;
    }
    
    .stock-table th {
        background-color: #f8f9fa;
        color: #495057;
        font-weight: 600;
        border-top: none;
    }
    
    .stock-table td {
        vertical-align: middle;
    }
    
    .low-quantity {
        padding: 0.25rem 0.5rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
        background-color: rgba(220, 53, 69, 0.1);
        color: #dc3545;
    }
    
    /* Empty state */
    .empty-state {
        padding: 50px 20px;
        text-align: center;
    }
    
    .empty-state .icon {
        font-size: 4rem;
        color: #28a745;
        margin-bottom: 20px;
    }
    
    /* Animation */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .fade-in {
        animation: fadeIn 0.5s ease-out forwards;
    }
    
    /* Card header styling */
    .card-header-custom {
        background-color: #fff;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        padding: 1rem 1.25rem;
    }
    
    .card-header-custom h5 {
        font-weight: 600;
    }
    
    .card-header-icon {
        margin-left: 0.5rem;
    }
    
    /* Card shadows and hover */
    .hover-shadow {
        transition: all 0.3s ease;
    }
    
    .hover-shadow:hover {
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        transform: translateY(-5px);
    }
</style>
{% endblock %}

{% block content %}
<!-- Stats Cards Row -->
<div class="row ps-4 g-4 mb-4 fade-in" style="animation-delay: 0.1s">
    <!-- Pending Permits Card -->
    <div class="col-md-3">
        <div class="stats-card">
            <div class="card-body p-4">
                <div class="d-flex align-items-center">
                    <div class="stats-icon-container bg-info-subtle">
                        <i class="fas fa-clipboard-list fa-2x text-info"></i>
                    </div>
                    <div class="stats-content">
                        <h2 class="stats-number">{{ pending_permits }}</h2>
                        <p class="stats-label">الأذونات المعلقة</p>
                    </div>
                </div>
            </div>
            <div class="card-footer border-0 bg-info text-white p-0">
                <a href="{% url 'inventory:invoice_list' %}" class="d-block p-3 text-white text-decoration-none text-center">
                    عرض التفاصيل <i class="fas fa-arrow-left ms-1"></i>
                </a>
            </div>
        </div>
    </div>
    
    <!-- Completed Permits Card -->
    <div class="col-md-3">
        <div class="stats-card">
            <div class="card-body p-4">
                <div class="d-flex align-items-center">
                    <div class="stats-icon-container bg-success-subtle">
                        <i class="fas fa-check-circle fa-2x text-success"></i>
                    </div>
                    <div class="stats-content">
                        <h2 class="stats-number">{{ completed_permits }}</h2>
                        <p class="stats-label">المنتهي التنفيذ</p>
                    </div>
                </div>
            </div>
            <div class="card-footer border-0 bg-success text-white p-0">
                <a href="{% url 'inventory:invoice_list' %}" class="d-block p-3 text-white text-decoration-none text-center">
                    عرض التفاصيل <i class="fas fa-arrow-left ms-1"></i>
                </a>
            </div>
        </div>
    </div>
    
    <!-- Total Permits Card -->
    <div class="col-md-3">
        <div class="stats-card">
            <div class="card-body p-4">
                <div class="d-flex align-items-center">
                    <div class="stats-icon-container bg-primary-subtle">
                        <i class="fas fa-file-invoice fa-2x text-primary"></i>
                    </div>
                    <div class="stats-content">
                        <h2 class="stats-number">{{ total_permits }}</h2>
                        <p class="stats-label">إجمالي الأذونات</p>
                    </div>
                </div>
            </div>
            <div class="card-footer border-0 bg-primary text-white p-0">
                <a href="{% url 'inventory:invoice_list' %}" class="d-block p-3 text-white text-decoration-none text-center">
                    عرض التفاصيل <i class="fas fa-arrow-left ms-1"></i>
                </a>
            </div>
        </div>
    </div>
    
    <!-- Low Stock Alert Card -->
    <div class="col-md-3">
        <div class="stats-card">
            <div class="card-body p-4">
                <div class="d-flex align-items-center">
                    <div class="stats-icon-container bg-warning-subtle">
                        <i class="fas fa-exclamation-triangle fa-2x text-warning"></i>
                    </div>
                    <div class="stats-content">
                        <h2 class="stats-number">{{ low_stock_count }}</h2>
                        <p class="stats-label">تنبيه المخزون</p>
                    </div>
                </div>
            </div>
            <div class="card-footer border-0 bg-warning text-white p-0">
                <a href="#low-stock-section" class="d-block p-3 text-white text-decoration-none text-center">
                    عرض التفاصيل <i class="fas fa-arrow-left ms-1"></i>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions Section -->
<div class="row ps-4 mb-4 fade-in" style="animation-delay: 0.2s">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header-custom d-flex align-items-center">
                <i class="fas fa-bolt card-header-icon text-primary"></i>
                <h5 class="mb-0">إجراءات سريعة</h5>
            </div>
            <div class="card-body p-4">
                <div class="row g-3">
                    <!-- Add Product -->
                    {% if perms.inventory.add_product or user.is_superuser %}
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-success shadow h-100 py-2">
                            <a href="{% url 'inventory:add_product' %}" class="text-decoration-none">
                                <div class="action-card">
                                    <div class="action-icon bg-primary-subtle">
                                        <i class="fas fa-box fa-2x text-primary"></i>
                                    </div>
                                    <h6>إضافة صنف جديد</h6>
                                    <small class="text-muted">إضافة قطعة غيار جديدة للمخزن</small>
                                </div>
                            </a>
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- Add Invoice -->
                    {% if perms.inventory.add_invoice or user.is_superuser %}
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-info shadow h-100 py-2">
                            <a href="{% url 'inventory:create_invoice' %}" class="text-decoration-none">
                                <div class="action-card">
                                    <div class="action-icon bg-success-subtle">
                                        <i class="fas fa-file-invoice fa-2x text-success"></i>
                                    </div>
                                    <h6>فاتورة جديدة</h6>
                                    <small class="text-muted">إنشاء فاتورة توريد أو صرف</small>
                                </div>
                            </a>
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- Stock Report -->
                    {% if perms.inventory.view_stockreport or user.is_superuser %}
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-warning shadow h-100 py-2">
                            <a href="{% url 'inventory:stock_report' %}" class="text-decoration-none">
                                <div class="action-card">
                                    <div class="action-icon bg-info-subtle">
                                        <i class="fas fa-chart-bar fa-2x text-info"></i>
                                    </div>
                                    <h6>تقرير المخزون</h6>
                                    <small class="text-muted">عرض تقارير مفصلة عن المخزون</small>
                                </div>
                            </a>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Low Stock Items Section -->
<div id="low-stock-section" class="row ps-4 mb-4 fade-in" style="animation-delay: 0.3s">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header-custom d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <i class="fas fa-exclamation-triangle card-header-icon text-warning"></i>
                    <h5 class="mb-0">تنبيهات المخزون</h5>
                </div>
                <a href="{% url 'inventory:product_list' %}?stock_status=low" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-external-link-alt ms-1"></i>
                    عرض الكل
                </a>
            </div>
            <div class="card-body p-0">
                {% if low_stock %}
                    <div class="table-container">
                        <table class="table table-hover stock-table">
                            <thead>
                                <tr>
                                    <th>رقم الصنف</th>
                                    <th>اسم الصنف</th>
                                    <th>الكمية الحالية</th>
                                    <th>الحد الأدنى</th>
                                    <th>الموقع</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in low_stock|slice:":5" %}
                                <tr>
                                    <td>{{ product.product_id }}</td>
                                    <td>{{ product.name }}</td>
                                    <td>
                                        <span class="low-quantity">{{ product.quantity }}</span>
                                    </td>
                                    <td>{{ product.minimum_threshold }}</td>
                                    <td>{{ product.location|default:"-" }}</td>
                                    <td>
                                        <a href="{% url 'inventory:product_edit' product.product_id %}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'inventory:invoice_add' %}?product={{ product.product_id }}" class="btn btn-sm btn-outline-success" title="إضافة فاتورة توريد">
                                            <i class="fas fa-plus"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="empty-state">
                        <div class="icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h5>لا توجد تنبيهات</h5>
                        <p class="text-muted">جميع الأصناف ضمن الحدود المسموح بها في المخزون.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities Section -->
<div class="row ps-4 mb-4 fade-in" style="animation-delay: 0.4s">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header-custom d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <i class="fas fa-history card-header-icon text-info"></i>
                    <h5 class="mb-0">آخر العمليات</h5>
                </div>
                <a href="{% url 'inventory:invoice_list' %}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-external-link-alt ms-1"></i>
                    عرض كل العمليات
                </a>
            </div>
            <div class="card-body p-0">
                <div class="table-container">
                    <table class="table table-hover stock-table">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>التاريخ</th>
                                <th>النوع</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in invoices|slice:":5" %}
                            <tr>
                                <td>{{ invoice.invoice_number }}</td>
                                <td>{{ invoice.date }}</td>
                                <td>
                                    {% if invoice.invoice_type == 'إضافة' %}
                                    <span class="badge bg-success">إضافة</span>
                                    {% else %}
                                    <span class="badge bg-warning text-dark">صرف</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-success">مكتمل</span>
                                </td>
                                <td>
                                    <a href="{% url 'inventory:invoice_detail' invoice.invoice_number %}" class="btn btn-sm btn-outline-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'inventory:invoice_edit' invoice.invoice_number %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="text-center py-4">
                                    <div class="empty-state">
                                        <p>لا توجد فواتير حديثة.</p>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
