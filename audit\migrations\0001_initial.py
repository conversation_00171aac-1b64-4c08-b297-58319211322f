# Generated by Django 5.0.14 on 2025-05-07 05:28

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AuditLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('CREATE', 'إنشاء'), ('UPDATE', 'تحديث'), ('DELETE', 'حذف'), ('VIEW', 'عرض'), ('LOGIN', 'تسجيل دخول'), ('LOGOUT', 'تسجيل خروج'), ('OTHER', 'أخرى')], max_length=50, verbose_name='الإجراء')),
                ('timestamp', models.DateTimeField(auto_now_add=True, verbose_name='وقت الإجراء')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='عنوان IP')),
                ('user_agent', models.TextField(blank=True, null=True, verbose_name='معلومات المتصفح')),
                ('app_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='اسم التطبيق')),
                ('object_id', models.CharField(blank=True, max_length=255, null=True, verbose_name='معرف الكائن')),
                ('object_repr', models.CharField(blank=True, max_length=255, null=True, verbose_name='وصف الكائن')),
                ('action_details', models.TextField(blank=True, null=True, verbose_name='تفاصيل الإجراء')),
                ('change_data', models.JSONField(blank=True, null=True, verbose_name='بيانات التغييرات')),
                ('content_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype', verbose_name='نوع المحتوى')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='audit_logs', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'سجل التدقيق',
                'verbose_name_plural': 'سجلات التدقيق',
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['timestamp'], name='audit_audit_timesta_19e18a_idx'), models.Index(fields=['user'], name='audit_audit_user_id_292c79_idx'), models.Index(fields=['action'], name='audit_audit_action_86e815_idx'), models.Index(fields=['content_type', 'object_id'], name='audit_audit_content_4c2ead_idx'), models.Index(fields=['app_name'], name='audit_audit_app_nam_f5f0bb_idx')],
            },
        ),
    ]
