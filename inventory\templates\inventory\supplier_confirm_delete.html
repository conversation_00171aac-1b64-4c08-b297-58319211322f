{% extends 'base.html' %}
{% load static %}

{% block title %}حذف المورد | نظام الدولية{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h2">حذف المورد</h1>
                <div>
                    <a href="{% url 'inventory:supplier_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-1"></i> العودة إلى قائمة الموردين
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6 mx-auto">
            <div class="card shadow-sm">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">تأكيد الحذف</h5>
                </div>
                <div class="card-body">
                    <p class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        هل أنت متأكد من حذف المورد "{{ object.name }}"؟
                    </p>
                    
                    <div class="card mb-3">
                        <div class="card-body">
                            <h5 class="card-title">{{ object.name }}</h5>
                            {% if object.contact_person %}
                            <p class="card-text"><strong>الشخص المسؤول:</strong> {{ object.contact_person }}</p>
                            {% endif %}
                            {% if object.phone %}
                            <p class="card-text"><strong>رقم الهاتف:</strong> {{ object.phone }}</p>
                            {% endif %}
                            {% if object.email %}
                            <p class="card-text"><strong>البريد الإلكتروني:</strong> {{ object.email }}</p>
                            {% endif %}
                        </div>
                    </div>
                    
                    <form method="post">
                        {% csrf_token %}
                        <div class="d-flex justify-content-end">
                            <a href="{% url 'inventory:supplier_list' %}" class="btn btn-outline-secondary me-2">
                                <i class="fas fa-times me-1"></i> إلغاء
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash-alt me-1"></i> تأكيد الحذف
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}