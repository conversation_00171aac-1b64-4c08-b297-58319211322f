# Generated by Django 5.0.14 on 2025-05-19 20:35

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('employee_tasks', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='employeetask',
            options={'ordering': ['-created_at', 'priority'], 'permissions': [('view_dashboard', 'Can view employee tasks dashboard'), ('view_mytask', 'Can view my employee tasks'), ('view_calendar', 'Can view employee tasks calendar'), ('view_analytics', 'Can view employee tasks analytics'), ('view_notification', 'Can view employee tasks notifications'), ('export_report', 'Can export employee tasks reports')], 'verbose_name': 'مهمة الموظف', 'verbose_name_plural': 'مهام الموظفين'},
        ),
    ]
