{% extends 'cars\base.html' %}

{% block title %}{{ title }} - نظام إدارة نشاط النقل{% endblock %}

{% block header %}{{ title }}{% endblock %}

{% block content %}
    <div class="card">
        <div class="card-body">
            <form method="post" novalidate>
                {% csrf_token %}
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.name.id_for_label }}" class="form-label">اسم الموظف</label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.name.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.job_title.id_for_label }}" class="form-label">المسمى الوظيفي</label>
                            {{ form.job_title }}
                            {% if form.job_title.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.job_title.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.phone.id_for_label }}" class="form-label">رقم الهاتف</label>
                            {{ form.phone }}
                            {% if form.phone.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.phone.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="{{ form.email.id_for_label }}" class="form-label">البريد الإلكتروني</label>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.email.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="{{ form.address.id_for_label }}" class="form-label">العنوان</label>
                            {{ form.address }}
                            {% if form.address.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.address.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="d-flex justify-content-between">
                    <a href="{% url 'cars:employee_list' %}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> العودة
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save"></i> حفظ
                    </button>
                </div>
            </form>
        </div>
    </div>
{% endblock %}