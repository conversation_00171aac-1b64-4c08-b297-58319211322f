{% extends 'Hr/base_hr.html' %}
{% load static %}

{% block title %}حذف المهمة - {{ task.title }} - نظام الدولية{% endblock %}

{% block page_title %}حذف المهمة: {{ task.title }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموارد البشرية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:tasks:list' %}">مهام الموظفين</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:tasks:detail' task.pk %}">{{ task.title|truncatechars:30 }}</a></li>
<li class="breadcrumb-item active">حذف</li>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow-sm">
            <div class="card-header bg-danger text-white py-3">
                <h5 class="mb-0 fw-semibold">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تأكيد حذف المهمة
                </h5>
            </div>
            <div class="card-body p-4">
                <div class="alert alert-warning mb-4">
                    <h5><i class="fas fa-exclamation-triangle me-2"></i> تحذير!</h5>
                    <p>أنت على وشك حذف المهمة التالية:</p>
                    <ul>
                        <li><strong>عنوان المهمة:</strong> {{ task.title }}</li>
                        <li><strong>الموظف:</strong> {{ task.employee.emp_name }}</li>
                        <li><strong>الحالة:</strong> 
                            {% if task.status == 'pending' %}
                            قيد الانتظار
                            {% elif task.status == 'in_progress' %}
                            قيد التنفيذ
                            {% elif task.status == 'completed' %}
                            مكتملة
                            {% elif task.status == 'cancelled' %}
                            ملغاة
                            {% endif %}
                        </li>
                        <li><strong>تاريخ الاستحقاق:</strong> {{ task.due_date|date:"Y-m-d" }}</li>
                    </ul>
                    <p class="mb-0">هذا الإجراء لا يمكن التراجع عنه.</p>
                </div>

                {% if task.status == 'completed' %}
                <div class="alert alert-info mb-4">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>ملاحظة:</strong> هذه المهمة مكتملة بالفعل. قد ترغب في الاحتفاظ بها كسجل للأنشطة المنجزة.
                </div>

                <div class="d-flex justify-content-between align-items-center">
                    <a href="{% url 'Hr:tasks:detail' task.pk %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-1"></i> العودة للتفاصيل
                    </a>
                    <a href="{% url 'Hr:tasks:edit' task.pk %}" class="btn btn-primary">
                        <i class="fas fa-edit me-1"></i> تعديل المهمة
                    </a>
                </div>
                {% else %}
                <form method="post">
                    {% csrf_token %}
                    <div class="d-flex justify-content-between align-items-center">
                        <a href="{% url 'Hr:tasks:detail' task.pk %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash-alt me-1"></i> تأكيد الحذف
                        </button>
                    </div>
                </form>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
