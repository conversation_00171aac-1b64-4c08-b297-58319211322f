{% extends 'Purchase_orders/base_purchase_orders.html' %}
{% load static %}

{% block title %}طلبات الشراء قيد الموافقة - نظام الدولية{% endblock %}

{% block page_title %}طلبات الشراء قيد الموافقة{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Purchase_orders:dashboard' %}">طلبات الشراء</a></li>
<li class="breadcrumb-item active">طلبات قيد الموافقة</li>
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">قائمة طلبات الشراء قيد الموافقة</h5>
        <a href="{% url 'Purchase_orders:create_purchase_request' %}" class="btn btn-primary">
            <i class="fas fa-plus-circle me-2"></i> إنشاء طلب شراء جديد
        </a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>رقم الطلب</th>
                        <th>التاريخ</th>
                        <th>المورد</th>
                        <th>مقدم الطلب</th>
                        <th>الإجمالي</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for request in purchase_requests %}
                    <tr>
                        <td>
                            <a href="{% url 'Purchase_orders:purchase_request_detail' request.id %}">{{ request.request_number }}</a>
                        </td>
                        <td>{{ request.request_date|date:"Y-m-d" }}</td>
                        <td>{{ request.vendor|default:"غير محدد" }}</td>
                        <td>{{ request.requested_by.get_full_name|default:request.requested_by.username }}</td>
                        <td>{{ request.total_amount }} ريال</td>
                        <td>
                            <a href="{% url 'Purchase_orders:purchase_request_detail' request.id %}" class="btn btn-sm btn-info">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="{% url 'Purchase_orders:approve_purchase_request' request.id %}" class="btn btn-sm btn-success">
                                <i class="fas fa-check"></i>
                            </a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="text-center py-4">لا توجد طلبات شراء قيد الموافقة</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
