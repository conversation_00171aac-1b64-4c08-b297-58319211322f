{% extends 'Purchase_orders/base_purchase_orders.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}إضافة عنصر لطلب الشراء - نظام الدولية{% endblock %}

{% block page_title %}إضافة عنصر لطلب الشراء #{{ purchase_request.request_number }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Purchase_orders:dashboard' %}">طلبات الشراء</a></li>
<li class="breadcrumb-item"><a href="{% url 'Purchase_orders:purchase_request_list' %}">قائمة طلبات الشراء</a></li>
<li class="breadcrumb-item"><a href="{% url 'Purchase_orders:purchase_request_detail' pk=purchase_request.pk %}">تفاصيل الطلب #{{ purchase_request.request_number }}</a></li>
<li class="breadcrumb-item active">إضافة عنصر</li>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-end mb-4">
    <a href="{% url 'Purchase_orders:purchase_request_detail' pk=purchase_request.pk %}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-1"></i> العودة للطلب
    </a>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">بيانات العنصر</h5>
    </div>
    <div class="card-body">
        <form method="post">
            {% csrf_token %}

            <div class="mb-3">
                <label for="{{ form.product.id_for_label }}" class="form-label">الصنف</label>
                {{ form.product|add_class:"form-control" }}
                {% if form.product.errors %}
                <div class="text-danger">
                    {% for error in form.product.errors %}
                    {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="{{ form.quantity_requested.id_for_label }}" class="form-label">الكمية المطلوبة</label>
                {{ form.quantity_requested|add_class:"form-control" }}
                {% if form.quantity_requested.errors %}
                <div class="text-danger">
                    {% for error in form.quantity_requested.errors %}
                    {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="{{ form.notes.id_for_label }}" class="form-label">ملاحظات</label>
                {{ form.notes|add_class:"form-control" }}
                {% if form.notes.errors %}
                <div class="text-danger">
                    {% for error in form.notes.errors %}
                    {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>

            <div class="d-flex justify-content-between">
                <button type="reset" class="btn btn-secondary">
                    <i class="fas fa-undo me-1"></i> إعادة تعيين
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i> إضافة العنصر
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
