{% extends 'inventory/base_inventory.html' %}
{% load static %}
{% load inventory_permission_tags %}

{% block title %}تأكيد حذف الإذن - نظام إدارة المخزن{% endblock %}

{% block extra_css %}
<style>
    .delete-card {
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        border-top: 3px solid #dc3545;
    }

    .delete-icon {
        font-size: 3rem;
        color: #dc3545;
        margin-bottom: 1rem;
    }

    .voucher-info {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .voucher-info p {
        margin-bottom: 5px;
    }

    .voucher-info strong {
        color: #495057;
    }

    .btn-action {
        min-width: 120px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row ps-4">
    <div class="col-md-8 mx-auto mb-4">
        <div class="card delete-card">
            <div class="card-header bg-white text-center py-4">
                <i class="fas fa-exclamation-triangle delete-icon"></i>
                <h4 class="mb-0">تأكيد حذف الإذن</h4>
            </div>
            <div class="card-body p-4">
                <div class="alert alert-warning mb-4">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <strong>تحذير:</strong> سيؤدي حذف هذا الإذن إلى إزالته نهائياً من قاعدة البيانات وعكس تأثيره على كميات المخزون. هذا الإجراء لا يمكن التراجع عنه.
                </div>

                <div class="alert alert-info mb-4">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>تأثير الحذف على المخزون:</strong>
                    {% if object.voucher_type == 'إذن اضافة' %}
                        <p>سيتم خصم الكميات المضافة من الرصيد الحالي للأصناف المرتبطة بهذا الإذن.</p>
                    {% elif object.voucher_type == 'إذن صرف' %}
                        <p>سيتم إضافة الكميات المنصرفة إلى الرصيد الحالي للأصناف المرتبطة بهذا الإذن.</p>
                    {% elif object.voucher_type == 'اذن مرتجع عميل' %}
                        <p>سيتم خصم الكميات المضافة من الرصيد الحالي للأصناف المرتبطة بهذا الإذن.</p>
                    {% elif object.voucher_type == 'إذن مرتجع مورد' %}
                        <p>سيتم إضافة الكميات المنصرفة إلى الرصيد الحالي للأصناف المرتبطة بهذا الإذن.</p>
                    {% endif %}
                </div>

                <div class="voucher-info">
                    <h5 class="mb-3">معلومات الإذن:</h5>
                    <p><strong>رقم الإذن:</strong> {{ object.voucher_number }}</p>
                    <p><strong>نوع الإذن:</strong> {{ object.voucher_type }}</p>
                    <p><strong>التاريخ:</strong> {{ object.date }}</p>

                    {% if object.voucher_type == 'إذن اضافة' or object.voucher_type == 'إذن مرتجع مورد' %}
                        {% if object.supplier %}
                        <p><strong>المورد:</strong> {{ object.supplier.name }}</p>
                        {% endif %}
                    {% elif object.voucher_type == 'إذن صرف' %}
                        {% if object.department %}
                        <p><strong>القسم:</strong> {{ object.department.name }}</p>
                        {% endif %}
                    {% elif object.voucher_type == 'اذن مرتجع عميل' %}
                        {% if object.customer %}
                        <p><strong>العميل:</strong> {{ object.customer.name }}</p>
                        {% endif %}
                    {% endif %}

                    {% if object.recipient %}
                    <p><strong>المستلم:</strong> {{ object.recipient }}</p>
                    {% endif %}

                    <p><strong>عدد الأصناف:</strong> {{ object.items_count }}</p>

                    {% if object.notes %}
                    <p><strong>ملاحظات:</strong> {{ object.notes }}</p>
                    {% endif %}
                </div>

                {% if object.items.all %}
                <div class="mt-4">
                    <h5 class="mb-3">الأصناف المرتبطة بالإذن:</h5>
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead class="table-light">
                                <tr>
                                    <th>كود الصنف</th>
                                    <th>اسم الصنف</th>
                                    {% if object.voucher_type in 'إذن اضافة,اذن مرتجع عميل' %}
                                        <th>الكمية المضافة</th>
                                    {% else %}
                                        <th>الكمية المنصرفة</th>
                                    {% endif %}
                                    <th>الرصيد الحالي</th>
                                    <th>التأثير بعد الحذف</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in object.items.all %}
                                <tr>
                                    <td>{{ item.product.product_id }}</td>
                                    <td>{{ item.product.name }}</td>
                                    {% if object.voucher_type in 'إذن اضافة,اذن مرتجع عميل' %}
                                        <td>{{ item.quantity_added }}</td>
                                    {% else %}
                                        <td>{{ item.quantity_disbursed }}</td>
                                    {% endif %}
                                    <td>{{ item.product.quantity }}</td>
                                    <td>
                                        {% if object.voucher_type in 'إذن اضافة,اذن مرتجع عميل' %}
                                            <span class="text-danger">-{{ item.quantity_added }}</span>
                                        {% else %}
                                            <span class="text-success">+{{ item.quantity_disbursed }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                {% endif %}

                <form method="post">
                    {% csrf_token %}
                    <div class="d-flex justify-content-center gap-3 mt-4">
                        <a href="{% url 'inventory:voucher_list' %}" class="btn btn-secondary btn-action">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-danger btn-action">
                            <i class="fas fa-trash-alt me-1"></i>
                            تأكيد الحذف
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
