{% extends 'administrator/base_admin.html' %}
{% load static %}

{% block title %}إدارة مجموعات المستخدم: {{ user_obj.username }} - نظام الدولية{% endblock %}

{% block page_title %}إدارة مجموعات المستخدم: {{ user_obj.username }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'administrator:admin_dashboard' %}">لوحة تحكم المدير</a></li>
<li class="breadcrumb-item"><a href="{% url 'administrator:permission_dashboard' %}">إدارة الصلاحيات</a></li>
<li class="breadcrumb-item active">إدارة مجموعات المستخدم: {{ user_obj.username }}</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card shadow-sm">
            <div class="card-header bg-light d-flex justify-content-between align-items-center py-3 border-bottom">
                <h5 class="mb-0 fw-semibold">إدارة مجموعات المستخدم: {{ user_obj.get_full_name|default:user_obj.username }}</h5>
                <a href="{% url 'administrator:user_permissions' pk=user_obj.id %}" class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-arrow-right me-1"></i>
                    العودة إلى صلاحيات المستخدم
                </a>
            </div>
            <div class="card-body">
                <div class="alert alert-info mb-4">
                    <i class="fas fa-info-circle me-2"></i>
                    حدد المجموعات التي ينتمي إليها المستخدم <strong>{{ user_obj.username }}</strong>. سيتمكن المستخدم من الوصول إلى الأقسام المسموح بها لهذه المجموعات.
                </div>
                
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead class="table-light">
                                <tr>
                                    <th class="py-3 px-3" style="width: 50px;">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="select-all">
                                        </div>
                                    </th>
                                    <th class="py-3 px-3">المجموعة</th>
                                    <th class="py-3 px-3">عدد المستخدمين</th>
                                    <th class="py-3 px-3">الأقسام المسموح بها</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for group in groups %}
                                <tr>
                                    <td class="px-3">
                                        <div class="form-check">
                                            <input class="form-check-input group-checkbox" type="checkbox" 
                                                   name="groups" value="{{ group.id }}"
                                                   {% if group.id in selected_groups %}checked{% endif %}>
                                        </div>
                                    </td>
                                    <td class="px-3">
                                        <h6 class="mb-0">{{ group.name }}</h6>
                                    </td>
                                    <td class="px-3">{{ group.user_set.count }}</td>
                                    <td class="px-3">
                                        {% if group.allowed_departments.exists %}
                                        <div class="d-flex flex-wrap gap-1">
                                            {% for dept in group.allowed_departments.all %}
                                            <span class="badge bg-primary">{{ dept.name }}</span>
                                            {% endfor %}
                                        </div>
                                        {% else %}
                                        <span class="badge bg-secondary">لا يوجد</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center py-4">
                                        <div class="mb-3">
                                            <i class="fas fa-users-slash fa-3x text-muted"></i>
                                        </div>
                                        <p class="text-muted">لا توجد مجموعات حالياً</p>
                                        <a href="{% url 'administrator:group_add' %}" class="btn btn-primary btn-sm">
                                            <i class="fas fa-plus-circle me-1"></i>
                                            إضافة مجموعة
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="d-flex justify-content-end mt-4">
                        <a href="{% url 'administrator:user_permissions' pk=user_obj.id %}" class="btn btn-outline-secondary me-2">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle "Select All" checkbox
        const selectAllCheckbox = document.getElementById('select-all');
        const groupCheckboxes = document.querySelectorAll('.group-checkbox');
        
        selectAllCheckbox.addEventListener('change', function() {
            groupCheckboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
            });
        });
        
        // Update "Select All" checkbox state based on individual checkboxes
        function updateSelectAllCheckbox() {
            const checkedCount = document.querySelectorAll('.group-checkbox:checked').length;
            selectAllCheckbox.checked = checkedCount === groupCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < groupCheckboxes.length;
        }
        
        groupCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateSelectAllCheckbox);
        });
        
        // Initialize "Select All" checkbox state
        updateSelectAllCheckbox();
    });
</script>
{% endblock %}
