{% load static %}
{% load i18n %}
<!DOCTYPE html>
<html lang="{{ current_language }}" dir="{{ text_direction }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة المهام{% endblock %}</title>

    <!-- Bootstrap RTL/LTR -->
    {% if text_direction == 'rtl' %}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    {% else %}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    {% endif %}

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/style_updated.css' %}">

    <style>
        :root {
            --font-family: {{ current_font|default:'Cairo' }}, sans-serif;
            --primary-color: #9c27b0;
            --secondary-color: #7b1fa2;
            --success-color: #4caf50;
            --info-color: #00bcd4;
            --warning-color: #ff9800;
            --danger-color: #f44336;
            --light-color: #f5f5f5;
            --dark-color: #212121;
            --body-bg: #f5f7fa;
            --card-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        html, body {
            height: 100%;
        }

        body {
            font-family: var(--font-family);
            background-color: var(--body-bg);
            overflow-x: hidden;
        }

        /* Layout */
        .page-wrapper {
            display: flex;
            flex-direction: column;
            min-height: 100%;
        }

        .content-wrapper {
            flex: 1;
            display: flex;
        }

        /* Sidebar */
        .sidebar {
            width: 280px;
            background: linear-gradient(to left, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            height: 100vh;
            position: fixed;
            right: 0;
            top: 0;
            z-index: 1000;
            transition: all 0.3s;
            box-shadow: -3px 0 10px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
            padding-top: 60px; /* Space for navbar */
        }

        .sidebar.collapsed {
            right: -280px;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.15);
        }

        .sidebar-header h3 {
            color: #fff;
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 0;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 1rem 0;
        }

        .sidebar-menu-item {
            padding: 0.8rem 1rem;
            cursor: pointer;
            border-radius: 0.375rem;
            display: flex;
            align-items: center;
            transition: all 0.3s;
            margin: 0.3rem 0.5rem;
        }

        .sidebar-menu-item:hover,
        .sidebar-menu-item.active {
            background-color: rgba(255,255,255,0.2);
            transform: translateX(-5px);
        }

        .sidebar-menu-item i {
            margin-left: 10px;
            width: 22px;
            text-align: center;
        }

        .sidebar-menu-item a {
            color: white;
            text-decoration: none;
            display: block;
            width: 100%;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            margin-right: 280px;
            transition: all 0.3s;
            padding: 70px 20px 20px 20px;
        }

        .main-content.expanded {
            margin-right: 0;
        }

        /* Navbar */
        .navbar {
            background-color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1020;
            padding: 0.5rem 1rem;
            height: 60px;
            transition: all 0.3s;
        }

        .navbar.with-sidebar {
            padding-right: 295px;
        }

        .navbar.sidebar-collapsed {
            padding-right: 15px;
        }

        /* Toggle Button */
        #sidebarToggle {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            transition: all 0.3s;
        }

        #sidebarToggle:hover {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
            transform: scale(1.05);
        }

        /* Responsive */
        @media (max-width: 992px) {
            .sidebar {
                right: -280px;
            }

            .sidebar.collapsed {
                right: 0;
            }

            .main-content {
                margin-right: 0;
            }

            .navbar {
                padding-right: 15px !important;
            }
        }

        /* Cards */
        .card {
            border-radius: 0.5rem;
            box-shadow: var(--card-shadow);
            border: none;
            margin-bottom: 1.5rem;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            background-color: white;
            padding: 1rem 1.5rem;
            font-weight: 600;
        }

        /* Alerts */
        .alerts-container {
            margin-bottom: 1.5rem;
        }

        .alert {
            border-radius: 0.5rem;
            border: none;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="page-wrapper">
        <!-- Navbar -->
        <nav class="navbar navbar-expand-lg navbar-light with-sidebar">
            <div class="container-fluid">
                <div class="d-flex align-items-center">
                    <button id="sidebarToggle" class="btn btn-primary me-2">
                        <i class="fas fa-bars"></i>
                    </button>
                    <a class="navbar-brand" href="{% url 'accounts:home' %}">
                        <i class="fas fa-tasks me-2"></i>
                        <span>نظام إدارة المهام</span>
                    </a>
                </div>
                <div class="d-flex align-items-center">
                    <div class="user-info me-3 d-flex align-items-center">
                        <i class="fas fa-user-circle text-primary me-2 fs-5"></i>
                        <span class="fw-bold">{{ request.user.username }}</span>
                    </div>
                    <a href="{% url 'accounts:logout' %}" class="btn btn-outline-danger btn-sm">
                        <i class="fas fa-sign-out-alt me-1"></i>
                        <span class="d-none d-md-inline-block">تسجيل الخروج</span>
                    </a>
                </div>
            </div>
        </nav>

        <div class="content-wrapper">
            <!-- Sidebar -->
            <aside class="sidebar">
                <div class="sidebar-header">
                    <h3><i class="fas fa-tasks me-2"></i> المهام</h3>
                </div>
                <ul class="sidebar-menu">
                    <li class="sidebar-menu-item">
                        <a href="{% url 'tasks:dashboard' %}">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>لوحة التحكم</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="{% url 'tasks:list' %}">
                            <i class="fas fa-list"></i>
                            <span>قائمة المهام</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="{% url 'tasks:create' %}">
                            <i class="fas fa-plus"></i>
                            <span>إنشاء مهمة جديدة</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="{% url 'tasks:my_tasks' %}">
                            <i class="fas fa-user-check"></i>
                            <span>مهامي</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="{% url 'tasks:completed' %}">
                            <i class="fas fa-check-circle"></i>
                            <span>المهام المكتملة</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="{% url 'tasks:reports' %}">
                            <i class="fas fa-chart-bar"></i>
                            <span>التقارير</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="{% url 'accounts:home' %}">
                            <i class="fas fa-home"></i>
                            <span>الصفحة الرئيسية</span>
                        </a>
                    </li>
                </ul>
            </aside>

            <!-- Main Content -->
            <div class="main-content">
                <!-- Page Header -->
                <div class="page-header mb-4">
                    <h2 class="mb-0">{% block page_title %}المهام{% endblock %}</h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            {% block breadcrumb %}
                            <li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
                            <li class="breadcrumb-item active">المهام</li>
                            {% endblock %}
                        </ol>
                    </nav>
                </div>

                <!-- Alerts -->
                {% if messages %}
                <div class="alerts-container">
                    {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}

                <!-- Main Content -->
                {% block content %}{% endblock %}
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const sidebar = document.querySelector('.sidebar');
            const mainContent = document.querySelector('.main-content');
            const navbar = document.querySelector('.navbar');
            const sidebarToggle = document.getElementById('sidebarToggle');

            // Toggle sidebar function
            function toggleSidebar() {
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');
                navbar.classList.toggle('with-sidebar');
                navbar.classList.toggle('sidebar-collapsed');

                // Save state to localStorage
                const sidebarState = sidebar.classList.contains('collapsed') ? 'collapsed' : 'expanded';
                localStorage.setItem('tasksSidebarState', sidebarState);
            }

            // Initialize sidebar state from localStorage
            function initSidebarState() {
                const savedState = localStorage.getItem('tasksSidebarState');

                // Default to expanded on desktop, collapsed on mobile
                const defaultState = window.innerWidth < 992 ? 'collapsed' : 'expanded';
                const sidebarState = savedState || defaultState;

                if (sidebarState === 'collapsed') {
                    sidebar.classList.add('collapsed');
                    mainContent.classList.add('expanded');
                    navbar.classList.remove('with-sidebar');
                    navbar.classList.add('sidebar-collapsed');
                } else {
                    sidebar.classList.remove('collapsed');
                    mainContent.classList.remove('expanded');
                    navbar.classList.add('with-sidebar');
                    navbar.classList.remove('sidebar-collapsed');
                }
            }

            // Toggle sidebar on button click
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', toggleSidebar);
            }

            // Initialize sidebar state
            initSidebarState();

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth < 992) {
                    // On mobile, collapse sidebar by default
                    if (!sidebar.classList.contains('collapsed')) {
                        sidebar.classList.add('collapsed');
                        mainContent.classList.add('expanded');
                        navbar.classList.remove('with-sidebar');
                        navbar.classList.add('sidebar-collapsed');
                    }
                }
            });

            // Highlight active menu item
            const currentPath = window.location.pathname;
            const menuItems = document.querySelectorAll('.sidebar-menu-item');

            menuItems.forEach(item => {
                const link = item.querySelector('a');
                if (link && currentPath.includes(link.getAttribute('href'))) {
                    item.classList.add('active');
                }
            });
        });
    </script>
    {% block extra_js %}{% endblock %}
</body>
</html>
