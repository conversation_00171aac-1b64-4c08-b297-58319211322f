{% extends 'Hr/base_hr.html' %}
{% load static %}

{% block title %}{{ title }} - نظام الدولية{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'Hr/css/leaves.css' %}">
{% endblock %}

{% block page_title %}{{ title }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:employees:list' %}">الموظفين</a></li>
<li class="breadcrumb-item active">{{ title }}</li>
{% endblock %}

{% block content %}
<div class="card shadow-sm">
    <div class="card-header bg-white d-flex justify-content-between align-items-center py-3">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            {{ title }}
        </h5>
        <a href="{% url 'Hr:leave_types:create' %}" class="btn btn-primary">
            <i class="fas fa-plus-circle me-1"></i>
            إضافة نوع إجازة جديد
        </a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>كود الإجازة</th>
                        <th>نوع الإجازة</th>
                        <th>الوصف</th>
                        <th>الحد الأقصى للأيام</th>
                        <th>مدفوعة الأجر</th>
                        <th>تتطلب موافقة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for leave_type in leave_types %}
                    <tr>
                        <td>{{ leave_type.code }}</td>
                        <td>{{ leave_type.name }}</td>
                        <td>{{ leave_type.description|default:"-" }}</td>
                        <td>{{ leave_type.max_days_per_year }}</td>
                        <td>
                            {% if leave_type.paid %}
                            <span class="badge bg-success">نعم</span>
                            {% else %}
                            <span class="badge bg-danger">لا</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if leave_type.requires_approval %}
                            <span class="badge bg-info">نعم</span>
                            {% else %}
                            <span class="badge bg-warning">لا</span>
                            {% endif %}
                        </td>
                        <td>
                            <a href="{% url 'Hr:leave_types:edit' leave_type.pk %}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-edit"></i>
                            </a>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="text-center">لا توجد أنواع إجازات مسجلة</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
