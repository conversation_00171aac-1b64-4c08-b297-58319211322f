{% extends 'Hr/base_hr.html' %}
{% load static %}

{% block title %}{{ title }} - نظام الدولية{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'Hr/css/leaves.css' %}">
{% endblock %}

{% block page_title %}{{ title }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:employees:list' %}">الموظفين</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:leaves:list' %}">إجازات الموظفين</a></li>
<li class="breadcrumb-item active">{{ title }}</li>
{% endblock %}

{% block content %}
<div class="card shadow-sm">
    <div class="card-header bg-white d-flex justify-content-between align-items-center py-3">
        <h5 class="mb-0">
            <i class="fas fa-eye me-2"></i>
            {{ title }}
        </h5>
        <div>
            {% if leave.status == 'pending' %}
            <a href="{% url 'Hr:leaves:approve' leave.pk %}" class="btn btn-success">
                <i class="fas fa-check-circle me-1"></i>
                موافقة/رفض
            </a>
            <a href="{% url 'Hr:leaves:edit' leave.pk %}" class="btn btn-primary">
                <i class="fas fa-edit me-1"></i>
                تعديل
            </a>
            {% endif %}
            <a href="{% url 'Hr:leaves:list' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6 class="mb-3">معلومات الموظف</h6>
                <table class="table table-bordered">
                    <tr>
                        <th style="width: 40%">اسم الموظف</th>
                        <td>{{ leave.employee.emp_full_name }}</td>
                    </tr>
                    <tr>
                        <th>القسم</th>
                        <td>{{ leave.employee.department.dept_name }}</td>
                    </tr>
                    <tr>
                        <th>الوظيفة</th>
                        <td>{{ leave.employee.jop_name }}</td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6 class="mb-3">تفاصيل الإجازة</h6>
                <table class="table table-bordered">
                    <tr>
                        <th style="width: 40%">نوع الإجازة</th>
                        <td>{{ leave.leave_type.name }}</td>
                    </tr>
                    <tr>
                        <th>من تاريخ</th>
                        <td>{{ leave.start_date }}</td>
                    </tr>
                    <tr>
                        <th>إلى تاريخ</th>
                        <td>{{ leave.end_date }}</td>
                    </tr>
                    <tr>
                        <th>عدد الأيام</th>
                        <td>{{ leave.total_days }}</td>
                    </tr>
                    <tr>
                        <th>الحالة</th>
                        <td>
                            {% if leave.status == 'pending' %}
                            <span class="badge bg-warning">قيد الانتظار</span>
                            {% elif leave.status == 'approved' %}
                            <span class="badge bg-success">تمت الموافقة</span>
                            {% elif leave.status == 'rejected' %}
                            <span class="badge bg-danger">مرفوضة</span>
                            {% elif leave.status == 'cancelled' %}
                            <span class="badge bg-secondary">ملغاة</span>
                            {% endif %}
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <h6 class="mb-3">سبب الإجازة</h6>
                <div class="p-3 bg-light rounded">
                    {{ leave.reason|linebreaks }}
                </div>
            </div>
        </div>

        {% if leave.documents %}
        <div class="row mt-4">
            <div class="col-12">
                <h6 class="mb-3">المستندات المرفقة</h6>
                <div class="p-3 bg-light rounded">
                    <a href="{{ leave.documents.url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-file me-1"></i>
                        عرض المستند
                    </a>
                </div>
            </div>
        </div>
        {% endif %}

        {% if leave.status != 'pending' %}
        <div class="row mt-4">
            <div class="col-12">
                <h6 class="mb-3">معلومات الموافقة/الرفض</h6>
                <table class="table table-bordered">
                    <tr>
                        <th style="width: 25%">تمت المراجعة بواسطة</th>
                        <td>{{ leave.approved_by.get_full_name }}</td>
                    </tr>
                    <tr>
                        <th>تاريخ المراجعة</th>
                        <td>{{ leave.approval_date }}</td>
                    </tr>
                    {% if leave.status == 'rejected' and leave.rejection_reason %}
                    <tr>
                        <th>سبب الرفض</th>
                        <td>{{ leave.rejection_reason }}</td>
                    </tr>
                    {% endif %}
                </table>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
