{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load django_permissions %}

{% block title %}تقرير الإجازات{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="page-title mb-4">تقرير الإجازات</h2>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <form method="get" class="mb-4">
        <div class="row">
            <div class="col-md-3">
                <div class="mb-3">
                    <label for="employee" class="form-label">الموظف</label>
                    <select name="employee" id="employee" class="form-select">
                        <option value="">-- جميع الموظفين --</option>
                        {% for emp in employees %}
                        <option value="{{ emp.id }}" {% if selected_employee == emp.id|stringformat:"s" %}selected{% endif %}>
                            {{ emp.emp_full_name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="mb-3">
                    <label for="leave_type" class="form-label">نوع الإجازة</label>
                    <select name="leave_type" id="leave_type" class="form-select">
                        <option value="">-- جميع الأنواع --</option>
                        {% for type in leave_types %}
                        <option value="{{ type.id }}" {% if selected_leave_type == type.id|stringformat:"s" %}selected{% endif %}>
                            {{ type.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="mb-3">
                    <label for="status" class="form-label">الحالة</label>
                    <select name="status" id="status" class="form-select">
                        <option value="">-- جميع الحالات --</option>
                        {% for status_code, status_name in statuses %}
                        <option value="{{ status_code }}" {% if selected_status == status_code %}selected{% endif %}>
                            {{ status_name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="mb-3">
                    <label for="date_from" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ selected_date_from|date:'Y-m-d' }}">
                </div>
            </div>
            <div class="col-md-3">
                <div class="mb-3">
                    <label for="date_to" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ selected_date_to|date:'Y-m-d' }}">
                </div>
            </div>
            <div class="col-12 text-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-1"></i>
                    بحث
                </button>
                <a href="{% url 'Hr:reports:leave_report' %}" class="btn btn-secondary">
                    <i class="fas fa-redo me-1"></i>
                    إعادة تعيين
                </a>
                {% if perms.Hr.export_leave_data or user|is_admin %}
                <div class="btn-group ms-2">
                    <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-download me-1"></i>
                        تصدير
                    </button>
                    <ul class="dropdown-menu">
                        <li>
                            <a class="dropdown-item" href="?{{ request.GET.urlencode }}&export=excel">
                                <i class="fas fa-file-excel me-1 text-success"></i>
                                Excel
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="?{{ request.GET.urlencode }}&export=csv">
                                <i class="fas fa-file-csv me-1 text-info"></i>
                                CSV
                            </a>
                        </li>
                    </ul>
                </div>
                {% endif %}
            </div>
        </div>
    </form>

    <!-- جدول البيانات -->
    {% if leaves %}
    <div class="table-responsive">
        <table class="table table-hover">
            <thead>
                <tr>
                    <th>الموظف</th>
                    <th>نوع الإجازة</th>
                    <th>تاريخ البداية</th>
                    <th>تاريخ النهاية</th>
                    <th>عدد الأيام</th>
                    <th>الحالة</th>
                    {% if perms.Hr.view_employeeleave_details or user|is_admin %}
                    <th class="text-center">الإجراءات</th>
                    {% endif %}
                </tr>
            </thead>
            <tbody>
                {% for leave in leaves %}
                <tr>
                    <td>{{ leave.employee.emp_full_name }}</td>
                    <td>{{ leave.leave_type.name }}</td>
                    <td>{{ leave.start_date|date:"Y-m-d" }}</td>
                    <td>{{ leave.end_date|date:"Y-m-d" }}</td>
                    <td>{{ leave.days_count }}</td>
                    <td>{{ leave.get_status_display }}</td>
                    {% if perms.Hr.view_employeeleave_details or user|is_admin %}
                    <td class="text-center">
                        <a href="{% url 'Hr:leaves:detail' leave.id %}" class="btn btn-sm btn-primary">
                            <i class="fas fa-eye"></i>
                        </a>
                        {% if perms.Hr.print_employeeleave or user|is_admin %}
                        <a href="{% url 'Hr:leaves:print' leave.id %}" class="btn btn-sm btn-info">
                            <i class="fas fa-print"></i>
                        </a>
                        {% endif %}
                    </td>
                    {% endif %}
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <div class="text-center py-5">
        <p>لا توجد بيانات للعرض</p>
    </div>
    {% endif %}
</div>
{% endblock %}