{% extends 'administrator/base_admin.html' %}
{% load static %}

{% block title %}حذف المستخدم - {{ user_obj.username }} - نظام الدولية{% endblock %}

{% block page_icon %}trash-alt{% endblock %}
{% block page_header %}حذف المستخدم - {{ user_obj.username }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'administrator:admin_dashboard' %}">مدير النظام</a></li>
<li class="breadcrumb-item"><a href="{% url 'administrator:user_list' %}">المستخدمين</a></li>
<li class="breadcrumb-item active">حذف {{ user_obj.username }}</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card border-0 shadow">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تأكيد حذف المستخدم
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning mb-4">
                    <div class="d-flex">
                        <div class="me-3">
                            <i class="fas fa-exclamation-triangle fa-2x text-warning"></i>
                        </div>
                        <div>
                            <h5 class="alert-heading">تحذير!</h5>
                            <p>أنت على وشك حذف المستخدم "{{ user_obj.username }}". هذا الإجراء لا يمكن التراجع عنه.</p>
                            <p class="mb-0">سيتم حذف جميع بيانات هذا المستخدم والصلاحيات المرتبطة به.</p>
                        </div>
                    </div>
                </div>
                
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title">معلومات المستخدم</h5>
                        <div class="list-group list-group-flush">
                            <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                <span><i class="fas fa-user me-2"></i> اسم المستخدم:</span>
                                <span class="fw-bold">{{ user_obj.username }}</span>
                            </div>
                            {% if user_obj.get_full_name %}
                            <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                <span><i class="fas fa-id-card me-2"></i> الاسم الكامل:</span>
                                <span>{{ user_obj.get_full_name }}</span>
                            </div>
                            {% endif %}
                            {% if user_obj.email %}
                            <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                <span><i class="fas fa-envelope me-2"></i> البريد الإلكتروني:</span>
                                <span>{{ user_obj.email }}</span>
                            </div>
                            {% endif %}
                            <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                <span><i class="fas fa-users me-2"></i> المجموعات:</span>
                                <span class="badge bg-primary">{{ user_obj.groups.count }}</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                <span><i class="fas fa-key me-2"></i> الصلاحيات:</span>
                                <span class="badge bg-danger">{{ user_obj.user_permissions.count }}</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                <span><i class="fas fa-calendar me-2"></i> تاريخ الإنشاء:</span>
                                <span>{{ user_obj.date_joined|date:"Y-m-d" }}</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                <span><i class="fas fa-sign-in-alt me-2"></i> آخر تسجيل دخول:</span>
                                <span>{{ user_obj.last_login|date:"Y-m-d H:i"|default:"لا يوجد" }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                {% if user_obj.is_superuser %}
                <div class="alert alert-danger mb-4">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <strong>تنبيه:</strong> هذا المستخدم لديه صلاحيات مدير النظام. حذف هذا المستخدم قد يؤثر على إمكانية الوصول إلى وظائف إدارية مهمة في النظام.
                </div>
                {% endif %}
                
                {% if user_obj.groups.exists %}
                <div class="alert alert-info mb-4">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>ملاحظة:</strong> هذا المستخدم ينتمي إلى {{ user_obj.groups.count }} مجموعة. سيتم إلغاء هذه العلاقات عند الحذف.
                </div>
                {% endif %}

                <form method="post" class="mt-4">
                    {% csrf_token %}
                    
                    <div class="form-check mb-4">
                        <input class="form-check-input" type="checkbox" id="confirmDelete" required>
                        <label class="form-check-label" for="confirmDelete">
                            أؤكد أنني أريد حذف المستخدم "{{ user_obj.username }}" وأدرك أن هذا الإجراء لا يمكن التراجع عنه.
                        </label>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'administrator:user_list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-danger" id="deleteBtn" disabled>
                            <i class="fas fa-trash-alt me-1"></i> حذف المستخدم
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        $('#confirmDelete').change(function() {
            if ($(this).is(':checked')) {
                $('#deleteBtn').prop('disabled', false);
            } else {
                $('#deleteBtn').prop('disabled', true);
            }
        });
    });
</script>
{% endblock %}
