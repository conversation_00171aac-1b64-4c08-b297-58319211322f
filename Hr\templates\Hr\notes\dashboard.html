{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load django_permissions %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.css" rel="stylesheet">
<style>
    .stats-card {
        transition: all 0.3s ease;
        border: none;
        border-radius: 15px;
        overflow: hidden;
    }
    
    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }
    
    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
    }
    
    .recent-notes-card {
        max-height: 500px;
        overflow-y: auto;
    }
    
    .note-item {
        border-left: 4px solid #dee2e6;
        transition: all 0.3s ease;
    }
    
    .note-item:hover {
        background-color: #f8f9fa;
        border-left-color: #007bff;
    }
    
    .note-type-positive {
        border-left-color: #28a745 !important;
    }
    
    .note-type-negative {
        border-left-color: #dc3545 !important;
    }
    
    .note-type-general {
        border-left-color: #17a2b8 !important;
    }
    
    .chart-container {
        position: relative;
        height: 300px;
    }
    
    .department-stat-item {
        padding: 0.75rem;
        border-radius: 8px;
        background: #f8f9fa;
        margin-bottom: 0.5rem;
        transition: all 0.3s ease;
    }
    
    .department-stat-item:hover {
        background: #e9ecef;
        transform: translateX(5px);
    }
</style>
{% endblock %}

{% block page_title %}
<div class="d-flex align-items-center justify-content-between">
    <div>
        <h1 class="h3 mb-1 text-gray-800">
            <i class="fas fa-sticky-note text-primary me-2"></i>
            {{ title }}
        </h1>
        <p class="text-muted mb-0">نظام إدارة ملاحظات الموظفين الشامل</p>
    </div>
    <div>
        <a href="{% url 'Hr:notes:create' %}" class="btn btn-primary btn-lg">
            <i class="fas fa-plus me-2"></i>
            إضافة ملاحظة جديدة
        </a>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card bg-primary text-white h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stats-icon bg-white bg-opacity-20 me-3">
                        <i class="fas fa-sticky-note"></i>
                    </div>
                    <div>
                        <div class="h4 mb-0 font-weight-bold">{{ total_notes }}</div>
                        <div class="text-white-50">إجمالي الملاحظات</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card bg-success text-white h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stats-icon bg-white bg-opacity-20 me-3">
                        <i class="fas fa-thumbs-up"></i>
                    </div>
                    <div>
                        <div class="h4 mb-0 font-weight-bold">{{ positive_notes }}</div>
                        <div class="text-white-50">ملاحظات إيجابية</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card bg-danger text-white h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stats-icon bg-white bg-opacity-20 me-3">
                        <i class="fas fa-thumbs-down"></i>
                    </div>
                    <div>
                        <div class="h4 mb-0 font-weight-bold">{{ negative_notes }}</div>
                        <div class="text-white-50">ملاحظات سلبية</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card bg-warning text-white h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="stats-icon bg-white bg-opacity-20 me-3">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div>
                        <div class="h4 mb-0 font-weight-bold">{{ follow_up_notes }}</div>
                        <div class="text-white-50">تتطلب متابعة</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Recent Notes -->
<div class="row">
    <!-- Notes Distribution Chart -->
    <div class="col-xl-6 col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">توزيع الملاحظات حسب النوع</h6>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="notesTypeChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Monthly Trends Chart -->
    <div class="col-xl-6 col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">الاتجاهات الشهرية</h6>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="monthlyTrendsChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Notes and Department Stats -->
<div class="row">
    <!-- Recent Notes -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">الملاحظات الحديثة</h6>
                <a href="{% url 'Hr:notes:reports' %}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-chart-bar me-1"></i>
                    عرض التقارير
                </a>
            </div>
            <div class="card-body recent-notes-card">
                {% for note in recent_notes %}
                <div class="note-item note-type-{{ note.note_type }} p-3 mb-3 rounded">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">
                                <a href="{% url 'Hr:notes:detail' note.id %}" class="text-decoration-none">
                                    {{ note.title }}
                                </a>
                                {% if note.is_important %}
                                <span class="badge bg-warning text-dark ms-2">مهم</span>
                                {% endif %}
                            </h6>
                            <p class="text-muted mb-2">{{ note.content|truncatewords:15 }}</p>
                            <div class="d-flex align-items-center text-sm text-muted">
                                <i class="fas fa-user me-1"></i>
                                <span class="me-3">{{ note.employee.emp_full_name|default:note.employee.emp_first_name }}</span>
                                <i class="fas fa-clock me-1"></i>
                                <span>{{ note.created_at|timesince }} مضت</span>
                            </div>
                        </div>
                        <div class="ms-3">
                            <span class="badge bg-{{ note.get_note_type_display_color }}">
                                {{ note.get_note_type_display }}
                            </span>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="text-center py-4">
                    <i class="fas fa-sticky-note fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد ملاحظات حديثة</h5>
                    <p class="text-muted">ابدأ بإضافة ملاحظات للموظفين</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    
    <!-- Department Statistics -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">إحصائيات الأقسام</h6>
            </div>
            <div class="card-body">
                {% for dept in department_stats %}
                <div class="department-stat-item">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">{{ dept.dept_name }}</h6>
                            <small class="text-muted">{{ dept.notes_count }} ملاحظة</small>
                        </div>
                        <div class="text-primary">
                            <i class="fas fa-building fa-lg"></i>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="text-center py-3">
                    <i class="fas fa-building fa-2x text-muted mb-2"></i>
                    <p class="text-muted mb-0">لا توجد إحصائيات متاحة</p>
                </div>
                {% endfor %}
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">إجراءات سريعة</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'Hr:notes:create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة ملاحظة جديدة
                    </a>
                    <a href="{% url 'Hr:notes:reports' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-chart-bar me-2"></i>
                        عرض التقارير
                    </a>
                    <a href="{% url 'Hr:employees:list' %}" class="btn btn-outline-info">
                        <i class="fas fa-users me-2"></i>
                        قائمة الموظفين
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Notes Type Distribution Chart
    const typeCtx = document.getElementById('notesTypeChart').getContext('2d');
    new Chart(typeCtx, {
        type: 'doughnut',
        data: {
            labels: ['إيجابية', 'سلبية', 'عامة'],
            datasets: [{
                data: [{{ positive_notes }}, {{ negative_notes }}, {{ general_notes }}],
                backgroundColor: ['#28a745', '#dc3545', '#17a2b8'],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    
    // Monthly Trends Chart
    const monthlyCtx = document.getElementById('monthlyTrendsChart').getContext('2d');
    const monthlyData = {{ monthly_stats|safe }};
    
    new Chart(monthlyCtx, {
        type: 'line',
        data: {
            labels: monthlyData.map(item => item.month),
            datasets: [{
                label: 'عدد الملاحظات',
                data: monthlyData.map(item => item.count),
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
});
</script>
{% endblock %}
