{% extends 'Purchase_orders/base_purchase_orders.html' %}
{% load static %}

{% block title %}طلبات الشراء المعتمدة - نظام الدولية{% endblock %}

{% block page_title %}طلبات الشراء المعتمدة{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Purchase_orders:dashboard' %}">طلبات الشراء</a></li>
<li class="breadcrumb-item active">الطلبات المعتمدة</li>
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">قائمة طلبات الشراء المعتمدة</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>رقم الطلب</th>
                        <th>تاريخ الطلب</th>
                        <th>تاريخ الموافقة</th>
                        <th>المورد</th>
                        <th>معتمد بواسطة</th>
                        <th>الإجمالي</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for request in purchase_requests %}
                    <tr>
                        <td>
                            <a href="{% url 'Purchase_orders:purchase_request_detail' request.id %}">{{ request.request_number }}</a>
                        </td>
                        <td>{{ request.request_date|date:"Y-m-d" }}</td>
                        <td>{{ request.approval_date|date:"Y-m-d" }}</td>
                        <td>{{ request.vendor|default:"غير محدد" }}</td>
                        <td>{{ request.approved_by.get_full_name|default:request.approved_by.username }}</td>
                        <td>{{ request.total_amount }} ريال</td>
                        <td>
                            <a href="{% url 'Purchase_orders:purchase_request_detail' request.id %}" class="btn btn-sm btn-info">
                                <i class="fas fa-eye"></i>
                            </a>
                            <button class="btn btn-sm btn-primary" onclick="printRequest({{ request.id }})">
                                <i class="fas fa-print"></i>
                            </button>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="text-center py-4">لا توجد طلبات شراء معتمدة</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function printRequest(requestId) {
        window.open(`{% url 'Purchase_orders:purchase_request_detail' 0 %}`.replace('0', requestId) + '?print=true', '_blank');
    }
</script>
{% endblock %}
