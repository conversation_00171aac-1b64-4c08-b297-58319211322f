# Generated by Django 5.0.14 on 2025-06-11 14:48

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('meetings', '0003_meetingtask'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='MeetingTaskStep',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField(verbose_name='وصف الخطوة')),
                ('completed', models.BooleanField(default=False, verbose_name='مكتملة')),
                ('completion_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الإنجاز')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_meeting_task_steps', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('meeting_task', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='steps', to='meetings.meetingtask', verbose_name='مهمة الاجتماع')),
            ],
            options={
                'verbose_name': 'خطوة مهمة اجتماع',
                'verbose_name_plural': 'خطوات مهام الاجتماعات',
                'ordering': ['created_at'],
            },
        ),
    ]
