{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}{{ title }} - نظام الدولية{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'Hr/css/leaves.css' %}">
<link rel="stylesheet" href="{% static 'admin/css/vendor/select2/select2.css' %}">
<link rel="stylesheet" href="{% static 'admin/css/vendor/select2/select2.min.css' %}">
{% endblock %}

{% block page_title %}{{ title }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:employees:list' %}">الموظفين</a></li>
<li class="breadcrumb-item active">{{ title }}</li>
{% endblock %}

{% block content %}
<div class="card shadow-sm mb-4">
    <div class="card-header bg-white">
        <h5 class="mb-0">بحث في الإجازات</h5>
    </div>
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                {{ form.employee|as_crispy_field }}
            </div>
            <div class="col-md-4">
                {{ form.leave_type|as_crispy_field }}
            </div>
            <div class="col-md-4">
                {{ form.status|as_crispy_field }}
            </div>
            <div class="col-md-4">
                {{ form.date_from|as_crispy_field }}
            </div>
            <div class="col-md-4">
                {{ form.date_to|as_crispy_field }}
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-1"></i>
                    بحث
                </button>
                <a href="{% url 'Hr:leaves:list' %}" class="btn btn-light">
                    <i class="fas fa-redo me-1"></i>
                    إعادة تعيين
                </a>
            </div>
        </form>
    </div>
</div>

<div class="card shadow-sm">
    <div class="card-header bg-white d-flex justify-content-between align-items-center py-3">
        <h5 class="mb-0">
            <i class="fas fa-calendar-alt me-2"></i>
            {{ title }}
        </h5>
        <a href="{% url 'Hr:leaves:create' %}" class="btn btn-primary">
            <i class="fas fa-plus-circle me-1"></i>
            إضافة إجازة جديدة
        </a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>الموظف</th>
                        <th>نوع الإجازة</th>
                        <th>من تاريخ</th>
                        <th>إلى تاريخ</th>
                        <th>عدد الأيام</th>
                        <th>الحالة</th>
                        <th>تاريخ الطلب</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for leave in leaves %}
                    <tr>
                        <td>{{ leave.employee.emp_full_name }}</td>
                        <td>{{ leave.leave_type.name }}</td>
                        <td>{{ leave.start_date }}</td>
                        <td>{{ leave.end_date }}</td>
                        <td>{{ leave.total_days }}</td>
                        <td>
                            {% if leave.status == 'pending' %}
                            <span class="badge bg-warning">قيد الانتظار</span>
                            {% elif leave.status == 'approved' %}
                            <span class="badge bg-success">تمت الموافقة</span>
                            {% elif leave.status == 'rejected' %}
                            <span class="badge bg-danger">مرفوضة</span>
                            {% elif leave.status == 'cancelled' %}
                            <span class="badge bg-secondary">ملغاة</span>
                            {% endif %}
                        </td>
                        <td>{{ leave.created_at|date:"Y-m-d" }}</td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{% url 'Hr:leaves:detail' leave.pk %}" 
                                   class="btn btn-sm btn-outline-info" 
                                   title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if leave.status == 'pending' %}
                                <a href="{% url 'Hr:leaves:edit' leave.pk %}" 
                                   class="btn btn-sm btn-outline-primary" 
                                   title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'Hr:leaves:approve' leave.pk %}" 
                                   class="btn btn-sm btn-outline-success" 
                                   title="موافقة/رفض">
                                    <i class="fas fa-check-circle"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="8" class="text-center">لا توجد إجازات مسجلة</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'admin/js/vendor/select2/select2.full.min.js' %}"></script>
<script src="{% static 'admin/js/vendor/select2/i18n/ar.js' %}"></script>
<script>
    $(document).ready(function() {
        $('.select2').select2({
            language: "ar",
            dir: "rtl"
        });
    });
</script>
{% endblock %}
