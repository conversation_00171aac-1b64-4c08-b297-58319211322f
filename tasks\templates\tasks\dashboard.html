{% extends 'tasks/base_tasks.html' %}
{% load static %}

{% block title %}لوحة تحكم المهام - نظام الدولية{% endblock %}

{% block page_title %}لوحة تحكم المهام{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item active">لوحة تحكم المهام</li>
{% endblock %}

{% block content %}
<div class="row">
    <!-- Summary Cards -->
    <div class="col-md-6 col-lg-3">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-subtitle text-muted mb-1">إجمالي المهام</h6>
                        <h2 class="mb-0">{{ total_tasks }}</h2>
                    </div>
                    <div class="bg-primary rounded-circle p-3 d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                        <i class="fas fa-tasks fa-2x text-white"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{% url 'tasks:list' %}" class="btn btn-sm btn-light w-100">
                        <i class="fas fa-arrow-left ml-1"></i> عرض المهام
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 col-lg-3">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-subtitle text-muted mb-1">المهام المكتملة</h6>
                        <h2 class="mb-0 text-success">{{ completed_tasks }}</h2>
                    </div>
                    <div class="bg-success rounded-circle p-3 d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                        <i class="fas fa-check-circle fa-2x text-white"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{% url 'tasks:completed' %}" class="btn btn-sm btn-light w-100">
                        <i class="fas fa-arrow-left ml-1"></i> عرض المهام المكتملة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 col-lg-3">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-subtitle text-muted mb-1">المهام قيد التنفيذ</h6>
                        <h2 class="mb-0 text-info">{{ in_progress_tasks }}</h2>
                    </div>
                    <div class="bg-info rounded-circle p-3 d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                        <i class="fas fa-spinner fa-2x text-white"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{% url 'tasks:list' %}?status=in_progress" class="btn btn-sm btn-light w-100">
                        <i class="fas fa-arrow-left ml-1"></i> عرض المهام قيد التنفيذ
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 col-lg-3">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-subtitle text-muted mb-1">المهام المتأخرة</h6>
                        <h2 class="mb-0 text-danger">{{ overdue_tasks }}</h2>
                    </div>
                    <div class="bg-danger rounded-circle p-3 d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                        <i class="fas fa-exclamation-circle fa-2x text-white"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{% url 'tasks:list' %}?status=overdue" class="btn btn-sm btn-light w-100">
                        <i class="fas fa-arrow-left ml-1"></i> عرض المهام المتأخرة
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <!-- My Tasks -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">مهامي</h5>
                <a href="{% url 'tasks:my_tasks' %}" class="btn btn-sm btn-primary">عرض الكل</a>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>العنوان</th>
                                <th>تاريخ الإنشاء</th>
                                <th>تاريخ الاستحقاق</th>
                                <th>الأولوية</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for task in my_tasks %}
                            <tr>
                                <td>
                                    <a href="{% url 'tasks:detail' task.id %}">{{ task.title }}</a>
                                </td>
                                <td>{{ task.created_at|date:"Y-m-d" }}</td>
                                <td>{{ task.due_date|date:"Y-m-d" }}</td>
                                <td>
                                    {% if task.priority == 'high' %}
                                    <span class="badge bg-danger">عالية</span>
                                    {% elif task.priority == 'medium' %}
                                    <span class="badge bg-warning">متوسطة</span>
                                    {% else %}
                                    <span class="badge bg-info">منخفضة</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if task.status == 'pending' %}
                                    <span class="badge bg-warning">قيد الانتظار</span>
                                    {% elif task.status == 'in_progress' %}
                                    <span class="badge bg-info">قيد التنفيذ</span>
                                    {% elif task.status == 'completed' %}
                                    <span class="badge bg-success">مكتمل</span>
                                    {% elif task.status == 'cancelled' %}
                                    <span class="badge bg-danger">ملغي</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="text-center py-4">لا توجد مهام مسندة إليك</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">إجراءات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-3">
                    <a href="{% url 'tasks:create' %}" class="btn btn-primary">
                        <i class="fas fa-plus-circle me-2"></i> إنشاء مهمة جديدة
                    </a>
                    <a href="{% url 'tasks:my_tasks' %}" class="btn btn-success">
                        <i class="fas fa-user-check me-2"></i> عرض مهامي
                    </a>
                    <a href="{% url 'tasks:reports' %}" class="btn btn-info">
                        <i class="fas fa-chart-bar me-2"></i> تقارير المهام
                    </a>
                </div>
            </div>
        </div>

        <!-- Overdue Tasks -->
        <div class="card mt-4">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-circle me-2"></i> المهام المتأخرة
                </h5>
            </div>
            <div class="card-body">
                {% if overdue_tasks_list %}
                <ul class="list-group list-group-flush">
                    {% for task in overdue_tasks_list %}
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span>{{ task.title }}</span>
                        <span class="badge bg-danger rounded-pill">{{ task.due_date|date:"Y-m-d" }}</span>
                    </li>
                    {% endfor %}
                </ul>
                <div class="mt-3">
                    <a href="{% url 'tasks:list' %}?status=overdue" class="btn btn-danger w-100">
                        عرض جميع المهام المتأخرة
                    </a>
                </div>
                {% else %}
                <p class="text-center mb-0">لا توجد مهام متأخرة</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
