# Generated by Django 5.0.14 on 2025-05-07 05:28

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='PurchaseRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('request_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الطلب')),
                ('request_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الطلب')),
                ('status', models.CharField(choices=[('pending', 'قيد الانتظار'), ('approved', 'تمت الموافقة'), ('rejected', 'مرفوض'), ('completed', 'مكتمل')], default='pending', max_length=20, verbose_name='حالة الطلب')),
                ('approval_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الموافقة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
            ],
            options={
                'verbose_name': 'طلب شراء',
                'verbose_name_plural': 'طلبات الشراء',
                'ordering': ['-request_date'],
            },
        ),
        migrations.CreateModel(
            name='PurchaseRequestItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity_requested', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الكمية المطلوبة')),
                ('status', models.CharField(choices=[('pending', 'قيد الانتظار'), ('approved', 'تمت الموافقة'), ('rejected', 'مرفوض'), ('transferred', 'تم الترحيل')], default='pending', max_length=20, verbose_name='حالة العنصر')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'عنصر طلب الشراء',
                'verbose_name_plural': 'عناصر طلبات الشراء',
                'ordering': ['purchase_request', 'created_at'],
            },
        ),
        migrations.CreateModel(
            name='Vendor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم المورد')),
                ('contact_person', models.CharField(blank=True, max_length=100, null=True, verbose_name='جهة الاتصال')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('address', models.TextField(blank=True, null=True, verbose_name='العنوان')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'مورد',
                'verbose_name_plural': 'الموردين',
                'ordering': ['name'],
            },
        ),
    ]
