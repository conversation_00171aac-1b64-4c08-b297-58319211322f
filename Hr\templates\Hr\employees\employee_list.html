{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load image_utils %}
{% load form_utils %}
{% load django_permissions %}

{% block title %}قائمة الموظفين - نظام الدولية{% endblock %}

{% block page_title %}
<div class="modern-page-header">
    <!-- Centered Header Section -->
    <div class="row justify-content-center mb-4">
        <div class="col-lg-8 text-center">
            <div class="page-title-section">
                <h1 class="page-title mb-2 fw-bold text-dark">
                    <i class="fas fa-users text-primary me-2"></i>
                    إدارة الموظفين
                </h1>
                <p class="page-subtitle text-muted mb-0 fs-5">عرض وإدارة بيانات الموظفين في النظام</p>
            </div>
        </div>
    </div>

    <!-- Add Employee Button - Left Side (RTL) -->
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-start">
                {% if perms.Hr.add_employee or user|is_admin %}
                <a href="{% url 'Hr:employees:create' %}" class="btn btn-primary btn-lg modern-btn shadow-sm">
                    <i class="fas fa-user-plus me-2"></i>
                    إضافة موظف جديد
                </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item active">قائمة الموظفين</li>
{% endblock %}

{% block content %}
<!-- Loading Overlay -->
<div id="loadingOverlay" class="loading-overlay" style="display: none;">
    <div class="text-center">
        <div class="loading-spinner mb-3"></div>
        <h5 class="text-primary">جاري تحديث البيانات...</h5>
        <p class="text-muted">يرجى الانتظار</p>
    </div>
</div>
<!-- Enhanced Statistics Cards with Modern Design -->
<div class="stats-section mb-5" id="statsSection">
    <div class="row g-4">
        <div class="col-xl-3 col-md-6">
            <div class="card stats-card ultra-modern-stats-card bg-gradient-primary border-0 h-100 hover-lift-enhanced">
                <div class="card-body d-flex align-items-center p-4 position-relative overflow-hidden">
                    <div class="stats-icon-wrapper me-3 position-relative z-2">
                        <div class="stats-icon-circle bg-white bg-opacity-20 backdrop-blur">
                            <i class="fas fa-users fa-lg text-white"></i>
                        </div>
                    </div>
                    <div class="stats-content position-relative z-2">
                        <h3 class="stats-number text-white mb-1 fw-bold counter-animate" id="totalEmployeesCount" data-target="{{ total_employees }}">{{ total_employees }}</h3>
                        <p class="stats-title text-white text-opacity-90 mb-0 fw-medium">إجمالي الموظفين</p>
                        <small class="text-white text-opacity-75" id="totalEmployeesSubtext">في النظام</small>
                    </div>
                    <div class="stats-decoration position-absolute">
                        <i class="fas fa-users opacity-05"></i>
                    </div>
                    <div class="stats-glow position-absolute top-0 start-0 w-100 h-100"></div>
                </div>
                <div class="card-footer bg-transparent border-0 py-2">
                    <div class="progress ultra-modern-progress" style="height: 4px;">
                        <div class="progress-bar bg-white progress-bar-animated progress-bar-striped" style="width: 100%" role="progressbar"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card stats-card ultra-modern-stats-card bg-gradient-success border-0 h-100 hover-lift-enhanced">
                <div class="card-body d-flex align-items-center p-4 position-relative overflow-hidden">
                    <div class="stats-icon-wrapper me-3 position-relative z-2">
                        <div class="stats-icon-circle bg-white bg-opacity-20 backdrop-blur">
                            <i class="fas fa-user-check fa-lg text-white"></i>
                        </div>
                    </div>
                    <div class="stats-content position-relative z-2">
                        <h3 class="stats-number text-white mb-1 fw-bold counter-animate" id="activeEmployeesCount" data-target="{{ active_employees }}">{{ active_employees }}</h3>
                        <p class="stats-title text-white text-opacity-90 mb-0 fw-medium">الموظفين النشطين</p>
                        <small class="text-white text-opacity-75" id="activeEmployeesPercentage">{% widthratio active_employees total_employees 100 %}% من الإجمالي</small>
                    </div>
                    <div class="stats-decoration position-absolute">
                        <i class="fas fa-user-check opacity-05"></i>
                    </div>
                    <div class="stats-glow position-absolute top-0 start-0 w-100 h-100"></div>
                </div>
                <div class="card-footer bg-transparent border-0 py-2">
                    <div class="progress ultra-modern-progress" style="height: 4px;">
                        <div class="progress-bar bg-white progress-bar-animated progress-bar-striped" id="activeEmployeesProgress" style="width: {% widthratio active_employees total_employees 100 %}%" role="progressbar"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card stats-card ultra-modern-stats-card bg-gradient-warning border-0 h-100 hover-lift-enhanced">
                <div class="card-body d-flex align-items-center p-4 position-relative overflow-hidden">
                    <div class="stats-icon-wrapper me-3 position-relative z-2">
                        <div class="stats-icon-circle bg-white bg-opacity-20 backdrop-blur">
                            <i class="fas fa-pause-circle fa-lg text-white"></i>
                        </div>
                    </div>
                    <div class="stats-content position-relative z-2">
                        <h3 class="stats-number text-white mb-1 fw-bold counter-animate" id="onLeaveEmployeesCount" data-target="{{ on_leave_employees }}">{{ on_leave_employees }}</h3>
                        <p class="stats-title text-white text-opacity-90 mb-0 fw-medium">في إجازة</p>
                        <small class="text-white text-opacity-75" id="onLeaveEmployeesSubtext">موظفين مؤقتاً</small>
                    </div>
                    <div class="stats-decoration position-absolute">
                        <i class="fas fa-pause-circle opacity-05"></i>
                    </div>
                    <div class="stats-glow position-absolute top-0 start-0 w-100 h-100"></div>
                </div>
                <div class="card-footer bg-transparent border-0 py-2">
                    <div class="progress ultra-modern-progress" style="height: 4px;">
                        <div class="progress-bar bg-white progress-bar-animated progress-bar-striped" id="onLeaveEmployeesProgress" style="width: {% widthratio on_leave_employees total_employees 100 %}%" role="progressbar"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card stats-card ultra-modern-stats-card bg-gradient-danger border-0 h-100 hover-lift-enhanced">
                <div class="card-body d-flex align-items-center p-4 position-relative overflow-hidden">
                    <div class="stats-icon-wrapper me-3 position-relative z-2">
                        <div class="stats-icon-circle bg-white bg-opacity-20 backdrop-blur">
                            <i class="fas fa-user-times fa-lg text-white"></i>
                        </div>
                    </div>
                    <div class="stats-content position-relative z-2">
                        <h3 class="stats-number text-white mb-1 fw-bold counter-animate" id="resignedEmployeesCount" data-target="{{ resigned_employees }}">{{ resigned_employees }}</h3>
                        <p class="stats-title text-white text-opacity-90 mb-0 fw-medium">المستقيلين</p>
                        <small class="text-white text-opacity-75" id="resignedEmployeesSubtext">موظفين سابقين</small>
                    </div>
                    <div class="stats-decoration position-absolute">
                        <i class="fas fa-user-times opacity-05"></i>
                    </div>
                    <div class="stats-glow position-absolute top-0 start-0 w-100 h-100"></div>
                </div>
                <div class="card-footer bg-transparent border-0 py-2">
                    <div class="progress ultra-modern-progress" style="height: 4px;">
                        <div class="progress-bar bg-white progress-bar-animated progress-bar-striped" id="resignedEmployeesProgress" style="width: {% widthratio resigned_employees total_employees 100 %}%" role="progressbar"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Ultra Modern Status Toggle -->
<div class="ultra-modern-toggle-container mb-5" id="statusToggleContainer">
    <div class="card border-0 shadow-lg ultra-modern-toggle-card">
        <div class="card-body p-4">
            <div class="d-flex align-items-center justify-content-between flex-wrap gap-3">
                <div class="d-flex align-items-center">
                    <div class="ultra-modern-toggle-wrapper me-4">
                        <input id="employeeStatusToggle" type="checkbox" class="ultra-modern-toggle-input" {% if status != 'inactive' %}checked{% endif %}>
                        <label for="employeeStatusToggle" class="ultra-modern-toggle-label">
                            <span class="ultra-modern-toggle-slider">
                                <span class="ultra-modern-toggle-handle">
                                    <i class="fas fa-user-check toggle-icon-active"></i>
                                    <i class="fas fa-user-times toggle-icon-inactive"></i>
                                </span>
                            </span>
                        </label>
                    </div>
                    <div class="toggle-content">
                        <h5 id="toggleStatusText" class="mb-1 fw-bold d-flex align-items-center {% if status == 'inactive' %}text-danger{% else %}text-success{% endif %}">
                            <i id="toggleStatusIcon" class="fas {% if status == 'inactive' %}fa-user-times{% else %}fa-user-check{% endif %} me-2"></i>
                            <span id="toggleStatusLabel">{% if status == 'inactive' %}موظفين غير نشطين{% else %}موظفين نشطين{% endif %}</span>
                        </h5>
                        <p class="text-muted mb-0 small">انقر للتبديل بين عرض الموظفين النشطين وغير النشطين</p>
                    </div>
                </div>
                <div class="toggle-stats">
                    <div class="d-flex gap-4">
                        <div class="text-center stats-mini-card active-stats">
                            <div class="stats-mini-icon bg-success-subtle text-success mb-1">
                                <i class="fas fa-user-check"></i>
                            </div>
                            <div class="fw-bold text-success fs-4" id="toggleActiveCount">{{ active_employees }}</div>
                            <small class="text-muted fw-medium">نشط</small>
                        </div>
                        <div class="text-center stats-mini-card inactive-stats">
                            <div class="stats-mini-icon bg-danger-subtle text-danger mb-1">
                                <i class="fas fa-user-times"></i>
                            </div>
                            <div class="fw-bold text-danger fs-4" id="toggleInactiveCount">{{ resigned_employees }}</div>
                            <small class="text-muted fw-medium">مستقيل</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<div class="row g-4">
    <div class="col-12 mb-4">
        <div class="card shadow-lg ultra-modern-filter-card border-0">
            <div class="card-header bg-gradient-primary text-white border-0 py-4">
                <div class="d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <div class="filter-header-icon me-3">
                            <i class="fas fa-filter fa-lg"></i>
                        </div>
                        <div>
                            <h5 class="mb-1 fw-bold">البحث والتصفية المتقدمة</h5>
                            <p class="mb-0 text-white-50 small">ابحث عن الموظفين باستخدام معايير متعددة ومتقدمة</p>
                        </div>
                    </div>
                    <div class="filter-stats text-white-50 text-end">
                        <small>{{ employees|length }} نتيجة</small>
                    </div>
                </div>
            </div>
            <div class="card-body p-4">
                <form method="get" action="" id="employeeFilterForm">
                    <!-- إذا كانت هناك فلاتر نشطة، نعرض ملخص لها -->
                    {% if request.GET %}
                    <div class="active-filters mb-3 p-2 bg-light rounded border">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="small fw-medium text-primary">
                                <i class="fas fa-filter me-1"></i> الفلاتر النشطة
                            </span>
                            <a href="{% url 'Hr:employees:list' %}" class="btn btn-sm btn-outline-secondary px-2 py-0">
                                <i class="fas fa-times-circle"></i> مسح الكل
                            </a>
                        </div>
                        <div class="active-filter-tags d-flex flex-wrap gap-1">
                            {% if request.GET.search %}
                            <span class="badge bg-primary-subtle text-primary">
                                الاسم: {{ request.GET.search }}
                                <a href="#" class="text-primary ms-1 remove-filter" data-field="search"><i class="fas fa-times-circle"></i></a>
                            </span>
                            {% endif %}
                            {% if request.GET.emp_code %}
                            <span class="badge bg-primary-subtle text-primary">
                                الكود: {{ request.GET.emp_code }}
                                <a href="#" class="text-primary ms-1 remove-filter" data-field="emp_code"><i class="fas fa-times-circle"></i></a>
                            </span>
                            {% endif %}
                            {% if request.GET.department %}
                            <span class="badge bg-primary-subtle text-primary">
                                القسم: {{ filter_form.department.field.choices|dictsort:"0"|dict_get:request.GET.department }}
                                <a href="#" class="text-primary ms-1 remove-filter" data-field="department"><i class="fas fa-times-circle"></i></a>
                            </span>
                            {% endif %}
                            {% if request.GET.job %}
                            <span class="badge bg-primary-subtle text-primary">
                                الوظيفة
                                <a href="#" class="text-primary ms-1 remove-filter" data-field="job"><i class="fas fa-times-circle"></i></a>
                            </span>
                            {% endif %}
                            {% if request.GET.working_condition and request.GET.working_condition != 'سارى' %}
                            <span class="badge bg-primary-subtle text-primary">
                                حالة العمل: {{ request.GET.working_condition }}
                                <a href="#" class="text-primary ms-1 remove-filter" data-field="working_condition"><i class="fas fa-times-circle"></i></a>
                            </span>
                            {% endif %}
                            {% if request.GET.insurance_status %}
                            <span class="badge bg-primary-subtle text-primary">
                                حالة التأمين: {{ request.GET.insurance_status }}
                                <a href="#" class="text-primary ms-1 remove-filter" data-field="insurance_status"><i class="fas fa-times-circle"></i></a>
                            </span>
                            {% endif %}
                            <!-- عرض الفلاتر المتقدمة النشطة -->
                            {% comment %} Advanced filter tags were here {% endcomment %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Enhanced Search Section -->
                    <div class="search-section mb-4">
                        <div class="row g-3">
                            <!-- Main Search -->
                            <div class="col-lg-8">
                                <div class="modern-search-wrapper position-relative">
                                    <div class="input-group search-main">
                                        <span class="input-group-text bg-primary text-white border-primary">
                                            <i class="fas fa-search"></i>
                                        </span>
                                        <input type="text" name="search" class="form-control search-autocomplete border-primary"
                                               placeholder="بحث شامل: الاسم، الكود، الرقم القومي، الهاتف، العنوان..."
                                               value="{{ request.GET.search|default:'' }}" autocomplete="off">
                                        <button type="submit" class="btn btn-primary px-4">
                                            <i class="fas fa-search me-1"></i>بحث
                                        </button>
                                    </div>
                                    <div class="search-results-container position-relative">
                                        <div class="search-results-dropdown d-none position-absolute w-100 bg-white shadow-lg rounded border z-3" style="max-height: 350px; overflow-y: auto;"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Quick Filters -->
                            <div class="col-lg-4">
                                <div class="d-flex gap-2 h-100 align-items-center">
                                    <button type="button" class="btn btn-outline-secondary flex-fill" id="advancedSearchToggle">
                                        <i class="fas fa-sliders-h me-1"></i>فلاتر متقدمة
                                    </button>
                                    <button type="button" class="btn btn-outline-info" id="viewToggle" title="تبديل العرض">
                                        <i class="fas fa-th-large"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Filter Groups -->
                    <div class="filter-groups">
                        <!-- Basic Filters -->
                        <div class="filter-group mb-4">
                            <div class="filter-group-header mb-3">
                                <h6 class="filter-group-title mb-0 fw-semibold text-dark d-flex align-items-center">
                                    <i class="fas fa-filter me-2 text-primary"></i>
                                    الفلاتر الأساسية
                                </h6>
                            </div>

                            <div class="row g-3">
                                <div class="col-lg-3 col-md-6">
                                    <label class="form-label small fw-medium text-dark">
                                        <i class="fas fa-id-badge me-1 text-primary"></i>
                                        كود الموظف
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-light border-end-0">
                                            <i class="fas fa-hashtag text-muted small"></i>
                                        </span>
                                        <input type="text" name="emp_code" class="form-control border-start-0"
                                               placeholder="أدخل كود الموظف" value="{{ request.GET.emp_code|default:'' }}">
                                    </div>
                                </div>

                                <div class="col-lg-3 col-md-6">
                                    <label class="form-label small fw-medium text-dark">
                                        <i class="fas fa-building me-1 text-primary"></i>
                                        {{ filter_form.department.label }}
                                    </label>
                                    {{ filter_form.department }}
                                </div>

                                <div class="col-lg-3 col-md-6">
                                    <label class="form-label small fw-medium text-dark">
                                        <i class="fas fa-briefcase me-1 text-primary"></i>
                                        {{ filter_form.job.label }}
                                    </label>
                                    {{ filter_form.job }}
                                </div>

                                <div class="col-lg-3 col-md-6">
                                    <label class="form-label small fw-medium text-dark">
                                        <i class="fas fa-user-check me-1 text-primary"></i>
                                        {{ filter_form.working_condition.label }}
                                    </label>
                                    {{ filter_form.working_condition }}
                                </div>
                            </div>
                        </div>

                        <!-- Advanced Filters (Collapsible) -->
                        <div class="collapse" id="advancedFilters">
                            <div class="filter-group mb-4">
                                <div class="filter-group-header mb-3">
                                    <h6 class="filter-group-title mb-0 fw-semibold text-dark d-flex align-items-center">
                                        <i class="fas fa-search-plus me-2 text-info"></i>
                                        فلاتر متقدمة
                                    </h6>
                                </div>

                                <div class="row g-3">
                                    <!-- Contact Information -->
                                    <div class="col-lg-4 col-md-6">
                                        <label class="form-label small fw-medium text-dark">
                                            <i class="fas fa-phone me-1 text-primary"></i>
                                            رقم الهاتف
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-light border-end-0">
                                                <i class="fas fa-phone text-muted small"></i>
                                            </span>
                                            <input type="text" name="phone" class="form-control border-start-0"
                                                   placeholder="بحث برقم الهاتف" value="{{ request.GET.phone|default:'' }}">
                                        </div>
                                    </div>

                                    <div class="col-lg-4 col-md-6">
                                        <label class="form-label small fw-medium text-dark">
                                            <i class="fas fa-id-card me-1 text-primary"></i>
                                            الرقم القومي
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-light border-end-0">
                                                <i class="fas fa-id-card text-muted small"></i>
                                            </span>
                                            <input type="text" name="national_id" class="form-control border-start-0"
                                                   placeholder="بحث بالرقم القومي" value="{{ request.GET.national_id|default:'' }}">
                                        </div>
                                    </div>

                                    <div class="col-lg-4 col-md-6">
                                        <label class="form-label small fw-medium text-dark">
                                            <i class="fas fa-shield-alt me-1 text-primary"></i>
                                            {{ filter_form.insurance_status.label }}
                                        </label>
                                        {{ filter_form.insurance_status }}
                                    </div>

                                    <!-- Car Information -->
                                    <div class="col-lg-4 col-md-6">
                                        <label class="form-label small fw-medium text-dark">
                                            <i class="fas fa-car me-1 text-primary"></i>
                                            السيارة
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-light border-end-0">
                                                <i class="fas fa-car text-muted small"></i>
                                            </span>
                                            <input type="text" name="car" class="form-control border-start-0"
                                                   placeholder="بحث بالسيارة" value="{{ request.GET.car|default:'' }}">
                                        </div>
                                    </div>

                                    <div class="col-lg-4 col-md-6">
                                        <label class="form-label small fw-medium text-dark">
                                            <i class="fas fa-map-marker-alt me-1 text-primary"></i>
                                            نقطة التقاط السيارة
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-light border-end-0">
                                                <i class="fas fa-map-marker-alt text-muted small"></i>
                                            </span>
                                            <input type="text" name="car_pick_up_point" class="form-control border-start-0"
                                                   placeholder="نقطة التقاط السيارة" value="{{ request.GET.car_pick_up_point|default:'' }}">
                                        </div>
                                    </div>

                                    <div class="col-lg-4 col-md-6">
                                        <label class="form-label small fw-medium text-dark">
                                            <i class="fas fa-clock me-1 text-primary"></i>
                                            نوع الوردية
                                        </label>
                                        <select name="shift_type" class="form-select">
                                            <option value="">جميع الورديات</option>
                                            <option value="صباحي" {% if request.GET.shift_type == 'صباحي' %}selected{% endif %}>صباحي</option>
                                            <option value="مسائي" {% if request.GET.shift_type == 'مسائي' %}selected{% endif %}>مسائي</option>
                                            <option value="ليلي" {% if request.GET.shift_type == 'ليلي' %}selected{% endif %}>ليلي</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {% comment %} Advanced search section removed {% endcomment %}

                    <!-- Enhanced Action Buttons -->
                    <div class="filter-actions mt-4">
                        <div class="row g-2">
                            <div class="col-md-6">
                                <button type="submit" class="btn btn-primary w-100 btn-lg">
                                    <i class="fas fa-search me-2"></i>
                                    تطبيق الفلاتر
                                </button>
                            </div>
                            <div class="col-md-3">
                                <a href="{% url 'Hr:employees:list' %}" class="btn btn-outline-secondary w-100 btn-lg">
                                    <i class="fas fa-undo me-2"></i>
                                    إعادة تعيين
                                </a>
                            </div>
                            <div class="col-md-3">
                                <button type="button" class="btn btn-outline-info w-100 btn-lg" data-bs-toggle="collapse"
                                        data-bs-target="#advancedFilters" aria-expanded="false" aria-controls="advancedFilters">
                                    <i class="fas fa-sliders-h me-2"></i>
                                    متقدم
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-12">
        <div class="card shadow-sm">
            <!-- Enhanced Card Header -->
            <div class="card-header bg-white border-bottom py-4">
                <div class="row align-items-center">
                    <div class="col-lg-6">
                        <div class="header-content">
                            <h5 id="employeeListTitle" class="mb-2 fw-bold text-dark">
                                {% if status == 'inactive' %}
                                <i id="employeeListIcon" class="fas fa-user-times text-danger me-2"></i>
                                <span id="employeeListTitleText">قائمة الموظفين غير النشطين</span>
                                {% else %}
                                <i id="employeeListIcon" class="fas fa-users text-success me-2"></i>
                                <span id="employeeListTitleText">قائمة الموظفين النشطين</span>
                                {% endif %}
                            </h5>
                            {% if employees %}
                            <div class="d-flex align-items-center gap-3">
                                <div class="result-count">
                                    <span class="badge bg-primary-subtle text-primary px-3 py-2">
                                        <i class="fas fa-users me-1"></i>
                                        <span class="fw-bold" id="employeeListCount">{{ employees|length }}</span> موظف
                                    </span>
                                </div>
                                {% if request.GET %}
                                <small class="text-muted">
                                    <i class="fas fa-filter me-1"></i>
                                    مطابق لمعايير البحث
                                </small>
                                {% endif %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <div class="d-flex justify-content-end gap-2">
                            <!-- View Toggle -->
                            <div class="btn-group view-toggle" role="group">
                                <button type="button" class="btn btn-outline-secondary active" id="tableViewBtn" title="عرض جدول">
                                    <i class="fas fa-table"></i>
                                </button>
                                <button type="button" class="btn btn-outline-secondary" id="cardViewBtn" title="عرض بطاقات">
                                    <i class="fas fa-th-large"></i>
                                </button>
                            </div>

                            <!-- Sort Dropdown -->
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="sortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-sort me-1"></i>
                                    ترتيب
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="sortDropdown">
                                    <li><h6 class="dropdown-header">ترتيب حسب</h6></li>
                                    <li><a class="dropdown-item sort-option" href="#" data-sort="emp_id">
                                        <i class="fas fa-hashtag me-2 text-primary"></i>رقم الموظف
                                    </a></li>
                                    <li><a class="dropdown-item sort-option" href="#" data-sort="emp_full_name">
                                        <i class="fas fa-user me-2 text-primary"></i>الاسم
                                    </a></li>
                                    <li><a class="dropdown-item sort-option" href="#" data-sort="department">
                                        <i class="fas fa-building me-2 text-primary"></i>القسم
                                    </a></li>
                                    <li><a class="dropdown-item sort-option" href="#" data-sort="emp_date_hiring">
                                        <i class="fas fa-calendar me-2 text-primary"></i>تاريخ التعيين
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><h6 class="dropdown-header">اتجاه الترتيب</h6></li>
                                    <li><a class="dropdown-item sort-direction" href="#" data-direction="asc">
                                        <i class="fas fa-arrow-up me-2 text-success"></i>تصاعدي
                                    </a></li>
                                    <li><a class="dropdown-item sort-direction" href="#" data-direction="desc">
                                        <i class="fas fa-arrow-down me-2 text-danger"></i>تنازلي
                                    </a></li>
                                </ul>
                            </div>

                            <!-- Add Employee Button -->
                            {% if perms.Hr.add_employee or user|is_admin %}
                            <a href="{% url 'Hr:employees:create' %}" class="btn btn-primary">
                                <i class="fas fa-user-plus me-2"></i>
                                إضافة موظف جديد
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body p-0" id="employeeListContainer">
                {% if employees %}

                <!-- Modern Card View (Hidden by default) -->
                <div id="cardView" class="employee-cards-container p-4" style="display: none;">
                    <div class="row g-4">
                        {% for employee in employees %}
                        <div class="col-xl-4 col-lg-6 col-md-6">
                            <div class="employee-card modern-employee-card h-100" data-emp-id="{{ employee.emp_id }}"
                                 data-emp-name="{{ employee.emp_full_name|default:employee.emp_first_name }}"
                                 data-dept="{{ employee.department.dept_name|default:'' }}"
                                 data-condition="{{ employee.working_condition|default:'' }}">
                                <div class="card border-0 shadow-sm h-100 hover-lift">
                                    <div class="card-body p-4">
                                        <!-- Employee Header -->
                                        <div class="employee-header d-flex align-items-center mb-3">
                                            <div class="employee-avatar me-3">
                                                {% if employee.emp_image %}
                                                <img src="{{ employee.emp_image|binary_to_img }}" alt="{{ employee.emp_full_name }}"
                                                     class="rounded-circle employee-card-img" width="60" height="60">
                                                {% else %}
                                                <div class="avatar-placeholder bg-primary text-white rounded-circle d-flex align-items-center justify-content-center"
                                                     style="width: 60px; height: 60px; font-size: 1.5rem; font-weight: bold;">
                                                    {{ employee.emp_first_name|slice:":1"|upper }}
                                                </div>
                                                {% endif %}
                                            </div>
                                            <div class="employee-info flex-grow-1">
                                                <h6 class="employee-name mb-1 fw-bold text-dark">
                                                    {{ employee.emp_full_name|default:employee.emp_first_name }}
                                                </h6>
                                                <div class="employee-id">
                                                    <span class="badge bg-light text-dark border">
                                                        <i class="fas fa-hashtag me-1"></i>{{ employee.emp_id }}
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="employee-status">
                                                {% if employee.working_condition == 'سارى' %}
                                                <span class="status-indicator bg-success"></span>
                                                {% elif employee.working_condition == 'منقطع عن العمل' %}
                                                <span class="status-indicator bg-warning"></span>
                                                {% elif employee.working_condition == 'استقالة' %}
                                                <span class="status-indicator bg-danger"></span>
                                                {% else %}
                                                <span class="status-indicator bg-secondary"></span>
                                                {% endif %}
                                            </div>
                                        </div>

                                        <!-- Employee Details -->
                                        <div class="employee-details">
                                            <div class="detail-row d-flex align-items-center mb-2">
                                                <i class="fas fa-building text-primary me-2" style="width: 16px;"></i>
                                                <span class="text-muted small">القسم:</span>
                                                <span class="ms-auto fw-medium">
                                                    {{ employee.department.dept_name|default:"-" }}
                                                </span>
                                            </div>
                                            <div class="detail-row d-flex align-items-center mb-2">
                                                <i class="fas fa-briefcase text-primary me-2" style="width: 16px;"></i>
                                                <span class="text-muted small">الوظيفة:</span>
                                                <span class="ms-auto fw-medium">
                                                    {{ employee.jop_name|default:"-" }}
                                                </span>
                                            </div>
                                            {% if employee.emp_phone1 %}
                                            <div class="detail-row d-flex align-items-center mb-2">
                                                <i class="fas fa-phone text-primary me-2" style="width: 16px;"></i>
                                                <span class="text-muted small">الهاتف:</span>
                                                <a href="tel:{{ employee.emp_phone1 }}" class="ms-auto text-decoration-none fw-medium">
                                                    {{ employee.emp_phone1 }}
                                                </a>
                                            </div>
                                            {% endif %}
                                            {% if employee.national_id %}
                                            <div class="detail-row d-flex align-items-center mb-3">
                                                <i class="fas fa-id-card text-primary me-2" style="width: 16px;"></i>
                                                <span class="text-muted small">الرقم القومي:</span>
                                                <span class="ms-auto fw-medium small">
                                                    {{ employee.national_id }}
                                                </span>
                                            </div>
                                            {% endif %}
                                        </div>

                                        <!-- Working Status Badge -->
                                        <div class="employee-status-badge mb-3">
                                            {% if employee.working_condition == 'سارى' %}
                                            <span class="badge bg-success-subtle text-success border border-success-subtle px-3 py-2 w-100">
                                                <i class="fas fa-check-circle me-1"></i>نشط
                                            </span>
                                            {% elif employee.working_condition == 'منقطع عن العمل' %}
                                            <span class="badge bg-warning-subtle text-warning border border-warning-subtle px-3 py-2 w-100">
                                                <i class="fas fa-pause-circle me-1"></i>منقطع عن العمل
                                            </span>
                                            {% elif employee.working_condition == 'استقالة' %}
                                            <span class="badge bg-danger-subtle text-danger border border-danger-subtle px-3 py-2 w-100">
                                                <i class="fas fa-times-circle me-1"></i>استقالة
                                            </span>
                                            {% else %}
                                            <span class="badge bg-secondary-subtle text-secondary border border-secondary-subtle px-3 py-2 w-100">
                                                <i class="fas fa-question-circle me-1"></i>{{ employee.working_condition|default:"-" }}
                                            </span>
                                            {% endif %}
                                        </div>

                                        <!-- Action Buttons -->
                                        <div class="employee-actions">
                                            <div class="btn-group w-100" role="group">
                                                <a href="{% url 'Hr:employees:detail' employee.emp_id %}"
                                                   class="btn btn-outline-primary btn-sm flex-fill" title="عرض التفاصيل">
                                                    <i class="fas fa-eye me-1"></i>عرض
                                                </a>
                                                {% if perms.Hr.change_employee or user|is_admin %}
                                                <a href="{% url 'Hr:employees:edit' employee.emp_id %}"
                                                   class="btn btn-primary btn-sm flex-fill" title="تعديل">
                                                    <i class="fas fa-edit me-1"></i>تعديل
                                                </a>
                                                {% endif %}
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-outline-secondary btn-sm dropdown-toggle"
                                                            data-bs-toggle="dropdown" aria-expanded="false" title="المزيد">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                    <ul class="dropdown-menu dropdown-menu-end">
                                                        <li><a class="dropdown-item" href="{% url 'Hr:employees:detail' employee.emp_id %}">
                                                            <i class="fas fa-id-card me-2 text-primary"></i>البطاقة الشخصية
                                                        </a></li>
                                                        <li><a class="dropdown-item" href="#">
                                                            <i class="fas fa-print me-2 text-secondary"></i>طباعة البيانات
                                                        </a></li>
                                                        {% if perms.Hr.delete_employee or user|is_admin %}
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li><button class="dropdown-item text-danger delete-employee"
                                                                    data-employee-id="{{ employee.emp_id }}"
                                                                    data-employee-name="{{ employee.emp_full_name }}">
                                                            <i class="fas fa-trash me-2"></i>حذف
                                                        </button></li>
                                                        {% endif %}
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- Enhanced Table View -->
                <div id="tableView" class="table-responsive">
                    <table class="table table-hover align-middle mb-0 modern-table" id="employeesTable">
                        <thead class="table-light sticky-top modern-table-header">
                            <tr>
                                <th class="py-4 px-4 sortable border-0" data-sort="emp_id">
                                    <div class="d-flex align-items-center fw-semibold">
                                        <i class="fas fa-hashtag me-2 text-primary"></i>
                                        الرقم
                                        <i class="fas fa-sort ms-2 text-muted"></i>
                                    </div>
                                </th>
                                <th class="py-4 px-4 sortable border-0" data-sort="emp_full_name">
                                    <div class="d-flex align-items-center fw-semibold">
                                        <i class="fas fa-user me-2 text-primary"></i>
                                        الموظف
                                        <i class="fas fa-sort ms-2 text-muted"></i>
                                    </div>
                                </th>
                                <th class="py-4 px-4 sortable border-0" data-sort="department">
                                    <div class="d-flex align-items-center fw-semibold">
                                        <i class="fas fa-building me-2 text-primary"></i>
                                        القسم
                                        <i class="fas fa-sort ms-2 text-muted"></i>
                                    </div>
                                </th>
                                <th class="py-4 px-4 border-0">
                                    <div class="d-flex align-items-center fw-semibold">
                                        <i class="fas fa-briefcase me-2 text-primary"></i>
                                        الوظيفة
                                    </div>
                                </th>
                                <th class="py-4 px-4 border-0">
                                    <div class="d-flex align-items-center fw-semibold">
                                        <i class="fas fa-phone me-2 text-primary"></i>
                                        الهاتف
                                    </div>
                                </th>
                                <th class="py-4 px-4 sortable border-0" data-sort="working_condition">
                                    <div class="d-flex align-items-center fw-semibold">
                                        <i class="fas fa-user-check me-2 text-primary"></i>
                                        الحالة
                                        <i class="fas fa-sort ms-2 text-muted"></i>
                                    </div>
                                </th>
                                <th class="py-4 px-4 text-center border-0">
                                    <div class="d-flex align-items-center justify-content-center fw-semibold">
                                        <i class="fas fa-cogs me-2 text-primary"></i>
                                        العمليات
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody class="modern-table-body">
                            {% for employee in employees %}
                            <tr class="employee-row modern-table-row border-0" data-emp-id="{{ employee.emp_id }}"
                                data-emp-name="{{ employee.emp_full_name|default:employee.emp_first_name }}"
                                data-dept="{{ employee.department.dept_name|default:'' }}"
                                data-condition="{{ employee.working_condition|default:'' }}">

                                <!-- Employee ID -->
                                <td class="px-4 py-3 border-0">
                                    <span class="badge bg-primary-subtle text-primary border border-primary-subtle px-3 py-2 fw-bold">
                                        #{{ employee.emp_id }}
                                    </span>
                                </td>

                                <!-- Employee Info -->
                                <td class="px-4 py-3 border-0">
                                    <div class="d-flex align-items-center">
                                        <div class="employee-avatar me-3">
                                            {% if employee.emp_image %}
                                            <img src="{{ employee.emp_image|binary_to_img }}" alt="{{ employee.emp_full_name }}"
                                                 class="rounded-circle object-fit-cover employee-table-img shadow-sm" width="50" height="50">
                                            {% else %}
                                            <div class="avatar bg-gradient-primary text-white rounded-circle d-flex align-items-center justify-content-center shadow-sm"
                                                 style="width: 50px; height: 50px; font-size: 1.2rem; font-weight: bold;">
                                                {{ employee.emp_first_name|slice:":1"|upper }}
                                            </div>
                                            {% endif %}
                                        </div>
                                        <div class="employee-details">
                                            <div class="employee-name-wrapper">
                                                <a href="{% url 'Hr:employees:detail' employee.emp_id %}"
                                                   class="fw-semibold text-decoration-none text-dark employee-name d-block mb-1 hover-primary">
                                                    {{ employee.emp_full_name|default:employee.emp_first_name }}
                                                </a>
                                            </div>
                                            {% if employee.national_id %}
                                            <div class="employee-meta">
                                                <small class="text-muted d-flex align-items-center">
                                                    <i class="fas fa-id-card me-1"></i>
                                                    {{ employee.national_id }}
                                                </small>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>

                                <!-- Department -->
                                <td class="px-4 py-3 border-0">
                                    {% if employee.department %}
                                    <div class="department-info">
                                        <span class="badge bg-info-subtle text-info border border-info-subtle px-2 py-1">
                                            <i class="fas fa-building me-1"></i>
                                            {{ employee.department.dept_name }}
                                        </span>
                                    </div>
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>

                                <!-- Job -->
                                <td class="px-4 py-3 border-0">
                                    {% if employee.jop_name %}
                                    <div class="job-info">
                                        <span class="fw-medium text-dark">{{ employee.jop_name }}</span>
                                    </div>
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>

                                <!-- Phone -->
                                <td class="px-4 py-3 border-0">
                                    {% if employee.emp_phone1 %}
                                    <a href="tel:{{ employee.emp_phone1 }}" class="text-decoration-none text-dark phone-link">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-phone text-success me-2"></i>
                                            <span class="fw-medium">{{ employee.emp_phone1 }}</span>
                                        </div>
                                    </a>
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <!-- Status -->
                                <td class="px-4 py-3 border-0">
                                    <div class="status-wrapper">
                                        {% if employee.working_condition == 'سارى' %}
                                        <span class="badge bg-success text-white px-3 py-2 rounded-pill">
                                            <i class="fas fa-check-circle me-1"></i>نشط
                                        </span>
                                        {% elif employee.working_condition == 'منقطع عن العمل' %}
                                        <span class="badge bg-warning text-white px-3 py-2 rounded-pill">
                                            <i class="fas fa-pause-circle me-1"></i>منقطع
                                        </span>
                                        {% elif employee.working_condition == 'استقالة' %}
                                        <span class="badge bg-danger text-white px-3 py-2 rounded-pill">
                                            <i class="fas fa-times-circle me-1"></i>استقالة
                                        </span>
                                        {% else %}
                                        <span class="badge bg-secondary text-white px-3 py-2 rounded-pill">
                                            <i class="fas fa-question-circle me-1"></i>{{ employee.working_condition|default:"غير محدد" }}
                                        </span>
                                        {% endif %}
                                    </div>
                                </td>

                                <!-- Actions -->
                                <td class="px-4 py-3 text-center border-0">
                                    <div class="action-buttons-wrapper">
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'Hr:employees:detail' employee.emp_id %}"
                                               class="btn btn-outline-primary btn-sm modern-action-btn" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if perms.Hr.change_employee or user|is_admin %}
                                            <a href="{% url 'Hr:employees:edit' employee.emp_id %}"
                                               class="btn btn-primary btn-sm modern-action-btn" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% endif %}
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-outline-secondary btn-sm dropdown-toggle modern-action-btn"
                                                        data-bs-toggle="dropdown" aria-expanded="false" title="المزيد">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-end shadow">
                                                    <li><h6 class="dropdown-header">إجراءات الموظف</h6></li>
                                                    <li><a class="dropdown-item" href="{% url 'Hr:employees:detail' employee.emp_id %}">
                                                        <i class="fas fa-id-card me-2 text-primary"></i>البطاقة الشخصية
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="#">
                                                        <i class="fas fa-print me-2 text-info"></i>طباعة البيانات
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="#">
                                                        <i class="fas fa-file-export me-2 text-success"></i>تصدير البيانات
                                                    </a></li>
                                                    {% if perms.Hr.delete_employee or user|is_admin %}
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><button class="dropdown-item text-danger delete-employee"
                                                                data-employee-id="{{ employee.emp_id }}"
                                                                data-employee-name="{{ employee.emp_full_name }}">
                                                        <i class="fas fa-trash me-2"></i>حذف الموظف
                                                    </button></li>
                                                    {% endif %}
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x mb-3 text-muted"></i>
                    <p class="mb-0">لا يوجد موظفين بالمعايير المحددة.</p>
                </div>
                {% endif %}
            </div>
        </div>

        {% if employees_by_department %}
        <div class="card shadow-sm mt-4">
            <div class="card-header bg-light py-3 border-bottom">
                <h5 class="mb-0 fw-semibold">
                    <i class="fas fa-chart-pie me-2 text-primary"></i>
                    توزيع الموظفين حسب الأقسام
                </h5>
            </div>
            <div class="card-body p-4">
                <div class="row g-3">
                    {% for dept in employees_by_department %}
                    <div class="col-md-6 col-lg-4">
                        <div class="border rounded p-3 h-100 d-flex flex-column">
                             <h6 class="card-title mb-1">{{ dept.dept_name }}</h6>
                             <div class="d-flex justify-content-between align-items-center mb-1">
                                 <span class="fw-bold fs-5 text-primary">{{ dept.count }}</span>
                                 {% if dept.dept_code %}
                                 <a href="{% url 'Hr:departments:detail' dept.dept_code %}" class="btn btn-sm btn-link p-0 text-decoration-none">عرض</a>
                                 {% endif %}
                             </div>
                             <div class="progress mt-auto" style="height: 6px;">
                                 <div class="progress-bar bg-primary" role="progressbar"
                                      style="width: {% widthratio dept.count total_employees 100 %}%;"
                                      aria-valuenow="{% widthratio dept.count total_employees 100 %}"
                                      aria-valuemin="0" aria-valuemax="100">
                                 </div>
                             </div>
                             <small class="text-muted mt-1">{% widthratio dept.count total_employees 100 %}% من الإجمالي</small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light py-3 border-bottom">
                <h5 class="mb-0 fw-semibold">روابط سريعة</h5>
            </div>
            <div class="card-body p-4">
                <div class="row g-3">
                    <div class="col-xl-3 col-md-6">
                        <a href="{% url 'Hr:employees:list' %}" class="card action-card h-100 text-decoration-none shadow-hover">
                            <div class="card-body d-flex align-items-center p-3">
                                <div class="action-icon bg-primary-subtle text-primary rounded-circle me-3 flex-shrink-0">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1 text-dark">قائمة الموظفين</h6>
                                    <p class="text-muted small mb-0">عرض وإدارة بيانات الموظفين</p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-xl-3 col-md-6">
                        <a href="{% url 'Hr:employees:create' %}" class="card action-card h-100 text-decoration-none shadow-hover">
                            <div class="card-body d-flex align-items-center p-3">
                                <div class="action-icon bg-success-subtle text-success rounded-circle me-3 flex-shrink-0">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1 text-dark">إضافة موظف جديد</h6>
                                    <p class="text-muted small mb-0">تسجيل بيانات موظف جديد</p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-xl-3 col-md-6">
                        <a href="{% url 'Hr:departments:list' %}" class="card action-card h-100 text-decoration-none shadow-hover">
                            <div class="card-body d-flex align-items-center p-3">
                                <div class="action-icon bg-info-subtle text-info rounded-circle me-3 flex-shrink-0">
                                    <i class="fas fa-building"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1 text-dark">الأقسام</h6>
                                    <p class="text-muted small mb-0">عرض وإدارة الأقسام</p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-xl-3 col-md-6">
                        <a href="{% url 'Hr:jobs:list' %}" class="card action-card h-100 text-decoration-none shadow-hover">
                            <div class="card-body d-flex align-items-center p-3">
                                <div class="action-icon bg-warning-subtle text-warning rounded-circle me-3 flex-shrink-0">
                                    <i class="fas fa-briefcase"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1 text-dark">الوظائف</h6>
                                    <p class="text-muted small mb-0">عرض وإدارة المسميات الوظيفية</p>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إضافة حاوية للإشعارات -->
<div class="toast-container position-fixed top-0 end-0 p-3">
    <div id="successToast" class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas fa-check-circle me-2"></i>
                <span class="toast-message"></span>
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    </div>
</div>

<!-- إضافة مؤشر التحميل -->
<div id="loadingIndicator" class="position-fixed top-0 start-0 end-0" style="display: none;">
    <div class="progress" style="height: 3px;">
        <div class="progress-bar progress-bar-striped progress-bar-animated bg-primary" role="progressbar" style="width: 100%"></div>
    </div>
</div>

<!-- قالب الإجراءات السريعة -->
<div class="position-fixed bottom-0 end-0 m-3">
    <div class="btn-group-vertical">
        <button id="quickActionsBtn" type="button" class="btn btn-primary rounded-circle mb-2" style="width: 50px; height: 50px;">
            <i class="fas fa-bolt"></i>
        </button>
    </div>
</div>

<!-- قائمة الإجراءات السريعة -->
<div class="quick-actions-menu position-fixed end-0 bottom-0 mb-5 me-3 bg-white rounded-3 shadow-lg p-3" style="display: none; min-width: 250px;">
    <h6 class="text-muted mb-3">إجراءات سريعة</h6>
    <div class="list-group list-group-flush">
        <a href="{% url 'Hr:employees:create' %}" class="list-group-item list-group-item-action">
            <i class="fas fa-user-plus text-primary me-2"></i>إضافة موظف جديد
        </a>
        <button class="list-group-item list-group-item-action" onclick="exportToExcel()">
            <i class="fas fa-file-excel text-success me-2"></i>تصدير إلى Excel
        </button>
        <button class="list-group-item list-group-item-action" onclick="printEmployeeList()">
            <i class="fas fa-print text-info me-2"></i>طباعة القائمة
        </button>
        {% comment %} Quick action for advanced search removed {% endcomment %}
    </div>
</div>

<!-- Keyboard Shortcuts Guide -->
<div class="modal fade" id="keyboardShortcutsModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title">
                    <i class="fas fa-keyboard me-2 text-primary"></i>
                    اختصارات لوحة المفاتيح
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row g-3">
                    <div class="col-6">
                        <div class="d-flex align-items-center">
                            <kbd class="me-2">Ctrl</kbd> + <kbd class="mx-2">F</kbd>
                            <span class="text-muted">بحث سريع</span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="d-flex align-items-center">
                            <kbd class="me-2">Ctrl</kbd> + <kbd class="mx-2">N</kbd>
                            <span class="text-muted">موظف جديد</span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="d-flex align-items-center">
                            <kbd class="me-2">Ctrl</kbd> + <kbd class="mx-2">P</kbd>
                            <span class="text-muted">طباعة القائمة</span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="d-flex align-items-center">
                            <kbd class="me-2">Ctrl</kbd> + <kbd class="mx-2">E</kbd>
                            <span class="text-muted">تصدير البيانات</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock content %}
{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/employee_list.css' %}">
<style>
    /* Employee List Specific Text Visibility Fixes */

    /* Page Header Fixes */
    .modern-page-header .page-title,
    .modern-page-header h1 {
        color: var(--text-primary) !important;
    }

    .modern-page-header .page-subtitle {
        color: var(--text-secondary) !important;
    }

    /* Stats Cards Fixes */
    .ultra-modern-stats-card .stats-content .stats-number,
    .ultra-modern-stats-card .stats-content .stats-title,
    .ultra-modern-stats-card .stats-content small {
        color: var(--text-inverse) !important;
    }

    /* Toggle Card Fixes */
    .ultra-modern-toggle-card .toggle-content h5 {
        color: var(--text-primary) !important;
    }

    .ultra-modern-toggle-card .toggle-content p {
        color: var(--text-secondary) !important;
    }

    .ultra-modern-toggle-card .stats-mini-card .fw-bold {
        color: inherit !important;
    }

    .ultra-modern-toggle-card .stats-mini-card small {
        color: var(--text-secondary) !important;
    }

    /* Filter Card Fixes */
    .ultra-modern-filter-card .card-header {
        background: var(--primary-gradient) !important;
        color: var(--text-inverse) !important;
    }

    .ultra-modern-filter-card .card-header h5,
    .ultra-modern-filter-card .card-header p {
        color: var(--text-inverse) !important;
    }

    .ultra-modern-filter-card .filter-stats {
        color: rgba(255, 255, 255, 0.8) !important;
    }

    /* Filter Form Fixes */
    .filter-group-title {
        color: var(--text-primary) !important;
    }

    .form-label {
        color: var(--text-primary) !important;
    }

    .input-group-text {
        background-color: var(--gray-100) !important;
        color: var(--text-secondary) !important;
        border-color: var(--gray-300) !important;
    }

    /* Active Filters Fixes */
    .active-filters {
        background-color: var(--gray-100) !important;
        border: 1px solid var(--gray-200) !important;
    }

    .active-filters .fw-medium {
        color: var(--primary-color) !important;
    }

    /* Employee List Title Fixes */
    #employeeListTitle {
        color: var(--text-primary) !important;
    }

    /* Result Count Fixes */
    .result-count {
        color: var(--text-secondary) !important;
    }

    /* Quick Actions Menu Fixes */
    .quick-actions-menu {
        background-color: var(--bg-primary) !important;
        border: 1px solid var(--gray-200) !important;
    }

    .quick-actions-menu h6 {
        color: var(--text-secondary) !important;
    }

    .quick-actions-menu .list-group-item {
        background-color: transparent !important;
        color: var(--text-primary) !important;
        border-color: var(--gray-200) !important;
    }

    .quick-actions-menu .list-group-item:hover {
        background-color: var(--gray-100) !important;
        color: var(--text-primary) !important;
    }

    /* Modal Fixes */
    .modal-content {
        background-color: var(--bg-primary) !important;
        color: var(--text-primary) !important;
    }

    .modal-header,
    .modal-body {
        color: var(--text-primary) !important;
    }

    .modal-title {
        color: var(--text-primary) !important;
    }

    /* Keyboard Shortcuts Modal Fixes */
    #keyboardShortcutsModal .text-muted {
        color: var(--text-secondary) !important;
    }

    #keyboardShortcutsModal kbd {
        background-color: var(--gray-200) !important;
        color: var(--text-primary) !important;
        border: 1px solid var(--gray-300) !important;
    }

    /* Toast Fixes */
    .toast {
        background-color: var(--bg-primary) !important;
        color: var(--text-primary) !important;
    }

    .toast.bg-success {
        background-color: var(--success-color) !important;
        color: var(--text-inverse) !important;
    }

    .toast-body {
        color: inherit !important;
    }

    /* Search Results Dropdown Fixes */
    .search-results-dropdown {
        background-color: var(--bg-primary) !important;
        border: 1px solid var(--gray-200) !important;
        color: var(--text-primary) !important;
    }

    /* Enhanced contrast for better readability */
    .text-success {
        color: var(--success-dark) !important;
    }

    .text-danger {
        color: var(--danger-dark) !important;
    }

    .text-warning {
        color: var(--warning-dark) !important;
    }

    .text-info {
        color: var(--info-dark) !important;
    }

    .text-primary {
        color: var(--primary-color) !important;
    }

    /* Ensure all card text is visible */
    .card .text-dark,
    .card h1, .card h2, .card h3, .card h4, .card h5, .card h6 {
        color: var(--text-primary) !important;
    }

    .card .text-muted {
        color: var(--text-secondary) !important;
    }

    /* Dynamic filtering transitions */
    .stats-section {
        transition: all 0.3s ease;
    }

    .stats-card {
        transition: all 0.3s ease;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
    }

    .ultra-modern-toggle-card {
        transition: all 0.3s ease;
    }

    .employee-list-container {
        transition: opacity 0.3s ease;
    }

    .content-loading {
        opacity: 0.6;
        pointer-events: none;
    }

    /* Loading overlay */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        backdrop-filter: blur(2px);
    }

    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #007bff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Enhanced hover effects */
    .modern-btn {
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .modern-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0,123,255,0.3);
    }

    .modern-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .modern-btn:hover::before {
        left: 100%;
    }

    /* Counter animation */
    .counter-animate {
        transition: all 0.3s ease;
    }

    /* Status indicator animations */
    .status-indicator {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
    }
</style>
{% endblock extra_css %}

{% block extra_js %}
<script>
    // Define Django URLs and CSRF token for use in external JS
    const employeeDetailUrlTemplate = "{% url 'Hr:employees:detail' 0 %}";
    const employeeListUrl = "{% url 'Hr:employees:list' %}";
    const employeeListAjaxUrl = "{% url 'Hr:employees:list_ajax' %}";
    const newEmployeeUrl = "{% url 'Hr:employees:create' %}";
    const exportUrlTemplate = "{% url 'Hr:employees:export' %}";
    const csrfToken = "{{ csrf_token }}";
    const employeeSearchUrlTemplate = "{% url 'Hr:employees:employee_search' %}?q=QUERY_PLACEHOLDER"; // Use a placeholder for query
    const emp_id_placeholder_for_url = '0'; // Or a more unique placeholder if '0' might appear in other parts of the URL
</script>
<script src="{% static 'js/employee_list.js' %}"></script>
{% endblock extra_js %}
