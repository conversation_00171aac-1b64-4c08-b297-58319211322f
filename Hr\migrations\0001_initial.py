# Generated by Django 5.0.14 on 2025-05-07 05:28

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AttendanceMachine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الماكينة')),
                ('ip_address', models.CharField(max_length=15, verbose_name='عنوان IP')),
                ('port', models.PositiveIntegerField(default=4370, verbose_name='المنفذ')),
                ('machine_type', models.CharField(choices=[('in', 'حضور'), ('out', 'انصراف'), ('both', 'حضور وانصراف')], max_length=10, verbose_name='نوع الماكينة')),
                ('location', models.CharField(blank=True, max_length=100, null=True, verbose_name='الموقع')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'ماكينة الحضور',
                'verbose_name_plural': 'ماكينات الحضور',
                'db_table': 'Hr_AttendanceMachine',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='AttendanceRule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم القاعدة')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف')),
                ('work_schedule', models.JSONField(verbose_name='جدول العمل')),
                ('late_grace_minutes', models.PositiveIntegerField(default=0, verbose_name='فترة سماح التأخير (دقائق)')),
                ('early_leave_grace_minutes', models.PositiveIntegerField(default=0, verbose_name='فترة سماح الانصراف المبكر (دقائق)')),
                ('weekly_off_days', models.JSONField(default=list, verbose_name='أيام الإجازة الأسبوعية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'قاعدة الحضور',
                'verbose_name_plural': 'قواعد الحضور',
                'db_table': 'Hr_AttendanceRule',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='Car',
            fields=[
                ('car_id', models.IntegerField(primary_key=True, serialize=False, verbose_name='رقم السيارة')),
                ('car_name', models.CharField(blank=True, max_length=50, null=True, verbose_name='اسم السيارة')),
                ('car_type', models.CharField(blank=True, max_length=50, null=True, verbose_name='نوع السيارة')),
                ('car_salary', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='تكلفة السيارة')),
                ('car_salary_farda', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='تكلفة السيارة (فردة)')),
                ('supplier', models.CharField(blank=True, max_length=50, null=True, verbose_name='المورد')),
                ('contract_type', models.CharField(blank=True, max_length=50, null=True, verbose_name='نوع العقد')),
                ('car_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم السيارة')),
                ('car_license_expiration_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ انتهاء رخصة السيارة')),
                ('driver_name', models.CharField(blank=True, max_length=50, null=True, verbose_name='اسم السائق')),
                ('driver_phone', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم هاتف السائق')),
                ('driver_license_expiration_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ انتهاء رخصة السائق')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('shift_type', models.CharField(blank=True, choices=[('حضور فقط', 'حضور فقط'), ('انصراف فقط', 'انصراف فقط')], max_length=50, null=True, verbose_name='نوع الوردية')),
                ('contract_type_farada', models.CharField(blank=True, max_length=50, null=True, verbose_name='نوع العقد (فردة)')),
            ],
            options={
                'verbose_name': 'السيارة',
                'verbose_name_plural': 'السيارات',
                'db_table': 'Tbl_Car',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='Department',
            fields=[
                ('dept_code', models.IntegerField(primary_key=True, serialize=False, verbose_name='رمز القسم')),
                ('dept_name', models.CharField(max_length=250, verbose_name='اسم القسم')),
                ('manager_id', models.IntegerField(blank=True, null=True, verbose_name='كود مدير القسم')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('note', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
            ],
            options={
                'verbose_name': 'القسم',
                'verbose_name_plural': 'الأقسام',
                'db_table': 'Tbl_Department',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='EmployeeFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100, verbose_name='العنوان')),
                ('file_type', models.CharField(choices=[('id_card', 'بطاقة الهوية'), ('contract', 'عقد العمل'), ('cv', 'السيرة الذاتية'), ('certificate', 'شهادة'), ('medical', 'تقرير طبي'), ('other', 'أخرى')], default='other', max_length=20, verbose_name='نوع الملف')),
                ('file', models.FileField(upload_to='employee_files/%Y/%m/', verbose_name='الملف')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'ملف الموظف',
                'verbose_name_plural': 'ملفات الموظفين',
                'ordering': ['-created_at'],
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='EmployeeLeave',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(verbose_name='تاريخ النهاية')),
                ('days_count', models.PositiveIntegerField(verbose_name='عدد الأيام')),
                ('reason', models.TextField(verbose_name='السبب')),
                ('status', models.CharField(choices=[('pending', 'قيد الانتظار'), ('approved', 'موافق عليه'), ('rejected', 'مرفوض'), ('cancelled', 'ملغى')], default='pending', max_length=20, verbose_name='الحالة')),
                ('approval_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الموافقة')),
                ('rejection_reason', models.TextField(blank=True, null=True, verbose_name='سبب الرفض')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'إجازة الموظف',
                'verbose_name_plural': 'إجازات الموظفين',
                'db_table': 'Hr_EmployeeLeave',
                'ordering': ['-start_date'],
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='EmployeeNote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100, verbose_name='العنوان')),
                ('content', models.TextField(verbose_name='المحتوى')),
                ('is_important', models.BooleanField(default=False, verbose_name='مهم')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'ملاحظة الموظف',
                'verbose_name_plural': 'ملاحظات الموظفين',
                'ordering': ['-created_at'],
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='EmployeeSalaryItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('value', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='القيمة')),
                ('effective_date', models.DateField(verbose_name='تاريخ السريان')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'بند راتب الموظف',
                'verbose_name_plural': 'بنود رواتب الموظفين',
                'db_table': 'Hr_EmployeeSalaryItem',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='EmployeeTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان المهمة')),
                ('description', models.TextField(verbose_name='وصف المهمة')),
                ('status', models.CharField(choices=[('pending', 'قيد الانتظار'), ('in_progress', 'قيد التنفيذ'), ('completed', 'مكتملة'), ('cancelled', 'ملغاة')], default='pending', max_length=20, verbose_name='الحالة')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('medium', 'متوسطة'), ('high', 'عالية'), ('urgent', 'عاجلة')], default='medium', max_length=20, verbose_name='الأولوية')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('due_date', models.DateField(verbose_name='تاريخ الاستحقاق')),
                ('completion_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإنجاز')),
                ('progress', models.PositiveIntegerField(default=0, help_text='من 0 إلى 100', verbose_name='نسبة الإنجاز (%)')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'مهمة الموظف',
                'verbose_name_plural': 'مهام الموظفين',
                'ordering': ['-due_date', 'priority'],
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='HrTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان المهمة')),
                ('description', models.TextField(verbose_name='وصف المهمة')),
                ('task_type', models.CharField(choices=[('insurance', 'متابعة التأمينات'), ('transportation', 'متابعة بدل المواصلات'), ('car_issues', 'مشاكل سيارات النقل'), ('contract_renewal', 'تجديد العقود'), ('other', 'أخرى')], default='other', max_length=20, verbose_name='نوع المهمة')),
                ('status', models.CharField(choices=[('pending', 'قيد الانتظار'), ('in_progress', 'قيد التنفيذ'), ('completed', 'مكتملة'), ('cancelled', 'ملغاة')], default='pending', max_length=20, verbose_name='الحالة')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('medium', 'متوسطة'), ('high', 'عالية'), ('urgent', 'عاجلة')], default='medium', max_length=20, verbose_name='الأولوية')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('due_date', models.DateField(verbose_name='تاريخ الاستحقاق')),
                ('completion_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإنجاز')),
                ('progress', models.PositiveIntegerField(default=0, help_text='من 0 إلى 100', verbose_name='نسبة الإنجاز (%)')),
                ('steps_taken', models.TextField(blank=True, null=True, verbose_name='الخطوات المتخذة')),
                ('reminder_days', models.PositiveIntegerField(default=3, verbose_name='أيام التذكير قبل الموعد')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'مهمة الموارد البشرية',
                'verbose_name_plural': 'مهام الموارد البشرية',
                'ordering': ['-due_date', 'priority'],
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='Job',
            fields=[
                ('jop_code', models.IntegerField(db_column='Jop_Code', primary_key=True, serialize=False, verbose_name='رمز الوظيفة')),
                ('jop_name', models.CharField(db_column='Jop_Name', max_length=50, verbose_name='اسم الوظيفة')),
            ],
            options={
                'verbose_name': 'الوظيفة',
                'verbose_name_plural': 'الوظائف',
                'db_table': 'Tbl_Jop',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='JobInsurance',
            fields=[
                ('job_code_insurance', models.IntegerField(db_column='job_code_insurance', primary_key=True, serialize=False, verbose_name='رمز وظيفة التأمين')),
                ('job_name_insurance', models.CharField(db_collation='Arabic_CI_AS', db_column='job_name_insurance', max_length=200, verbose_name='اسم وظيفة التأمين')),
            ],
            options={
                'verbose_name': 'وظيفة التأمين',
                'verbose_name_plural': 'وظائف التأمين',
                'db_table': 'Tbl_Jop_Name_insurance',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='LeaveType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='الاسم')),
                ('affects_salary', models.BooleanField(default=False, verbose_name='يؤثر على الراتب')),
                ('is_paid', models.BooleanField(default=True, verbose_name='مدفوع الأجر')),
                ('max_days_per_year', models.PositiveIntegerField(default=0, help_text='0 يعني غير محدود', verbose_name='الحد الأقصى للأيام في السنة')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'نوع الإجازة',
                'verbose_name_plural': 'أنواع الإجازات',
                'db_table': 'Hr_LeaveType',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='OfficialHoliday',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الإجازة')),
                ('date', models.DateField(verbose_name='تاريخ الإجازة')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'إجازة رسمية',
                'verbose_name_plural': 'إجازات رسمية',
                'db_table': 'Hr_OfficialHoliday',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='PayrollEntry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('basic_salary', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الراتب الأساسي')),
                ('variable_salary', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الراتب المتغير')),
                ('allowances', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الاستحقاقات')),
                ('deductions', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الاستقطاعات')),
                ('overtime', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='العمل الإضافي')),
                ('penalties', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الجزاءات')),
                ('total_salary', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='إجمالي الراتب')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'سجل الراتب',
                'verbose_name_plural': 'سجلات الرواتب',
                'db_table': 'Hr_PayrollEntry',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='PayrollItemDetail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ')),
            ],
            options={
                'verbose_name': 'تفاصيل بند الراتب',
                'verbose_name_plural': 'تفاصيل بنود الرواتب',
                'db_table': 'Hr_PayrollItemDetail',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='PayrollPeriod',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الفترة')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(verbose_name='تاريخ النهاية')),
                ('status', models.CharField(choices=[('draft', 'قيد الإعداد'), ('calculated', 'تم الاحتساب'), ('approved', 'تم الاعتماد'), ('paid', 'تم الدفع')], default='draft', max_length=20, verbose_name='الحالة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'فترة الراتب',
                'verbose_name_plural': 'فترات الرواتب',
                'db_table': 'Hr_PayrollPeriod',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='PickupPoint',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم النقطة')),
                ('address', models.CharField(max_length=255, verbose_name='العنوان')),
                ('coordinates', models.CharField(blank=True, max_length=100, null=True, verbose_name='الإحداثيات')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'نقطة تجمع',
                'verbose_name_plural': 'نقاط التجمع',
                'db_table': 'Hr_PickupPoint',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='SalaryItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم البند')),
                ('item_type', models.CharField(choices=[('fixed', 'ثابت'), ('variable', 'متغير'), ('allowance', 'استحقاق'), ('deduction', 'استقطاع')], max_length=20, verbose_name='نوع البند')),
                ('calculation_method', models.CharField(choices=[('fixed_amount', 'مبلغ ثابت'), ('percentage', 'نسبة مئوية')], max_length=20, verbose_name='طريقة الحساب')),
                ('affects_total', models.BooleanField(default=True, verbose_name='يؤثر على الإجمالي')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'بند الراتب',
                'verbose_name_plural': 'بنود الرواتب',
                'db_table': 'Hr_SalaryItem',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='TaskStep',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('task_id', models.IntegerField(verbose_name='رقم المهمة')),
                ('description', models.TextField(verbose_name='وصف الخطوة')),
                ('completed', models.BooleanField(default=False, verbose_name='مكتملة')),
                ('completion_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإنجاز')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'خطوة المهمة',
                'verbose_name_plural': 'خطوات المهام',
                'ordering': ['-created_at'],
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='Employee',
            fields=[
                ('emp_id', models.IntegerField(db_column='Emp_ID', primary_key=True, serialize=False, verbose_name='رقم الموظف')),
                ('emp_first_name', models.CharField(blank=True, db_column='Emp_First_Name', max_length=50, null=True, verbose_name='الاسم الأول')),
                ('emp_second_name', models.CharField(blank=True, db_column='Emp_Second_Name', max_length=50, null=True, verbose_name='الاسم الثاني')),
                ('emp_full_name', models.CharField(blank=True, db_column='Emp_Full_Name', max_length=100, null=True, verbose_name='الاسم الكامل')),
                ('emp_name_english', models.CharField(blank=True, db_column='Emp_Name_English', max_length=50, null=True, verbose_name='الاسم بالإنجليزية')),
                ('emp_type', models.CharField(blank=True, choices=[('ذكر', 'ذكر'), ('انثى', 'انثى')], db_column='Emp_Type', max_length=50, null=True, verbose_name='نوع الموظف')),
                ('mother_name', models.CharField(blank=True, db_column='Mother_Name', max_length=50, null=True, verbose_name='اسم الأم')),
                ('emp_phone1', models.CharField(blank=True, db_column='Emp_Phone1', max_length=50, null=True, verbose_name='رقم الهاتف 1')),
                ('emp_phone2', models.CharField(blank=True, db_column='Emp_Phone2', max_length=50, null=True, verbose_name='رقم الهاتف 2')),
                ('emp_address', models.CharField(blank=True, db_column='Emp_Address', max_length=200, null=True, verbose_name='العنوان')),
                ('governorate', models.CharField(blank=True, db_column='Governorate', max_length=50, null=True, verbose_name='المحافظة')),
                ('emp_marital_status', models.CharField(blank=True, choices=[('أعزب', 'أعزب'), ('متزوج', 'متزوج'), ('مطلق', 'مطلق'), ('أرمل', 'أرمل')], db_column='Emp_Marital_Status', max_length=50, null=True, verbose_name='الحالة الاجتماعية')),
                ('emp_nationality', models.CharField(blank=True, db_column='Emp_Nationality', max_length=50, null=True, verbose_name='الجنسية')),
                ('people_with_special_needs', models.BooleanField(blank=True, db_column='People_With_Special_Needs', null=True, verbose_name='ذوي الاحتياجات الخاصة')),
                ('national_id', models.CharField(blank=True, db_column='National_ID', max_length=14, null=True, verbose_name='الرقم القومي')),
                ('date_birth', models.DateField(blank=True, db_column='Date_Birth', null=True, verbose_name='تاريخ الميلاد')),
                ('age', models.CharField(blank=True, db_column='Age', max_length=100, null=True, verbose_name='العمر')),
                ('place_birth', models.CharField(blank=True, db_column='Place_Birth', max_length=50, null=True, verbose_name='محل الميلاد')),
                ('emp_image', models.BinaryField(blank=True, db_column='Emp_Image', max_length='max', null=True, verbose_name='صورة الموظف')),
                ('personal_id_expiry_date', models.DateField(blank=True, db_column='Personal_ID_Expiry_Date', null=True, verbose_name='تاريخ انتهاء البطاقة الشخصية')),
                ('military_service_certificate', models.CharField(blank=True, choices=[('أدى الخدمة', 'أدى الخدمة'), ('إعفاء', 'إعفاء'), ('مؤجل', 'مؤجل'), ('لم يبلغ السن', 'لم يبلغ السن')], db_column='Military_Service_Certificate', max_length=50, null=True, verbose_name='شهادة الخدمة العسكرية')),
                ('working_condition', models.CharField(blank=True, choices=[('سارى', 'سارى'), ('إجازة', 'إجازة'), ('استقالة', 'استقالة'), ('انقطاع عن العمل', 'انقطاع عن العمل')], db_column='Working_Condition', max_length=50, null=True, verbose_name='حالة العمل')),
                ('dept_name', models.CharField(blank=True, db_column='Dept_Name', max_length=50, null=True, verbose_name='اسم القسم')),
                ('jop_code', models.IntegerField(blank=True, db_column='Jop_Code', null=True, verbose_name='كود الوظيفة')),
                ('jop_name', models.CharField(blank=True, db_column='Jop_Name', max_length=50, null=True, verbose_name='اسم الوظيفة')),
                ('emp_date_hiring', models.DateField(blank=True, db_column='Emp_Date_Hiring', null=True, verbose_name='تاريخ التعيين')),
                ('emp_car', models.CharField(blank=True, db_column='Emp_Car', max_length=50, null=True, verbose_name='السيارة')),
                ('car_ride_time', models.DateTimeField(blank=True, db_column='Car_Ride_Time', null=True, verbose_name='وقت ركوب السيارة')),
                ('car_pick_up_point', models.CharField(blank=True, db_column='Car_Pick_Up_Point', max_length=100, null=True, verbose_name='نقطة التقاط السيارة')),
                ('insurance_status', models.CharField(blank=True, choices=[('مؤمن عليه', 'مؤمن عليه'), ('غير مؤمن عليه', 'غير مؤمن عليه')], db_column='Insurance_Status', max_length=50, null=True, verbose_name='حالة التأمين')),
                ('jop_code_insurance', models.IntegerField(blank=True, db_column='Jop_Code_insurance', null=True, verbose_name='كود وظيفة التأمين')),
                ('jop_name_insurance', models.CharField(blank=True, db_column='Jop_Name_insurance', max_length=50, null=True, verbose_name='اسم وظيفة التأمين')),
                ('insurance_code', models.IntegerField(blank=True, db_column='Insurance_Code', null=True, verbose_name='كود التأمين')),
                ('number_insurance', models.IntegerField(blank=True, db_column='Number_Insurance', null=True, verbose_name='رقم التأمين')),
                ('date_insurance_start', models.DateField(blank=True, db_column='Date_Insurance_Start', null=True, verbose_name='تاريخ بداية التأمين')),
                ('insurance_salary', models.DecimalField(blank=True, db_column='Insurance_Salary', decimal_places=2, max_digits=18, null=True, verbose_name='راتب التأمين')),
                ('percentage_insurance_payable', models.DecimalField(blank=True, db_column='Percentage_Insurance_Payable', decimal_places=4, max_digits=18, null=True, verbose_name='نسبة التأمين المستحق')),
                ('due_insurance_amount', models.DecimalField(blank=True, db_column='Due_Insurance_Amount', decimal_places=2, max_digits=18, null=True, verbose_name='مبلغ التأمين المستحق')),
                ('health_card', models.CharField(blank=True, choices=[('موجودة', 'موجودة'), ('غير موجوده', 'غير موجوده')], db_column='Health_Card', max_length=50, null=True, verbose_name='بطاقة صحية')),
                ('health_card_number', models.IntegerField(blank=True, db_column='Health_Card_Number', null=True, verbose_name='رقم البطاقة الصحية')),
                ('health_card_start_date', models.DateField(blank=True, db_column='Health_Card_Start_Date', null=True, verbose_name='تاريخ بداية البطاقة الصحية')),
                ('health_card_renewal_date', models.DateField(blank=True, db_column='Health_Card_Renewal_Date', null=True, verbose_name='تاريخ تجديد البطاقة الصحية')),
                ('the_health_card_remains_expire', models.IntegerField(blank=True, db_column='The_health_card_remains_expire', null=True, verbose_name='المتبقي لانتهاء البطاقة الصحية')),
                ('health_card_expiration_date', models.DateField(blank=True, db_column='Health_Card_Expiration_Date', null=True, verbose_name='تاريخ انتهاء البطاقة الصحية')),
                ('hiring_date_health_card', models.DateField(blank=True, db_column='Hiring_Date_Health_Card', null=True, verbose_name='تاريخ تعيين البطاقة الصحية')),
                ('form_s1', models.BooleanField(blank=True, db_column='Form_S1', null=True, verbose_name='نموذج S1')),
                ('confirmation_insurance_entry', models.BooleanField(blank=True, db_column='Confirmation_Insurance_Entry', null=True, verbose_name='تأكيد دخول التأمين')),
                ('delivery_date_s1', models.DateField(blank=True, db_column='Delivery_Date_S1', null=True, verbose_name='تاريخ تسليم S1')),
                ('receive_date_s1', models.DateField(blank=True, db_column='Receive_Date_S1', null=True, verbose_name='تاريخ استلام S1')),
                ('form_s6', models.BooleanField(blank=True, db_column='Form_S6', null=True, verbose_name='نموذج S6')),
                ('delivery_date_s6', models.DateField(blank=True, db_column='Delivery_Date_S6', null=True, verbose_name='تاريخ تسليم S6')),
                ('receive_date_s6', models.DateField(blank=True, db_column='Receive_Date_S6', null=True, verbose_name='تاريخ استلام S6')),
                ('entrance_date_s1', models.DateField(blank=True, db_column='Entrance_Date_S1', null=True, verbose_name='تاريخ دخول S1')),
                ('entrance_number_s1', models.IntegerField(blank=True, db_column='Entrance_Number_S1', null=True, verbose_name='رقم دخول S1')),
                ('entrance_date_s6', models.DateField(blank=True, db_column='Entrance_Date_S6', null=True, verbose_name='تاريخ دخول S6')),
                ('entrance_number_s6', models.IntegerField(blank=True, db_column='Entrance_Number_S6', null=True, verbose_name='رقم دخول S6')),
                ('skill_level_measurement_certificate', models.BooleanField(blank=True, db_column='Skill_level_measurement_certificate', null=True, verbose_name='شهادة قياس مستوى المهارة')),
                ('qualification_certificate', models.CharField(blank=True, db_column='Qualification_Certificate', max_length=50, null=True, verbose_name='شهادة المؤهل')),
                ('currentweekshift', models.CharField(blank=True, db_column='CurrentWeekShift', max_length=50, null=True, verbose_name='وردية الأسبوع الحالي')),
                ('nextweekshift', models.CharField(blank=True, db_column='NextWeekShift', max_length=50, null=True, verbose_name='وردية الأسبوع القادم')),
                ('friday_operation', models.CharField(blank=True, db_column='Friday_Operation', max_length=50, null=True, verbose_name='عملية يوم الجمعة')),
                ('shift_type', models.CharField(blank=True, choices=[('صباحي', 'صباحي'), ('مسائي', 'مسائي'), ('ليلي', 'ليلي')], db_column='Shift_Type', max_length=50, null=True, verbose_name='نوع الوردية')),
                ('shift_paper', models.CharField(blank=True, db_column='Shift_paper', max_length=50, null=True, verbose_name='ورقة الشيفت')),
                ('remaining_contract_renewal', models.IntegerField(blank=True, db_column='Remaining_Contract_Renewal', null=True, verbose_name='تجديد العقد المتبقي')),
                ('medical_exam_form_submission', models.BooleanField(blank=True, db_column='Medical_Exam_Form_Submission', null=True, verbose_name='تقديم استمارة الفحص الطبي')),
                ('contract_renewal_date', models.DateField(blank=True, db_column='Contract_Renewal_Date', null=True, verbose_name='تاريخ تجديد العقد')),
                ('contract_renewal_month', models.IntegerField(blank=True, db_column='Contract_Renewal_Month', null=True, verbose_name='شهر تجديد العقد')),
                ('contract_expiry_date', models.DateField(blank=True, db_column='Contract_Expiry_Date', null=True, verbose_name='تاريخ انتهاء العقد')),
                ('end_date_probationary_period', models.DateField(blank=True, db_column='End_date_probationary_period', null=True, verbose_name='تاريخ انتهاء فترة الاختبار')),
                ('years_since_contract_start', models.CharField(blank=True, db_column='Years_Since_Contract_Start', max_length=100, null=True, verbose_name='السنوات منذ بداية العقد')),
                ('birth_certificate', models.BooleanField(blank=True, db_column='Birth_Certificate', null=True, verbose_name='شهادة الميلاد')),
                ('insurance_printout', models.BooleanField(blank=True, db_column='Insurance_Printout', null=True, verbose_name='مطبوعة التأمين')),
                ('id_card_photo', models.BooleanField(blank=True, db_column='ID_Card_Photo', null=True, verbose_name='صورة البطاقة الشخصية')),
                ('personal_photos', models.BooleanField(blank=True, db_column='Personal_Photos', null=True, verbose_name='صور شخصية')),
                ('employment_contract', models.BooleanField(blank=True, db_column='Employment_Contract', null=True, verbose_name='عقد العمل')),
                ('medical_exam_form', models.BooleanField(blank=True, db_column='Medical_Exam_Form', null=True, verbose_name='استمارة الفحص الطبي')),
                ('criminal_record_check', models.BooleanField(blank=True, db_column='Criminal_Record_Check', null=True, verbose_name='فحص السجل الجنائي')),
                ('social_status_report', models.BooleanField(blank=True, db_column='Social_Status_Report', null=True, verbose_name='تقرير الحالة الاجتماعية')),
                ('work_heel', models.BooleanField(blank=True, db_column='Work_Heel', null=True, verbose_name='كعب العمل')),
                ('heel_work_number', models.IntegerField(blank=True, db_column='Heel_Work_Number', null=True, verbose_name='رقم كعب العمل')),
                ('heel_work_registration_date', models.DateField(blank=True, db_column='Heel_Work_Registration_Date', null=True, verbose_name='تاريخ تسجيل كعب العمل')),
                ('heel_work_recipient', models.CharField(blank=True, db_column='Heel_Work_Recipient', max_length=50, null=True, verbose_name='مستلم كعب العمل')),
                ('heel_work_recipient_address', models.CharField(blank=True, db_column='Heel_Work_Recipient_Address', max_length=50, null=True, verbose_name='عنوان مستلم كعب العمل')),
                ('date_resignation', models.DateField(blank=True, db_column='Date_Resignation', null=True, verbose_name='تاريخ الاستقالة')),
                ('reason_resignation', models.CharField(blank=True, db_column='Reason_Resignation', max_length=100, null=True, verbose_name='سبب الاستقالة')),
                ('confirm_exit_insurance', models.BooleanField(blank=True, db_column='Confirm_Exit_Insurance', null=True, verbose_name='تأكيد خروج التأمين')),
                ('department', models.ForeignKey(blank=True, db_column='Dept_Code', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='employees', to='Hr.department', verbose_name='القسم')),
            ],
            options={
                'verbose_name': 'الموظف',
                'verbose_name_plural': 'الموظفون',
                'db_table': 'Tbl_Employee',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='AttendanceSummary',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('status', models.CharField(choices=[('present', 'حاضر'), ('absent', 'غائب'), ('late', 'متأخر'), ('early_leave', 'انصراف مبكر'), ('holiday', 'إجازة'), ('weekend', 'عطلة أسبوعية')], max_length=20, verbose_name='الحالة')),
                ('time_in', models.TimeField(blank=True, null=True, verbose_name='وقت الحضور')),
                ('time_out', models.TimeField(blank=True, null=True, verbose_name='وقت الانصراف')),
                ('late_minutes', models.PositiveIntegerField(default=0, verbose_name='دقائق التأخير')),
                ('early_leave_minutes', models.PositiveIntegerField(default=0, verbose_name='دقائق الانصراف المبكر')),
                ('overtime_minutes', models.PositiveIntegerField(default=0, verbose_name='دقائق العمل الإضافي')),
                ('working_minutes', models.PositiveIntegerField(default=0, verbose_name='دقائق العمل')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendance_summaries', to='Hr.employee', verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'ملخص الحضور',
                'verbose_name_plural': 'ملخصات الحضور',
                'db_table': 'Hr_AttendanceSummary',
                'ordering': ['date'],
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='AttendanceRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('record_date', models.DateField(verbose_name='تاريخ التسجيل')),
                ('record_time', models.TimeField(verbose_name='وقت التسجيل')),
                ('record_type', models.CharField(choices=[('in', 'حضور'), ('out', 'انصراف')], max_length=5, verbose_name='نوع التسجيل')),
                ('source', models.CharField(choices=[('machine', 'ماكينة'), ('manual', 'يدوي')], default='machine', max_length=10, verbose_name='المصدر')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('machine', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='records', to='Hr.attendancemachine', verbose_name='الماكينة')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendance_records', to='Hr.employee', verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'سجل الحضور',
                'verbose_name_plural': 'سجلات الحضور',
                'db_table': 'Hr_AttendanceRecord',
                'ordering': ['record_date', 'record_time'],
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='EmployeeAttendanceRule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('effective_date', models.DateField(verbose_name='تاريخ السريان')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('attendance_rule', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='employees', to='Hr.attendancerule', verbose_name='قاعدة الحضور')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendance_rules', to='Hr.employee', verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'قاعدة حضور الموظف',
                'verbose_name_plural': 'قواعد حضور الموظفين',
                'db_table': 'Hr_EmployeeAttendanceRule',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='EmployeeEvaluation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('evaluation_date', models.DateField(verbose_name='تاريخ التقييم')),
                ('period_start', models.DateField(verbose_name='بداية فترة التقييم')),
                ('period_end', models.DateField(verbose_name='نهاية فترة التقييم')),
                ('performance_rating', models.PositiveSmallIntegerField(choices=[(1, 'ضعيف'), (2, 'مقبول'), (3, 'جيد'), (4, 'جيد جدا'), (5, 'ممتاز')], verbose_name='تقييم الأداء')),
                ('attendance_rating', models.PositiveSmallIntegerField(choices=[(1, 'ضعيف'), (2, 'مقبول'), (3, 'جيد'), (4, 'جيد جدا'), (5, 'ممتاز')], verbose_name='تقييم الحضور')),
                ('teamwork_rating', models.PositiveSmallIntegerField(choices=[(1, 'ضعيف'), (2, 'مقبول'), (3, 'جيد'), (4, 'جيد جدا'), (5, 'ممتاز')], verbose_name='تقييم العمل الجماعي')),
                ('initiative_rating', models.PositiveSmallIntegerField(choices=[(1, 'ضعيف'), (2, 'مقبول'), (3, 'جيد'), (4, 'جيد جدا'), (5, 'ممتاز')], verbose_name='تقييم المبادرة')),
                ('communication_rating', models.PositiveSmallIntegerField(choices=[(1, 'ضعيف'), (2, 'مقبول'), (3, 'جيد'), (4, 'جيد جدا'), (5, 'ممتاز')], verbose_name='تقييم التواصل')),
                ('overall_rating', models.DecimalField(decimal_places=2, max_digits=3, verbose_name='التقييم الإجمالي')),
                ('strengths', models.TextField(verbose_name='نقاط القوة')),
                ('areas_for_improvement', models.TextField(verbose_name='مجالات التحسين')),
                ('goals', models.TextField(verbose_name='الأهداف للفترة القادمة')),
                ('employee_comments', models.TextField(blank=True, null=True, verbose_name='تعليقات الموظف')),
                ('is_acknowledged', models.BooleanField(default=False, verbose_name='تم الاطلاع من قبل الموظف')),
                ('acknowledgement_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاطلاع')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='evaluations', to='Hr.employee', verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'تقييم الموظف',
                'verbose_name_plural': 'تقييمات الموظفين',
                'db_table': 'Hr_EmployeeEvaluation',
                'ordering': ['-evaluation_date'],
                'managed': True,
            },
        ),
    ]
