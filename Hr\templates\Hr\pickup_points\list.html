{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-2 text-gray-800">{{ title }}</h1>
    <p class="mb-4">قائمة بجميع نقاط التجمع المسجلة في النظام.</p>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">نقاط التجمع</h6>
            <a href="{% url 'Hr:pickup_points:create' %}" class="btn btn-sm btn-primary">
                <i class="fas fa-plus"></i> إضافة نقطة تجمع
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>العنوان</th>
                            <th>السيارة</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for point in pickup_points %}
                        <tr>
                            <td>{{ point.name }}</td>
                            <td>{{ point.address }}</td>
                            <td>{{ point.car.car_name|default:point.car.car_id }}</td>
                            <td>
                                {% if point.is_active %}
                                <span class="badge badge-success">نشط</span>
                                {% else %}
                                <span class="badge badge-danger">غير نشط</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{% url 'Hr:pickup_points:detail' point.pk %}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'Hr:pickup_points:edit' point.pk %}" class="btn btn-sm btn-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'Hr:pickup_points:delete' point.pk %}" class="btn btn-sm btn-danger">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="5" class="text-center">لا توجد نقاط تجمع مسجلة.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        $('#dataTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
            }
        });
    });
</script>
{% endblock %}
