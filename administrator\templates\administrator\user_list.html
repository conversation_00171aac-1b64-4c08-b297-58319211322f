{% extends 'administrator/base_admin.html' %}
{% load static %}

{% block title %}المستخدمين - لوحة تحكم مدير النظام{% endblock %}

{% block page_icon %}users{% endblock %}
{% block page_header %}إدارة المستخدمين{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    قائمة المستخدمين
                </h5>
                {% if perms.auth.add_user %}
                <a href="{% url 'administrator:user_add' %}" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus-circle me-2"></i>
                    إضافة مستخدم جديد
                </a>
                {% endif %}
            </div>
            <div class="card-body">
                {% if users %}
                <div class="table-responsive">
                    <table class="table table-hover table-striped align-middle">
                        <thead>
                            <tr>
                                <th scope="col">#</th>
                                <th scope="col">اسم المستخدم</th>
                                <th scope="col">الاسم الكامل</th>
                                <th scope="col">البريد الإلكتروني</th>
                                <th scope="col">حالة النشاط</th>
                                <th scope="col">آخر دخول</th>
                                <th scope="col">المجموعات</th>
                                <th scope="col">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in users %}
                            <tr>
                                <td>{{ forloop.counter }}</td>
                                <td><strong>{{ user.username }}</strong></td>
                                <td>
                                    {% if user.first_name or user.last_name %}
                                    {{ user.first_name }} {{ user.last_name }}
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.email %}
                                    {{ user.email }}
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.is_active %}
                                    <span class="badge bg-success">نشط</span>
                                    {% else %}
                                    <span class="badge bg-danger">غير نشط</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.last_login %}
                                    {{ user.last_login|date:"Y-m-d H:i" }}
                                    {% else %}
                                    <span class="text-muted">لم يسجل الدخول بعد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% for group in user.groups.all %}
                                    <span class="badge bg-primary">{{ group.name }}</span>
                                    {% empty %}
                                    <span class="text-muted">-</span>
                                    {% endfor %}
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{% url 'administrator:user_groups' user.id %}" class="btn btn-sm btn-outline-primary" title="إدارة المجموعات">
                                            <i class="fas fa-users-cog"></i>
                                        </a>
                                        <a href="{% url 'administrator:user_permissions' user.id %}" class="btn btn-sm btn-outline-info" title="إدارة الصلاحيات">
                                            <i class="fas fa-key"></i>
                                        </a>
                                        <a href="{% url 'administrator:user_edit' user.id %}" class="btn btn-sm btn-outline-secondary" title="تعديل معلومات المستخدم">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'administrator:user_delete' user.id %}" class="btn btn-sm btn-outline-danger" title="حذف المستخدم">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-4x mb-3 text-muted"></i>
                    <h5>لا يوجد مستخدمين</h5>
                    <p class="text-muted">لم يتم إنشاء أي مستخدمين بعد. انقر على زر "إضافة مستخدم جديد" لإنشاء أول مستخدم.</p>
                    <a href="{% url 'administrator:user_add' %}" class="btn btn-primary mt-2">
                        <i class="fas fa-plus-circle me-2"></i>
                        إضافة مستخدم جديد
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5>
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات حول إدارة المستخدمين والصلاحيات
                </h5>
            </div>
            <div class="card-body">
                <p>يمكنك من هذه الصفحة إدارة المستخدمين وتعيين المجموعات والصلاحيات لهم:</p>
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="mb-3">إدارة المجموعات والصلاحيات:</h6>
                        <ul>
                            <li><strong>إدارة المجموعات</strong>: يمكنك إضافة المستخدمين إلى مجموعات محددة.</li>
                            <li><strong>إدارة الصلاحيات</strong>: يمكنك تعيين صلاحيات محددة للمستخدمين بشكل مباشر دون إضافتهم لمجموعة.</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="mb-3">روابط مفيدة:</h6>
                        <ul>
                            <li><a href="{% url 'administrator:group_list' %}">إدارة المجموعات</a></li>
                            <li><a href="{% url 'administrator:permission_dashboard' %}">عرض كافة الصلاحيات المتاحة</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
