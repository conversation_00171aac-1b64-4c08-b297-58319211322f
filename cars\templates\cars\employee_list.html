{% extends 'cars\base.html' %}

{% block title %}الموظفين - نظام إدارة نشاط النقل{% endblock %}

{% block header %}قائمة الموظفين{% endblock %}

{% block content %}
    <div class="mb-3 d-flex justify-content-between align-items-center">
        <h4 class="mb-0">إجمالي الموظفين: {{ employees|length }}</h4>
        <a href="{% url 'cars:employee_add' %}" class="btn btn-success">
            <i class="bi bi-plus-circle"></i> إضافة موظف جديد
        </a>
    </div>

    <div class="table-container">
        <table class="table table-striped table-hover">
            <thead class="table-dark">
                <tr>
                    <th>اسم الموظف</th>
                    <th>المسمى الوظيفي</th>
                    <th>رقم الهاتف</th>
                    <th>البريد الإلكتروني</th>
                    <th>العنوان</th>
                    <th>العمليات</th>
                </tr>
            </thead>
            <tbody>
                {% for employee in employees %}
                    <tr>
                        <td>{{ employee.name }}</td>
                        <td>{{ employee.job_title }}</td>
                        <td>{{ employee.phone }}</td>
                        <td>{{ employee.email }}</td>
                        <td>{{ employee.address }}</td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{% url 'cars:employee_edit' employee.id %}" class="btn btn-sm btn-primary" title="تعديل">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <a href="{% url 'cars:employee_delete' employee.id %}" class="btn btn-sm btn-danger" title="حذف">
                                    <i class="bi bi-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                {% empty %}
                    <tr>
                        <td colspan="6" class="text-center py-4">
                            <div class="alert alert-info mb-0">
                                لا يوجد موظفين مسجلين حالياً. قم بإضافة موظف جديد من خلال زر الإضافة.
                            </div>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
{% endblock %}