{% extends 'Hr/base_hr.html' %}
{% load static %}

{% block title %}تحليل البيانات - الموارد البشرية{% endblock %}

{% block page_title %}تحليل البيانات - الموارد البشرية{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموارد البشرية</a></li>
<li class="breadcrumb-item active">تحليل البيانات</li>
{% endblock %}

{% block content %}
<!-- Summary Cards -->
<div class="row g-3 mb-4">
    <div class="col-xl-3 col-md-6">
        <div class="card shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <p class="text-muted mb-1">إجمالي الموظفين</p>
                        <h2 class="mb-0 fw-bold">{{ total_employees }}</h2>
                        <span class="text-muted fs-7 d-block mt-1">
                            <span class="text-success">
                                <i class="fas fa-arrow-up me-1"></i>{{ employee_growth }}%
                            </span> مقارنة بالشهر الماضي
                        </span>
                    </div>
                    <div class="rounded-circle bg-primary-subtle p-2 mt-1">
                        <i class="fas fa-users text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <p class="text-muted mb-1">متوسط الرواتب</p>
                        <h2 class="mb-0 fw-bold">{{ avg_salary }}</h2>
                        <span class="text-muted fs-7 d-block mt-1">
                            <span class="text-{% if salary_change >= 0 %}success{% else %}danger{% endif %}">
                                <i class="fas fa-arrow-{% if salary_change >= 0 %}up{% else %}down{% endif %} me-1"></i>{{ salary_change|abs }}%
                            </span> مقارنة بالشهر الماضي
                        </span>
                    </div>
                    <div class="rounded-circle bg-success-subtle p-2 mt-1">
                        <i class="fas fa-money-bill-wave text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <p class="text-muted mb-1">نسبة الحضور</p>
                        <h2 class="mb-0 fw-bold">{{ attendance_rate }}%</h2>
                        <span class="text-muted fs-7 d-block mt-1">
                            <span class="text-{% if attendance_change >= 0 %}success{% else %}danger{% endif %}">
                                <i class="fas fa-arrow-{% if attendance_change >= 0 %}up{% else %}down{% endif %} me-1"></i>{{ attendance_change|abs }}%
                            </span> مقارنة بالشهر الماضي
                        </span>
                    </div>
                    <div class="rounded-circle bg-info-subtle p-2 mt-1">
                        <i class="fas fa-user-clock text-info"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <p class="text-muted mb-1">معدل الإحتفاظ</p>
                        <h2 class="mb-0 fw-bold">{{ retention_rate }}%</h2>
                        <span class="text-muted fs-7 d-block mt-1">
                            <span class="text-{% if retention_change >= 0 %}success{% else %}danger{% endif %}">
                                <i class="fas fa-arrow-{% if retention_change >= 0 %}up{% else %}down{% endif %} me-1"></i>{{ retention_change|abs }}%
                            </span> مقارنة بالشهر الماضي
                        </span>
                    </div>
                    <div class="rounded-circle bg-warning-subtle p-2 mt-1">
                        <i class="fas fa-user-shield text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row g-4">
    <!-- Departments Chart (left column) -->
    <div class="col-xl-8">
        <div class="card shadow-sm mb-4">
            <div class="card-header d-flex justify-content-between align-items-center bg-light py-3">
                <h5 class="mb-0 fw-semibold">
                    <i class="fas fa-chart-bar text-primary me-2"></i>توزيع الموظفين حسب الأقسام
                </h5>
                <div class="chart-actions">
                    <button class="btn btn-sm btn-outline-secondary" data-chart-filter="departments" data-period="month">شهر</button>
                    <button class="btn btn-sm btn-outline-secondary active" data-chart-filter="departments" data-period="quarter">ربع سنوي</button>
                    <button class="btn btn-sm btn-outline-secondary" data-chart-filter="departments" data-period="year">سنة</button>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-container" style="height: 300px;">
                    <canvas id="departmentsChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Employee Status Chart -->
        <div class="card shadow-sm mb-4">
            <div class="card-header d-flex justify-content-between align-items-center bg-light py-3">
                <h5 class="mb-0 fw-semibold">
                    <i class="fas fa-chart-pie text-primary me-2"></i>توزيع حالة الموظفين
                </h5>
                <select class="form-select form-select-sm status-chart-filter" style="width: auto;">
                    <option value="all">جميع الأقسام</option>
                    {% for dept in departments %}
                    <option value="{{ dept.dept_code }}">{{ dept.dept_name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="card-body">
                <div class="chart-container" style="height: 300px;">
                    <canvas id="statusChart"></canvas>
                </div>
                <div class="d-flex flex-wrap justify-content-center gap-3 mt-3">
                    <div class="d-flex align-items-center">
                        <span class="me-2" style="display: block; width: 12px; height: 12px; background-color: #28a745; border-radius: 2px;"></span>
                        <span>سارى ({{ active_count }})</span>
                    </div>
                    <div class="d-flex align-items-center">
                        <span class="me-2" style="display: block; width: 12px; height: 12px; background-color: #17a2b8; border-radius: 2px;"></span>
                        <span>إجازة ({{ leave_count }})</span>
                    </div>
                    <div class="d-flex align-items-center">
                        <span class="me-2" style="display: block; width: 12px; height: 12px; background-color: #dc3545; border-radius: 2px;"></span>
                        <span>استقالة ({{ resigned_count }})</span>
                    </div>
                    <div class="d-flex align-items-center">
                        <span class="me-2" style="display: block; width: 12px; height: 12px; background-color: #6c757d; border-radius: 2px;"></span>
                        <span>انقطاع عن العمل ({{ terminated_count }})</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Attendance Trends -->
        <div class="card shadow-sm">
            <div class="card-header d-flex justify-content-between align-items-center bg-light py-3">
                <h5 class="mb-0 fw-semibold">
                    <i class="fas fa-chart-line text-primary me-2"></i>اتجاهات الحضور
                </h5>
                <select class="form-select form-select-sm attendance-chart-period" style="width: auto;">
                    <option value="week">أسبوع</option>
                    <option value="month" selected>شهر</option>
                    <option value="quarter">ربع سنة</option>
                </select>
            </div>
            <div class="card-body">
                <div class="chart-container" style="height: 300px;">
                    <canvas id="attendanceChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Right Column (sidebar) -->
    <div class="col-xl-4">
        <!-- Employee Gender Distribution -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light py-3">
                <h5 class="mb-0 fw-semibold">
                    <i class="fas fa-venus-mars text-primary me-2"></i>توزيع النوع
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="height: 200px;">
                    <canvas id="genderChart"></canvas>
                </div>
                <div class="d-flex justify-content-center gap-4 mt-3">
                    <div class="d-flex align-items-center">
                        <span class="me-2" style="display: block; width: 12px; height: 12px; background-color: #007bff; border-radius: 2px;"></span>
                        <span>ذكر ({{ male_count }})</span>
                    </div>
                    <div class="d-flex align-items-center">
                        <span class="me-2" style="display: block; width: 12px; height: 12px; background-color: #e83e8c; border-radius: 2px;"></span>
                        <span>أنثى ({{ female_count }})</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Age Distribution -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light py-3">
                <h5 class="mb-0 fw-semibold">
                    <i class="fas fa-birthday-cake text-primary me-2"></i>توزيع الأعمار
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="height: 200px;">
                    <canvas id="ageChart"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Insurance Status -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light py-3">
                <h5 class="mb-0 fw-semibold">
                    <i class="fas fa-shield-alt text-primary me-2"></i>حالة التأمين
                </h5>
            </div>
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-7">
                        <div class="chart-container" style="height: 150px;">
                            <canvas id="insuranceChart"></canvas>
                        </div>
                    </div>
                    <div class="col-5">
                        <div class="d-flex flex-column gap-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <span class="me-2" style="display: block; width: 10px; height: 10px; background-color: #28a745; border-radius: 2px;"></span>
                                    <span>مؤمن عليه</span>
                                </div>
                                <span class="fw-bold">{{ insured_count }}</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <span class="me-2" style="display: block; width: 10px; height: 10px; background-color: #dc3545; border-radius: 2px;"></span>
                                    <span>غير مؤمن عليه</span>
                                </div>
                                <span class="fw-bold">{{ not_insured_count }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Leave Distribution -->
        <div class="card shadow-sm">
            <div class="card-header bg-light py-3">
                <h5 class="mb-0 fw-semibold">
                    <i class="fas fa-umbrella-beach text-primary me-2"></i>توزيع الإجازات
                </h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="height: 200px;">
                    <canvas id="leaveChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .card {
        border: none;
        border-radius: 8px;
        overflow: hidden;
        transition: all 0.3s ease;
    }
    
    .card:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1) !important;
    }
    
    .chart-container {
        position: relative;
        width: 100%;
    }
    
    .chart-actions .btn {
        padding: 0.25rem 0.75rem;
        font-size: 0.8rem;
    }
    
    .chart-actions .btn.active {
        background-color: var(--bs-primary);
        border-color: var(--bs-primary);
        color: #fff;
    }
    
    .fs-7 {
        font-size: 0.8rem;
    }

    .form-select-sm {
        font-size: 0.85rem;
        padding: 0.25rem 1rem 0.25rem 0.5rem; 
    }
    
    .icon-wrapper {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
    }
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set global Chart.js configuration
    Chart.defaults.font.size = 12;
    Chart.defaults.font.family = "'Cairo', 'Segoe UI', sans-serif";
    Chart.defaults.color = '#6c757d';
    Chart.defaults.plugins.legend.position = 'bottom';
    
    // Department Distribution Chart
    const departmentCtx = document.getElementById('departmentsChart').getContext('2d');
    const departmentChart = new Chart(departmentCtx, {
        type: 'bar',
        data: {
            labels: {{ dept_names|safe }},
            datasets: [{
                label: 'عدد الموظفين',
                data: {{ dept_counts|safe }},
                backgroundColor: [
                    'rgba(54, 162, 235, 0.7)',
                    'rgba(75, 192, 192, 0.7)',
                    'rgba(255, 159, 64, 0.7)',
                    'rgba(255, 99, 132, 0.7)',
                    'rgba(153, 102, 255, 0.7)',
                    'rgba(201, 203, 207, 0.7)',
                    'rgba(255, 205, 86, 0.7)'
                ],
                borderColor: [
                    'rgba(54, 162, 235, 1)',
                    'rgba(75, 192, 192, 1)',
                    'rgba(255, 159, 64, 1)',
                    'rgba(255, 99, 132, 1)',
                    'rgba(153, 102, 255, 1)',
                    'rgba(201, 203, 207, 1)',
                    'rgba(255, 205, 86, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            },
            plugins: {
                title: {
                    display: false
                },
                legend: {
                    display: false
                }
            }
        }
    });
    
    // Status Distribution Chart
    const statusCtx = document.getElementById('statusChart').getContext('2d');
    const statusChart = new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: ['سارى', 'إجازة', 'استقالة', 'انقطاع عن العمل'],
            datasets: [{
                label: 'عدد الموظفين',
                data: [{{ active_count }}, {{ leave_count }}, {{ resigned_count }}, {{ terminated_count }}],
                backgroundColor: [
                    '#28a745',
                    '#17a2b8',
                    '#dc3545',
                    '#6c757d'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
    
    // Gender Distribution Chart
    const genderCtx = document.getElementById('genderChart').getContext('2d');
    const genderChart = new Chart(genderCtx, {
        type: 'pie',
        data: {
            labels: ['ذكر', 'أنثى'],
            datasets: [{
                label: 'الجنس',
                data: [{{ male_count }}, {{ female_count }}],
                backgroundColor: [
                    '#007bff',
                    '#e83e8c'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
    
    // Age Distribution Chart
    const ageCtx = document.getElementById('ageChart').getContext('2d');
    const ageChart = new Chart(ageCtx, {
        type: 'bar',
        data: {
            labels: ['18-25', '26-35', '36-45', '46-55', '56+'],
            datasets: [{
                label: 'العمر',
                data: {{ age_distribution|safe }},
                backgroundColor: 'rgba(75, 192, 192, 0.7)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
    
    // Insurance Status Chart
    const insuranceCtx = document.getElementById('insuranceChart').getContext('2d');
    const insuranceChart = new Chart(insuranceCtx, {
        type: 'pie',
        data: {
            labels: ['مؤمن عليه', 'غير مؤمن عليه'],
            datasets: [{
                label: 'حالة التأمين',
                data: [{{ insured_count }}, {{ not_insured_count }}],
                backgroundColor: [
                    '#28a745',
                    '#dc3545'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
    
    // Leave Type Distribution Chart
    const leaveCtx = document.getElementById('leaveChart').getContext('2d');
    const leaveChart = new Chart(leaveCtx, {
        type: 'bar',
        data: {
            labels: {{ leave_types|safe }},
            datasets: [{
                label: 'عدد الإجازات',
                data: {{ leave_counts|safe }},
                backgroundColor: 'rgba(255, 99, 132, 0.7)',
                borderColor: 'rgba(255, 99, 132, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
    
    // Attendance Chart
    const attendanceCtx = document.getElementById('attendanceChart').getContext('2d');
    const attendanceChart = new Chart(attendanceCtx, {
        type: 'line',
        data: {
            labels: {{ attendance_dates|safe }},
            datasets: [
                {
                    label: 'معدل الحضور',
                    data: {{ attendance_rates|safe }},
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 2,
                    pointBackgroundColor: 'rgba(54, 162, 235, 1)',
                    fill: true,
                    tension: 0.3
                },
                {
                    label: 'معدل التأخير',
                    data: {{ late_rates|safe }},
                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                    borderColor: 'rgba(255, 99, 132, 1)',
                    borderWidth: 2,
                    pointBackgroundColor: 'rgba(255, 99, 132, 1)',
                    fill: true,
                    tension: 0.3
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                }
            },
            interaction: {
                mode: 'index',
                intersect: false
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.dataset.label + ': ' + context.parsed.y + '%';
                        }
                    }
                }
            }
        }
    });
    
    // Chart Period Filter Buttons
    document.querySelectorAll('[data-chart-filter]').forEach(button => {
        button.addEventListener('click', function() {
            const filterType = this.getAttribute('data-chart-filter');
            const period = this.getAttribute('data-period');
            
            // Remove active class from all buttons in the same group
            document.querySelectorAll(`[data-chart-filter="${filterType}"]`).forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Add active class to clicked button
            this.classList.add('active');
            
            // Here you would typically fetch new data based on the selected period
            // For demonstration, we'll just log the action
            console.log(`Changing ${filterType} chart to ${period} period`);
            
            // In a real application, you would make an AJAX call here to get new data
            // and then update the chart
        });
    });
    
    // Department filter for status chart
    document.querySelector('.status-chart-filter').addEventListener('change', function() {
        const selectedDept = this.value;
        console.log(`Filtering status chart by department: ${selectedDept}`);
        
        // In a real application, you would make an AJAX call here to get new data
        // and then update the chart
    });
    
    // Period filter for attendance chart
    document.querySelector('.attendance-chart-period').addEventListener('change', function() {
        const selectedPeriod = this.value;
        console.log(`Changing attendance chart period to: ${selectedPeriod}`);
        
        // In a real application, you would make an AJAX call here to get new data
        // and then update the chart
    });
});
</script>
