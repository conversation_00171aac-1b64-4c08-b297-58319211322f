# نظام إدارة نشاط النقل (Transport Management System)

نظام متكامل لإدارة أسطول السيارات وحساب تكاليف التشغيل. يوفر هذا التطبيق واجهة مستخدم سهلة الاستخدام لإدارة السيارات والرحلات وحساب متوسط سعر التشغيل بناءً على عوامل متعددة.

## الميزات الرئيسية

- إدارة قاعدة بيانات السيارات مع معلومات تفصيلية عن كل سيارة
- إضافة وتعديل وحذف الرحلات
- حساب متوسط سعر التشغيل بشكل احترافي
- تتبع جميع التكاليف (الوقود، الصيانة، الإهلاك، الترخيص، أرباح السائق)
- عرض تقارير وإحصاءات مفصلة مدعمة برسومات بيانية
- واجهة مستخدم سهلة الاستخدام باللغة العربية
- قاعدة بيانات SQLite للتخزين

## المتطلبات

- Python 3.8+
- Django 4.0+
- متصفح ويب حديث

## التثبيت والإعداد

1. قم بتنزيل أو نسخ المشروع من مستودع Git
```
git clone https://github.com/username/transport-management-system.git
cd transport_management_system
```

2. إنشاء بيئة افتراضية وتفعيلها (اختياري ولكن ينصح به)
```
python -m venv venv
# في نظام Windows
venv\Scripts\activate
# في نظام Linux/Mac
source venv/bin/activate
```

3. تثبيت الحزم المطلوبة
```
pip install -r requirements.txt
```

4. تهيئة قاعدة البيانات
```
python manage.py makemigrations
python manage.py migrate
```

5. إنشاء مستخدم مدير (الإدارة فقط)
```
python manage.py createsuperuser
```

6. تشغيل الخادم المحلي
```
python manage.py runserver
```

7. افتح المتصفح وانتقل إلى `http://127.0.0.1:8080`

## كيفية الاستخدام

1. **إدارة السيارات**
   - إضافة سيارات جديدة من تبويب "إضافة سيارة"
   - عرض جميع السيارات من تبويب "السيارات"
   - تعديل أو حذف السيارات من قائمة السيارات

2. **إدارة الرحلات**
   - إضافة رحلات جديدة من تبويب "الرحلات" ثم "إضافة رحلة"
   - يتم حساب جميع التكاليف تلقائياً بناءً على الإعدادات

3. **حساب متوسط السعر**
   - انتقل إلى تبويب "حساب متوسط السعر" لرؤية متوسط سعر تشغيل كل سيارة
   - يتم عرض جميع السيارات النشطة وتكاليفها المفصلة

4. **الإعدادات**
   - قم بتعديل أسعار الوقود ومعدلات التكاليف من تبويب "الإعدادات"

5. **التقارير**
   - انتقل إلى تبويب "التقارير" لمشاهدة إحصاءات وتحليلات مفصلة عن نشاط النقل

## هيكل المشروع

```
transport_management_system/
├── transport_project/       # مجلد المشروع الرئيسي
│   ├── settings.py          # إعدادات المشروع
│   ├── urls.py              # تكوين المسارات الرئيسي
│   └── ...
├── cars/                    # تطبيق إدارة السيارات
│   ├── models.py            # نماذج البيانات
│   ├── views.py             # معالجات الطلبات
│   ├── urls.py              # تكوين المسارات
│   ├── forms.py             # نماذج الإدخال
│   ├── admin.py             # تكوين واجهة الإدارة
│   └── templatetags/        # وسوم القوالب المخصصة
│       ├── __init__.py
│       └── custom_filters.py
├── templates/               # قوالب HTML
│   ├── base.html            # القالب الأساسي
│   └── cars/                # قوالب التطبيق
│       ├── home.html
│       ├── car_list.html
│       └── ...
├── static/                  # الملفات الثابتة
│   ├── css/
│   │   └── custom.css
│   ├── js/
│   └── img/
├── manage.py                # سكريبت إدارة Django
└── db.sqlite3               # قاعدة البيانات
```

## الرخصة

هذا المشروع مرخص بموجب رخصة MIT. انظر ملف LICENSE للحصول على التفاصيل.
