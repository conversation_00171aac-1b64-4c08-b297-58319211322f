{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load django_permissions %}

{% block content %}
<div class="container">
    <h1 class="mt-4">تقرير التدريب</h1>

    <form method="get" class="mb-4">
        <div class="row">
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="start_date" class="form-label">تاريخ البدء</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="{{ request.GET.start_date }}">
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="end_date" class="form-label">تاريخ الانتهاء</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="{{ request.GET.end_date }}">
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="training_type" class="form-label">نوع التدريب</label>
                    <select class="form-select" id="training_type" name="training_type">
                        <option value="">جميع الأنواع</option>
                        {% for type in training_types %}
                        <option value="{{ type }}" {% if request.GET.training_type == type %}selected{% endif %}>{{ type }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
        </div>

        <div class="actions text-end">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-search me-1"></i>
                بحث
            </button>
            <a href="{% url 'Hr:reports:report_detail' 'trainings' %}" class="btn btn-secondary">
                <i class="fas fa-redo me-1"></i>
                إعادة تعيين
            </a>
            {% if perms.Hr.export_training_data or user|is_admin %}
            <div class="btn-group ms-2">
                <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-download me-1"></i>
                    تصدير
                </button>
                <ul class="dropdown-menu">
                    <li>
                        <a class="dropdown-item" href="?{{ request.GET.urlencode }}&export=excel">
                            <i class="fas fa-file-excel me-1 text-success"></i>
                            Excel
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="?{{ request.GET.urlencode }}&export=csv">
                            <i class="fas fa-file-csv me-1 text-info"></i>
                            CSV
                        </a>
                    </li>
                </ul>
            </div>
            {% endif %}
        </div>
    </form>

    <div class="table-responsive">
        <table class="table table-bordered table-hover">
            <thead>
                <tr>
                    <th>#</th>
                    <th>اسم التدريب</th>
                    <th>تاريخ البدء</th>
                    <th>تاريخ الانتهاء</th>
                    <th>المدرب</th>
                    <th>الموقع</th>
                    <th>الحالة</th>
                    <th class="text-center">الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for training in trainings %}
                <tr>
                    <td>{{ forloop.counter }}</td>
                    <td>{{ training.name }}</td>
                    <td>{{ training.start_date }}</td>
                    <td>{{ training.end_date }}</td>
                    <td>{{ training.trainer }}</td>
                    <td>{{ training.location }}</td>
                    <td>{{ training.get_status_display }}</td>
                    <td class="text-center">
                        {% if perms.Hr.view_training_detail or user|is_admin %}
                        <a href="{% url 'Hr:trainings:detail' training.id %}" class="btn btn-sm btn-primary">
                            <i class="fas fa-eye"></i>
                        </a>
                        {% endif %}
                        {% if perms.Hr.print_training or user|is_admin %}
                        <a href="{% url 'Hr:trainings:print' training.id %}" class="btn btn-sm btn-info">
                            <i class="fas fa-print"></i>
                        </a>
                        {% endif %}
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="8" class="text-center">لا توجد بيانات لعرضها</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}