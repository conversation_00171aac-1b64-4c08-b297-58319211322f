{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ page_title }} - نظام الدولية{% endblock %}

{% block page_title %}{{ page_title }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموظفين</a></li>
<li class="breadcrumb-item active">{{ page_title }}</li>
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">تقرير الحضور والانصراف</h5>
        <div>
            <a href="{% url 'Hr:attendance:attendance_report_export' %}?{{ request.GET.urlencode }}" class="btn btn-success">
                <i class="fas fa-file-excel"></i> تصدير التقرير
            </a>
            <button type="button" class="btn btn-primary" onclick="window.print()">
                <i class="fas fa-print"></i> طباعة
            </button>
        </div>
    </div>
    <div class="card-body">
        <!-- Filter Form -->
        <form method="get" class="mb-4">
            <div class="row">
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="department" class="form-label">القسم</label>
                        <select name="department" id="department" class="form-select">
                            <option value="">-- جميع الأقسام --</option>
                            {% for dept in departments %}
                            <option value="{{ dept.id }}" {% if selected_department|stringformat:"s" == dept.id|stringformat:"s" %}selected{% endif %}>
                                {{ dept.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="employee" class="form-label">الموظف</label>
                        <select name="employee" id="employee" class="form-select">
                            <option value="">-- جميع الموظفين --</option>
                            {% for emp in employees %}
                            <option value="{{ emp.id }}" {% if selected_employee|stringformat:"s" == emp.id|stringformat:"s" %}selected{% endif %}>
                                {{ emp.emp_full_name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="mb-3">
                        <label for="start_date" class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" 
                            value="{{ request.GET.start_date }}">
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="mb-3">
                        <label for="end_date" class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" 
                            value="{{ request.GET.end_date }}">
                    </div>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <div class="mb-3 w-100">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search"></i> تطبيق
                        </button>
                    </div>
                </div>
            </div>
        </form>

        <!-- Summary Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-light">
                    <div class="card-body">
                        <h6 class="card-title">إجمالي أيام العمل</h6>
                        <p class="card-text h3">{{ summary.total_working_days }}</p>
                        <small class="text-muted">خلال الفترة المحددة</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <h6 class="card-title">معدل الحضور</h6>
                        <p class="card-text h3">{{ summary.attendance_rate }}%</p>
                        <small>متوسط الحضور في الموعد</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning">
                    <div class="card-body">
                        <h6 class="card-title">معدل التأخير</h6>
                        <p class="card-text h3">{{ summary.late_rate }}%</p>
                        <small>متوسط التأخير اليومي</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-danger text-white">
                    <div class="card-body">
                        <h6 class="card-title">معدل الغياب</h6>
                        <p class="card-text h3">{{ summary.absence_rate }}%</p>
                        <small>متوسط الغياب اليومي</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Department Statistics -->
        {% if department_stats %}
        <div class="mb-4">
            <h6>إحصائيات الأقسام:</h6>
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>القسم</th>
                            <th>عدد الموظفين</th>
                            <th>معدل الحضور</th>
                            <th>معدل التأخير</th>
                            <th>معدل الغياب</th>
                            <th>متوسط ساعات العمل</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for dept in department_stats %}
                        <tr>
                            <td>{{ dept.name }}</td>
                            <td>{{ dept.employee_count }}</td>
                            <td>{{ dept.attendance_rate }}%</td>
                            <td>{{ dept.late_rate }}%</td>
                            <td>{{ dept.absence_rate }}%</td>
                            <td>{{ dept.avg_work_hours }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endif %}

        <!-- Employee Details -->
        {% if employee_stats %}
        <div class="mb-4">
            <h6>تفاصيل الموظفين:</h6>
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>الموظف</th>
                            <th>القسم</th>
                            <th>أيام الحضور</th>
                            <th>أيام التأخير</th>
                            <th>أيام الغياب</th>
                            <th>إجمالي ساعات العمل</th>
                            <th>متوسط ساعات العمل</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for emp in employee_stats %}
                        <tr>
                            <td>{{ emp.emp_full_name }}</td>
                            <td>{{ emp.department }}</td>
                            <td>{{ emp.attendance_days }}</td>
                            <td>{{ emp.late_days }}</td>
                            <td>{{ emp.absent_days }}</td>
                            <td>{{ emp.total_work_hours }}</td>
                            <td>{{ emp.avg_work_hours }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endif %}

        <!-- Trends Chart -->
        <div class="mb-4">
            <h6>اتجاهات الحضور:</h6>
            <canvas id="attendanceTrendsChart"></canvas>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/chart.min.js' %}"></script>
<script>
    $(document).ready(function() {
        // Initialize select2 for dropdowns
        $('#department, #employee').select2({
            theme: 'bootstrap-5',
            language: "ar",
            dir: "rtl"
        });

        // Initialize attendance trends chart
        const ctx = document.getElementById('attendanceTrendsChart').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: {{ chart_data.labels|safe }},
                datasets: [
                    {
                        label: 'معدل الحضور',
                        data: {{ chart_data.attendance_rates|safe }},
                        borderColor: '#198754',
                        tension: 0.1
                    },
                    {
                        label: 'معدل التأخير',
                        data: {{ chart_data.late_rates|safe }},
                        borderColor: '#ffc107',
                        tension: 0.1
                    },
                    {
                        label: 'معدل الغياب',
                        data: {{ chart_data.absence_rates|safe }},
                        borderColor: '#dc3545',
                        tension: 0.1
                    }
                ]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });
    });
</script>
{% endblock %}