{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load image_utils %}
{% load django_permissions %}
{% load form_utils %}

{% block title %}{{ employee.emp_full_name }} - تفاصيل الموظف{% endblock %}

{% block page_title %}تفاصيل الموظف{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:employees:list' %}">الموظفين</a></li>
<li class="breadcrumb-item active">{{ employee.emp_full_name|default:"موظف" }}</li>
{% endblock %}

{% block content %}
<!-- شريط الإجراءات -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h4 class="mb-0">{{ employee.emp_full_name }}</h4>
        <p class="text-muted mb-0">{{ employee.jop_name|default:"No Job Assigned" }}</p>
    </div>
    <div class="btn-group">
        {% if perms.Hr.change_employee or user|is_admin %}
            <a href="{% url 'Hr:employees:edit' emp_id=employee.emp_id %}" class="btn btn-primary">
                <i class="fas fa-edit me-1"></i> تعديل
            </a>
        {% endif %}
        {% if perms.Hr.view_employee or user|is_admin %}
            <a href="{% url 'Hr:employees:print' emp_id=employee.emp_id %}" class="btn btn-info" target="_blank">
                <i class="fas fa-print me-1"></i> طباعة
            </a>
        {% endif %}
        {% if perms.Hr.delete_employee or user|is_admin %}
            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteEmployeeModal">
                <i class="fas fa-trash me-1"></i> حذف
            </button>
        {% endif %}
    </div>
</div>

<!-- المعلومات العامة -->
<div class="row">
    <!-- البيانات الشخصية -->
    <div class="col-lg-4 mb-4">
        <div class="card shadow-sm h-100">
            <div class="card-body text-center pt-4">
                {% if employee.emp_image %}
                <div class="employee-avatar mx-auto position-relative mb-3">
                    <img src="{{ employee.emp_image|binary_to_img }}" alt="{{ employee.emp_full_name }}" class="rounded-circle user-image">
                    {% if employee.working_condition == 'سارى' %}
                    <span class="status-badge bg-success"><i class="fas fa-check"></i></span>
                    {% elif employee.working_condition == 'استقالة' %}
                    <span class="status-badge bg-info"><i class="fas fa-pause"></i></span>
                    {% elif employee.working_condition == 'انقطاع عن العمل'%}
                    <span class="status-badge bg-danger"><i class="fas fa-times"></i></span>
                    {% endif %}
                </div>
                {% else %}
                <div class="employee-avatar-placeholder mx-auto position-relative mb-3">
                    <div class="avatar-placeholder display-1">
                        {{ employee.emp_first_name|slice:":1"|upper }}
                    </div>
                    {% if employee.working_condition == 'سارى' %}
                    <span class="status-badge bg-success"><i class="fas fa-check"></i></span>
                    {% elif employee.working_condition == 'استقالة' %}
                    <span class="status-badge bg-info"><i class="fas fa-pause"></i></span>
                    {% elif employee.working_condition == 'انقطاع عن العمل'%}
                    <span class="status-badge bg-danger"><i class="fas fa-times"></i></span>
                    {% endif %}
                </div>
                {% endif %}

                <h5 class="mt-3 mb-1">{{ employee.emp_full_name }}</h5>
                <p class="text-muted">{{ employee.jop_name|default:"-" }}</p>

                <hr>

                <div class="row text-start g-3 mt-2">
                    <div class="col-6">
                        <h6 class="text-muted mb-1">رقم الموظف</h6>
                        <p class="mb-0">{{ employee.emp_id }}</p>
                    </div>
                    <div class="col-6">
                        <h6 class="text-muted mb-1">الحالة</h6>
                        <p class="mb-0">{{ employee.working_condition }}</p>
                    </div>
                    <div class="col-6">
                        <h6 class="text-muted mb-1">الرقم القومي</h6>
                        <p class="mb-0">{{ employee.national_id|default:"-" }}</p>
                    </div>
                    <div class="col-6">
                        <h6 class="text-muted mb-1">تاريخ التعيين</h6>
                        <p class="mb-0">{% if employee.emp_date_hiring %}{{ employee.emp_date_hiring|date:"d/m/Y" }}{% else %}-{% endif %}</p>
                    </div>
                </div>

                <div class="mt-3">
                    <h6 class="text-muted mb-2">بيانات الاتصال</h6>
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-phone-alt text-muted me-2"></i>
                        <span>{{ employee.emp_phone1|default:"-" }}</span>
                    </div>
                    {% if employee.emp_phone2 %}
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-phone-alt text-muted me-2"></i>
                        <span>{{ employee.emp_phone2 }}</span>
                    </div>
                    {% endif %}
                    <div class="d-flex align-items-center">
                        <i class="fas fa-map-marker-alt text-muted me-2"></i>
                        <span>{{ employee.emp_address|default:"-" }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- البيانات التفصيلية -->
    <div class="col-lg-8">
        <!-- بيانات العمل والتوظيف -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-white py-3">
                <h5 class="mb-0">
                    <i class="fas fa-briefcase me-2"></i>
                    معلومات العمل
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">القسم</h6>
                        <p>{{ employee.department.dept_name|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">الوظيفة</h6>
                        <p>{{ employee.jop_name|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">نوع الموظف</h6>
                        <p>{{ employee.emp_type|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">تاريخ التعيين</h6>
                        <p>{% if employee.emp_date_hiring %}{{ employee.emp_date_hiring|date:"d/m/Y" }}{% else %}-{% endif %}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">تاريخ تجديد العقد</h6>
                        <p>{% if employee.contract_renewal_date %}{{ employee.contract_renewal_date|date:"d/m/Y" }}{% else %}-{% endif %}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">الوردية</h6>
                        <p>{{ employee.shift_type|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">وردية الأسبوع الحالي</h6>
                        <p>{{ employee.current_week_shift|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">وردية الأسبوع القادم</h6>
                        <p>{{ employee.next_week_shift|default:"-" }}</p>
                    </div>
                </div>

                {% if employee.working_condition == 'استقالة' %}
                <hr>
                <h6 class="text-danger mb-2">بيانات الاستقالة</h6>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <h6 class="text-muted mb-1">تاريخ الاستقالة</h6>
                        <p>{% if employee.date_resignation %}{{ employee.date_resignation|date:"d/m/Y" }}{% else %}-{% endif %}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <h6 class="text-muted mb-1">سبب الاستقالة</h6>
                        <p>{{ employee.reason_resignation|default:"-" }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- بيانات التأمين -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-white py-3">
                <h5 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    التأمين والبطاقة الصحية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">حالة التأمين</h6>
                        <p>{{ employee.insurance_status|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">وظيفة التأمين</h6>
                        <p>{{ employee.job_insurance.job_name_insurance|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">رقم التأمين</h6>
                        <p>{{ employee.number_insurance|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">تاريخ بداية التأمين</h6>
                        <p>{% if employee.date_insurance_start %}{{ employee.date_insurance_start|date:"d/m/Y" }}{% else %}-{% endif %}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">راتب التأمين</h6>
                        <p>{{ employee.insurance_salary|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">نسبة التأمين المستحق</h6>
                        <p>{{ employee.percentage_insurance_payable|default:"-" }}</p>
                    </div>
                </div>

                <hr>

                <h6 class="text-primary mb-2">بيانات البطاقة الصحية</h6>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <h6 class="text-muted mb-1">البطاقة الصحية</h6>
                        <p>{{ employee.health_card|default:"-" }}</p>
                    </div>
                    <div class="col-md-3 mb-3">
                        <h6 class="text-muted mb-1">رقم البطاقة</h6>
                        <p>{{ employee.health_card_number|default:"-" }}</p>
                    </div>
                    <div class="col-md-3 mb-3">
                        <h6 class="text-muted mb-1">تاريخ البداية</h6>
                        <p>{% if employee.health_card_start_date %}{{ employee.health_card_start_date|date:"d/m/Y" }}{% else %}-{% endif %}</p>
                    </div>
                    <div class="col-md-3 mb-3">
                        <h6 class="text-muted mb-1">تاريخ التجديد</h6>
                        <p>{% if employee.health_card_renewal_date %}{{ employee.health_card_renewal_date|date:"d/m/Y" }}{% else %}-{% endif %}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- بيانات شخصية إضافية -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-white py-3">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    معلومات شخصية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">الاسم الأول</h6>
                        <p>{{ employee.emp_first_name|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">الاسم الثاني</h6>
                        <p>{{ employee.emp_second_name|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">اسم الأم</h6>
                        <p>{{ employee.mother_name|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">الاسم بالإنجليزية</h6>
                        <p>{{ employee.emp_name_english|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">الجنسية</h6>
                        <p>{{ employee.emp_nationality|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">الحالة الاجتماعية</h6>
                        <p>{{ employee.emp_marital_status|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">تاريخ الميلاد</h6>
                        <p>{% if employee.date_birth %}{{ employee.date_birth|date:"d/m/Y" }}{% else %}-{% endif %}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">محل الميلاد</h6>
                        <p>{{ employee.place_birth|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">المحافظة</h6>
                        <p>{{ employee.governorate|default:"-" }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <h6 class="text-muted mb-1">شهادة الخدمة العسكرية</h6>
                        <p>{{ employee.military_service_certificate|default:"-" }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج الحذف -->
<div class="modal fade" id="deleteEmployeeModal" tabindex="-1" aria-labelledby="deleteEmployeeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteEmployeeModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من رغبتك في حذف بيانات الموظف <strong>{{ employee.emp_full_name }}</strong>؟</p>
                <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form action="{% url 'Hr:employees:delete' employee.emp_id %}" method="post" class="d-inline">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">نعم، حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* صورة الموظف وبيانات البطاقة الشخصية */
    .employee-avatar {
        width: 160px;
        height: 160px;
        border-radius: 50%;
        overflow: hidden;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        border: 5px solid #fff;
        transition: all 0.3s ease;
        margin-bottom: 1.5rem;
        position: relative;
    }
    
    .employee-avatar:hover {
        transform: scale(1.05);
    }

    .employee-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .employee-avatar-placeholder {
        width: 150px;
        height: 150px;
        margin-bottom: 1.5rem;
        position: relative;
    }

    .avatar-placeholder {
        width: 150px;
        height: 150px;
        border-radius: 50%;
        background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 3.5rem;
        font-weight: 300;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 4px solid #fff;
    }

    .status-badge {
        position: absolute;
        bottom: 5px;
        right: 5px;
        width: 35px;
        height: 35px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 3px solid white;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }
    
    /* تحسين مظهر العناوين والنصوص */
    h6.text-muted {
        font-size: 0.9rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    /* تنسيق أزرار الإجراءات */
    .btn-group .btn {
        padding: 0.6rem 1.2rem;
        font-weight: 600;
        letter-spacing: 0.5px;
        margin-right: 0.5rem;
        position: relative;
        overflow: hidden;
    }

    .btn-group .btn i {
        margin-left: 8px;
        font-size: 0.9em;
    }

    /* تأثير خاص للأزرار عند التحويم */
    .btn-group .btn::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(255,255,255,0.2);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: width 0.6s ease, height 0.6s ease;
    }

    .btn-group .btn:hover::after {
        width: 200%;
        height: 200%;
    }
    
    /* تأثيرات خاصة للأيقونات داخل البطاقات */
    .card .card-header i.fas {
        width: 40px;
        height: 40px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 10px;
        margin-left: 12px;
        position: relative;
    }
    
    /* إضافة تأثير خلفية خفيف للأيقونات */
    .card .card-header i.fas::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: currentColor;
        opacity: 0.1;
        border-radius: 10px;
        z-index: -1;
    }
    
    /* تنسيق خاص لشاشة التفاصيل */
    .card:nth-of-type(1) .card-header i.fas {
        color: var(--primary-color);
    }

    .card:nth-of-type(2) .card-header i.fas {
        color: var(--success-color);
    }

    .card:nth-of-type(3) .card-header i.fas {
        color: var(--warning-color);
    }

    /* ===== TEXT VISIBILITY FIXES FOR EMPLOYEE DETAIL ===== */

    /* Page Header Text Fixes */
    h4 {
        color: var(--text-primary) !important;
    }

    .text-muted {
        color: var(--text-secondary) !important;
    }

    /* Card Content Text Fixes */
    .card h5,
    .card h6,
    .card p {
        color: var(--text-primary) !important;
    }

    .card .text-muted {
        color: var(--text-secondary) !important;
    }

    /* Employee Name and Job Title */
    .card-body h5 {
        color: var(--text-primary) !important;
    }

    .card-body .text-muted {
        color: var(--text-secondary) !important;
    }

    /* Status Badge Text */
    .status-badge {
        color: var(--text-inverse) !important;
    }

    /* Contact Information Text */
    .card-body span {
        color: var(--text-primary) !important;
    }

    /* Section Headers */
    .card-header h5 {
        color: var(--text-primary) !important;
    }

    /* Data Labels and Values */
    .col-md-4 h6,
    .col-md-3 h6,
    .col-md-6 h6 {
        color: var(--text-secondary) !important;
        font-weight: 600 !important;
    }

    .col-md-4 p,
    .col-md-3 p,
    .col-md-6 p {
        color: var(--text-primary) !important;
    }

    /* Special Section Headers */
    h6.text-primary {
        color: var(--primary-color) !important;
    }

    h6.text-danger {
        color: var(--danger-color) !important;
    }

    /* Modal Text Fixes */
    .modal-content {
        background-color: var(--bg-primary) !important;
        color: var(--text-primary) !important;
    }

    .modal-title {
        color: var(--text-primary) !important;
    }

    .modal-body p {
        color: var(--text-primary) !important;
    }

    .modal-body .text-danger {
        color: var(--danger-color) !important;
    }

    /* Button Group Text */
    .btn-group .btn {
        color: var(--text-inverse) !important;
    }

    /* Avatar Placeholder Text */
    .avatar-placeholder {
        color: var(--text-inverse) !important;
    }

    /* Contact Icons */
    .fas.text-muted {
        color: var(--text-secondary) !important;
    }

    /* Ensure all text in cards is visible */
    .card * {
        color: inherit;
    }

    .card .text-start h6 {
        color: var(--text-secondary) !important;
    }

    .card .text-start p {
        color: var(--text-primary) !important;
    }
</style>
{% endblock %}
