{% extends 'administrator/base_admin.html' %}
{% load static %}

{% block title %}إدارة صلاحيات المجموعة - لوحة تحكم مدير النظام{% endblock %}

{% block page_icon %}shield-alt{% endblock %}
{% block page_header %}إدارة صلاحيات المجموعة {{ group.name }}{% endblock %}

{% block extra_css %}
<style>
    /* Main Containers */
    .permissions-container {
        max-height: 700px;
        overflow-y: auto;
        border: 1px solid #eaeaea;
        border-radius: 0.5rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        position: relative;
    }
    
    /* Search Box Styling */
    .search-box {
        position: sticky;
        top: 0;
        z-index: 100;
        background-color: white;
        padding: 1rem;
        margin-bottom: 1rem;
        border-bottom: 1px solid #eaeaea;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
    }
    
    .search-box .input-group-text {
        background-color: #f8f9fa;
        border-left: none;
    }
    
    .search-box .form-control {
        border-right: none;
    }
    
    /* Permission Groups */
    .permission-group {
        margin-bottom: 1rem;
        border: 1px solid #eaeaea;
        border-radius: 0.5rem;
        transition: all 0.2s ease-in-out;
        background-color: #fff;
    }
    
    .permission-group:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    }
    
    .permission-group-header {
        background-color: #f8f9fa;
        padding: 0.8rem 1rem;
        border-bottom: 1px solid #eaeaea;
        cursor: pointer;
        border-radius: 0.5rem 0.5rem 0 0;
        position: relative;
    }
    
    .permission-group-header h6 {
        font-weight: 600;
    }
    
    .permission-group-header .badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
    }
    
    .permission-group-content {
        padding: 1rem 1.2rem;
        background-color: rgba(250, 250, 250, 0.3);
    }
    
    .permission-count {
        background-color: #e9ecef;
        color: #495057;
        font-size: 0.8rem;
        padding: 0.25em 0.6em;
        border-radius: 50px;
        margin-right: 0.5rem;
    }
    
    /* Permission Items */
    .permission-checkbox {
        margin-bottom: 0.7rem;
        padding: 0.4rem 0.6rem;
        border-radius: 0.25rem;
        transition: background-color 0.15s ease-in-out;
    }
    
    .permission-checkbox:hover {
        background-color: rgba(0, 0, 0, 0.02);
    }
    
    .permission-checkbox .form-check {
        display: flex;
        align-items: center;
    }
    
    .permission-checkbox .form-check-label {
        margin-right: 0.5rem;
        user-select: none;
        cursor: pointer;
        flex: 1;
    }
    
    .permission-checkbox .form-check-input {
        cursor: pointer;
    }
    
    /* Permission Types */
    .perm-view {
        border-right: 3px solid #28a745;
    }
    
    .perm-add {
        border-right: 3px solid #007bff;
    }
    
    .perm-change {
        border-right: 3px solid #fd7e14;
    }
    
    .perm-delete {
        border-right: 3px solid #dc3545;
    }
    
    /* Group Select Buttons */
    .group-actions .btn {
        border-radius: 50px;
        padding: 0.2rem 0.7rem;
        font-size: 0.75rem;
    }
    
    /* Stats Counter */
    .permissions-stats {
        position: sticky;
        bottom: 0;
        background-color: #f8f9fa;
        padding: 0.7rem 1rem;
        border-top: 1px solid #eaeaea;
        font-size: 0.9rem;
        z-index: 100;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .stat-badge {
        font-size: 0.75rem;
        padding: 0.25em 0.6em;
        margin: 0 0.2rem;
        border-radius: 50px;
    }

    /* Group Expand/Collapse */
    .group-toggle-icon {
        transition: transform 0.2s ease-in-out;
    }

    .collapsed .group-toggle-icon {
        transform: rotate(-90deg);
    }
    
    /* Mobile Optimizations */
    @media (max-width: 768px) {
        .permission-group-header {
            padding: 0.7rem;
        }
        
        .group-actions .btn {
            padding: 0.2rem 0.4rem;
            font-size: 0.7rem;
        }
        
        .permission-group-content {
            padding: 0.7rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    إدارة صلاحيات المجموعة
                </h5>
                <a href="{% url 'administrator:group_list' %}" class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة إلى قائمة المجموعات
                </a>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card shadow-sm">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">معلومات المجموعة</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>اسم المجموعة:</strong> {{ group.name }}</p>
                                <p><strong>عدد المستخدمين في المجموعة:</strong> {{ group.user_set.count }}</p>
                                <p><strong>المستخدمين:</strong> 
                                    {% for user in group.user_set.all %}
                                    <span class="badge bg-primary">{{ user.username }}</span>
                                    {% empty %}
                                    <span class="text-muted">لا يوجد مستخدمين في هذه المجموعة</span>
                                    {% endfor %}
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="alert alert-info">
                            <h6 class="alert-heading">
                                <i class="fas fa-info-circle me-2"></i>
                                معلومات هامة حول صلاحيات المجموعة
                            </h6>
                            <ul class="mb-0">
                                <li>الصلاحيات المختارة ستتم إضافتها لجميع المستخدمين في هذه المجموعة.</li>
                                <li>أي تغييرات تجريها على صلاحيات المجموعة ستؤثر فورًا على جميع المستخدمين المنتمين إليها.</li>
                                <li>يمكنك أيضًا إضافة <a href="{% url 'administrator:user_list' %}">صلاحيات مباشرة للمستخدمين</a> بشكل منفصل عن المجموعات.</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="permissions-container">
                                <!-- Search Box -->
                                <div class="search-box">
                                    <div class="d-sm-flex align-items-center justify-content-between">
                                        <div class="d-flex align-items-center mb-2 mb-sm-0">
                                            <h6 class="mb-0 ml-2 d-none d-sm-block">صلاحيات المجموعة</h6>
                                            <span class="badge bg-light text-dark permissions-counter me-2">0</span> 
                                            <span>صلاحية محددة</span>
                                        </div>
                                        <div class="input-group">
                                            <input type="text" id="permissionSearch" class="form-control" placeholder="ابحث عن صلاحية...">
                                            <span class="input-group-text">
                                                <i class="fas fa-search"></i>
                                            </span>
                                        </div>
                                    </div>
                                    
                                    <div class="mt-2">
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="checkbox" id="showSelected" value="1">
                                            <label class="form-check-label" for="showSelected">إظهار المحدد فقط</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="checkbox" id="expandAll" value="1">
                                            <label class="form-check-label" for="expandAll">توسيع الكل</label>
                                        </div>
                                    </div>
                                </div>
                                
                                {{ form.permissions.errors }}
                                
                                <div class="permissions-list px-3 py-2">
                                    <!-- Create collections for each permission type -->
                                    {% with view_perms=form.permissions %}
                                    {% with add_perms=form.permissions %}
                                    {% with change_perms=form.permissions %}
                                    {% with delete_perms=form.permissions %}
                                    
                                    <!-- View Permissions Group -->
                                    <div class="permission-group" data-type="view">
                                        <div class="permission-group-header d-flex justify-content-between align-items-center bg-success bg-opacity-10">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-chevron-down me-2 group-toggle-icon"></i>
                                                <h6 class="mb-0 text-success">صلاحيات العرض</h6>
                                                <span class="permission-count bg-success bg-opacity-25 text-success">
                                                    {{ view_perms|length }}
                                                </span>
                                                <span class="badge bg-success selected-count me-1" data-group="view">0</span>
                                            </div>
                                            <div class="group-actions">
                                                <a href="#" class="select-all-type btn btn-sm btn-outline-success" data-type="view">
                                                    <i class="fas fa-check-square me-1"></i>الكل
                                                </a>
                                                <a href="#" class="deselect-all-type btn btn-sm btn-outline-secondary" data-type="view">
                                                    <i class="fas fa-square me-1"></i>لا شيء
                                                </a>
                                            </div>
                                        </div>
                                        <div class="permission-group-content" id="group-view">
                                            <div class="row">
                                                {% for checkbox in form.permissions %}
                                                {% with perm_name=checkbox.choice_label.instance.name %}
                                                {% if 'view_' in perm_name %}
                                                <div class="col-md-6 col-lg-4">
                                                    <div class="permission-checkbox perm-view" data-type="view" data-permname="{{ perm_name }}" data-app="{{ checkbox.choice_label.instance.content_type.app_label|slugify }}">
                                                        <div class="form-check">
                                                            {{ checkbox.tag }}
                                                            <label class="form-check-label" for="{{ checkbox.id_for_label }}" title="{{ perm_name }}">
                                                                <strong>{{ checkbox.choice_label.instance.content_type.app_label }}</strong>: 
                                                                {{ checkbox.choice_label.instance.content_type.model.title }}
                                                                <small class="text-success">(عرض)</small>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                                {% endif %}
                                                {% endwith %}
                                                {% endfor %}
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Add Permissions Group -->
                                    <div class="permission-group" data-type="add">
                                        <div class="permission-group-header d-flex justify-content-between align-items-center bg-primary bg-opacity-10">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-chevron-down me-2 group-toggle-icon"></i>
                                                <h6 class="mb-0 text-primary">صلاحيات الإضافة</h6>
                                                <span class="permission-count bg-primary bg-opacity-25 text-primary">
                                                    {{ add_perms|length }}
                                                </span>
                                                <span class="badge bg-primary selected-count me-1" data-group="add">0</span>
                                            </div>
                                            <div class="group-actions">
                                                <a href="#" class="select-all-type btn btn-sm btn-outline-primary" data-type="add">
                                                    <i class="fas fa-check-square me-1"></i>الكل
                                                </a>
                                                <a href="#" class="deselect-all-type btn btn-sm btn-outline-secondary" data-type="add">
                                                    <i class="fas fa-square me-1"></i>لا شيء
                                                </a>
                                            </div>
                                        </div>
                                        <div class="permission-group-content" id="group-add">
                                            <div class="row">
                                                {% for checkbox in form.permissions %}
                                                {% with perm_name=checkbox.choice_label.instance.name %}
                                                {% if 'add_' in perm_name %}
                                                <div class="col-md-6 col-lg-4">
                                                    <div class="permission-checkbox perm-add" data-type="add" data-permname="{{ perm_name }}" data-app="{{ checkbox.choice_label.instance.content_type.app_label|slugify }}">
                                                        <div class="form-check">
                                                            {{ checkbox.tag }}
                                                            <label class="form-check-label" for="{{ checkbox.id_for_label }}" title="{{ perm_name }}">
                                                                <strong>{{ checkbox.choice_label.instance.content_type.app_label }}</strong>: 
                                                                {{ checkbox.choice_label.instance.content_type.model.title }}
                                                                <small class="text-primary">(إضافة)</small>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                                {% endif %}
                                                {% endwith %}
                                                {% endfor %}
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Change Permissions Group -->
                                    <div class="permission-group" data-type="change">
                                        <div class="permission-group-header d-flex justify-content-between align-items-center bg-warning bg-opacity-10">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-chevron-down me-2 group-toggle-icon"></i>
                                                <h6 class="mb-0 text-warning">صلاحيات التعديل</h6>
                                                <span class="permission-count bg-warning bg-opacity-25 text-warning">
                                                    {{ change_perms|length }}
                                                </span>
                                                <span class="badge bg-warning selected-count me-1" data-group="change">0</span>
                                            </div>
                                            <div class="group-actions">
                                                <a href="#" class="select-all-type btn btn-sm btn-outline-warning" data-type="change">
                                                    <i class="fas fa-check-square me-1"></i>الكل
                                                </a>
                                                <a href="#" class="deselect-all-type btn btn-sm btn-outline-secondary" data-type="change">
                                                    <i class="fas fa-square me-1"></i>لا شيء
                                                </a>
                                            </div>
                                        </div>
                                        <div class="permission-group-content" id="group-change">
                                            <div class="row">
                                                {% for checkbox in form.permissions %}
                                                {% with perm_name=checkbox.choice_label.instance.name %}
                                                {% if 'change_' in perm_name %}
                                                <div class="col-md-6 col-lg-4">
                                                    <div class="permission-checkbox perm-change" data-type="change" data-permname="{{ perm_name }}" data-app="{{ checkbox.choice_label.instance.content_type.app_label|slugify }}">
                                                        <div class="form-check">
                                                            {{ checkbox.tag }}
                                                            <label class="form-check-label" for="{{ checkbox.id_for_label }}" title="{{ perm_name }}">
                                                                <strong>{{ checkbox.choice_label.instance.content_type.app_label }}</strong>: 
                                                                {{ checkbox.choice_label.instance.content_type.model.title }}
                                                                <small class="text-warning">(تعديل)</small>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                                {% endif %}
                                                {% endwith %}
                                                {% endfor %}
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Delete Permissions Group -->
                                    <div class="permission-group" data-type="delete">
                                        <div class="permission-group-header d-flex justify-content-between align-items-center bg-danger bg-opacity-10">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-chevron-down me-2 group-toggle-icon"></i>
                                                <h6 class="mb-0 text-danger">صلاحيات الحذف</h6>
                                                <span class="permission-count bg-danger bg-opacity-25 text-danger">
                                                    {{ delete_perms|length }}
                                                </span>
                                                <span class="badge bg-danger selected-count me-1" data-group="delete">0</span>
                                            </div>
                                            <div class="group-actions">
                                                <a href="#" class="select-all-type btn btn-sm btn-outline-danger" data-type="delete">
                                                    <i class="fas fa-check-square me-1"></i>الكل
                                                </a>
                                                <a href="#" class="deselect-all-type btn btn-sm btn-outline-secondary" data-type="delete">
                                                    <i class="fas fa-square me-1"></i>لا شيء
                                                </a>
                                            </div>
                                        </div>
                                        <div class="permission-group-content" id="group-delete">
                                            <div class="row">
                                                {% for checkbox in form.permissions %}
                                                {% with perm_name=checkbox.choice_label.instance.name %}
                                                {% if 'delete_' in perm_name %}
                                                <div class="col-md-6 col-lg-4">
                                                    <div class="permission-checkbox perm-delete" data-type="delete" data-permname="{{ perm_name }}" data-app="{{ checkbox.choice_label.instance.content_type.app_label|slugify }}">
                                                        <div class="form-check">
                                                            {{ checkbox.tag }}
                                                            <label class="form-check-label" for="{{ checkbox.id_for_label }}" title="{{ perm_name }}">
                                                                <strong>{{ checkbox.choice_label.instance.content_type.app_label }}</strong>: 
                                                                {{ checkbox.choice_label.instance.content_type.model.title }}
                                                                <small class="text-danger">(حذف)</small>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                                {% endif %}
                                                {% endwith %}
                                                {% endfor %}
                                            </div>
                                        </div>
                                    </div>
                                    
                                    {% endwith %}
                                    {% endwith %}
                                    {% endwith %}
                                    {% endwith %}
                                </div>

                                <!-- Stats Footer -->
                                <div class="permissions-stats">
                                    <div>
                                        <span class="total-permissions"></span> صلاحية متاحة | 
                                        <span class="selected-permissions"></span> صلاحية محددة
                                    </div>
                                    <div>
                                        <span class="stat-badge bg-success">عرض <span id="view-count">0</span></span>
                                        <span class="stat-badge bg-primary">إضافة <span id="add-count">0</span></span>
                                        <span class="stat-badge bg-warning">تعديل <span id="change-count">0</span></span>
                                        <span class="stat-badge bg-danger">حذف <span id="delete-count">0</span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                        <a href="{% url 'administrator:group_list' %}" class="btn btn-outline-secondary">
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ صلاحيات المجموعة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Initialize counters
        updateCounts();
        
        // Toggle permission groups expand/collapse
        $('.permission-group-header').on('click', function(e) {
            if (!$(e.target).hasClass('btn') && $(e.target).parents('.btn').length === 0) {
                var content = $(this).siblings('.permission-group-content');
                content.slideToggle(200);
                $(this).toggleClass('collapsed');
            }
        });
        
        // Search functionality for permissions
        $('#permissionSearch').on('keyup', function() {
            var value = $(this).val().toLowerCase();
            $('.permission-checkbox').filter(function() {
                var matches = $(this).text().toLowerCase().indexOf(value) > -1;
                $(this).toggle(matches);
                
                // Show the parent group if at least one permission is visible
                var parent = $(this).closest('.permission-group');
                var hasVisibleChildren = parent.find('.permission-checkbox:visible').length > 0;
                parent.toggle(hasVisibleChildren);
            });
        });
        
        // Show only selected permissions when checkbox is toggled
        $('#showSelected').on('change', function() {
            if ($(this).prop('checked')) {
                $('.permission-checkbox').each(function() {
                    var isChecked = $(this).find('input[type="checkbox"]').prop('checked');
                    $(this).toggle(isChecked);
                    
                    // Show the parent group if at least one permission is visible
                    var parent = $(this).closest('.permission-group');
                    var hasVisibleChildren = parent.find('.permission-checkbox:visible').length > 0;
                    parent.toggle(hasVisibleChildren);
                });
            } else {
                $('.permission-checkbox').show();
                $('.permission-group').show();
            }
        });
        
        // Expand/collapse all permission groups
        $('#expandAll').on('change', function() {
            if ($(this).prop('checked')) {
                $('.permission-group-content').slideDown(200);
                $('.permission-group-header').removeClass('collapsed');
            } else {
                $('.permission-group-content').slideUp(200);
                $('.permission-group-header').addClass('collapsed');
            }
        });
        
        // Quick select/deselect all for a permission type
        $('.select-all-type').on('click', function(e) {
            e.preventDefault();
            var type = $(this).data('type');
            $('.permission-checkbox[data-type="' + type + '"] input[type="checkbox"]').prop('checked', true);
            updateCounts();
        });
        
        $('.deselect-all-type').on('click', function(e) {
            e.preventDefault();
            var type = $(this).data('type');
            $('.permission-checkbox[data-type="' + type + '"] input[type="checkbox"]').prop('checked', false);
            updateCounts();
        });
        
        // Update counts when any checkbox is changed
        $('input[type="checkbox"]').on('change', function() {
            updateCounts();
        });
        
        // Function to update all counts and statistics
        function updateCounts() {
            // Total permissions count
            var totalPerms = $('.permission-checkbox').length;
            $('.total-permissions').text(totalPerms);
            
            // Selected permissions count
            var selectedPerms = $('.permission-checkbox input[type="checkbox"]:checked').length;
            $('.selected-permissions').text(selectedPerms);
            $('.permissions-counter').text(selectedPerms);
            
            // View, add, change, delete counts
            var viewCount = $('.perm-view input[type="checkbox"]:checked').length;
            var addCount = $('.perm-add input[type="checkbox"]:checked').length;
            var changeCount = $('.perm-change input[type="checkbox"]:checked').length;
            var deleteCount = $('.perm-delete input[type="checkbox"]:checked').length;
            
            $('#view-count').text(viewCount);
            $('#add-count').text(addCount);
            $('#change-count').text(changeCount);
            $('#delete-count').text(deleteCount);
            
            // Update permission type selected counts
            $('.permission-group').each(function() {
                var permType = $(this).data('type');
                if (permType) {
                    var checkedCount = $('.permission-checkbox[data-type="' + permType + '"] input[type="checkbox"]:checked').length;
                    $('.selected-count[data-group="' + permType + '"]').text(checkedCount);
                    
                    // Update styling based on count
                    if (checkedCount > 0) {
                        $('.selected-count[data-group="' + permType + '"]').removeClass('bg-light text-dark');
                    } else {
                        $('.selected-count[data-group="' + permType + '"]').addClass('bg-light text-dark');
                    }
                }
            });
        }
    });
</script>
{% endblock %}
