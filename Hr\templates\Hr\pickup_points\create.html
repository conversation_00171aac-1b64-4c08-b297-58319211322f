{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-2 text-gray-800">{{ title }}</h1>
    <p class="mb-4">إنشاء نقطة تجمع جديدة في النظام.</p>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">بيانات نقطة التجمع</h6>
        </div>
        <div class="card-body">
            <form method="post">
                {% csrf_token %}
                
                <div class="form-group row">
                    <label for="{{ form.name.id_for_label }}" class="col-sm-2 col-form-label">الاسم</label>
                    <div class="col-sm-10">
                        {{ form.name }}
                        {% if form.name.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.name.errors }}
                        </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="form-group row">
                    <label for="{{ form.address.id_for_label }}" class="col-sm-2 col-form-label">العنوان</label>
                    <div class="col-sm-10">
                        {{ form.address }}
                        {% if form.address.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.address.errors }}
                        </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="form-group row">
                    <label for="{{ form.coordinates.id_for_label }}" class="col-sm-2 col-form-label">الإحداثيات</label>
                    <div class="col-sm-10">
                        {{ form.coordinates }}
                        {% if form.coordinates.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.coordinates.errors }}
                        </div>
                        {% endif %}
                        <small class="form-text text-muted">أدخل الإحداثيات بصيغة "خط العرض,خط الطول" مثل "24.7136,46.6753"</small>
                    </div>
                </div>
                
                <div class="form-group row">
                    <label for="{{ form.description.id_for_label }}" class="col-sm-2 col-form-label">الوصف</label>
                    <div class="col-sm-10">
                        {{ form.description }}
                        {% if form.description.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.description.errors }}
                        </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="form-group row">
                    <label for="{{ form.car.id_for_label }}" class="col-sm-2 col-form-label">السيارة</label>
                    <div class="col-sm-10">
                        {{ form.car }}
                        {% if form.car.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.car.errors }}
                        </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="form-group row">
                    <div class="col-sm-2">الحالة</div>
                    <div class="col-sm-10">
                        <div class="form-check">
                            {{ form.is_active }}
                            <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                نشط
                            </label>
                            {% if form.is_active.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.is_active.errors }}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="form-group row">
                    <div class="col-sm-10 offset-sm-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ
                        </button>
                        <a href="{% url 'Hr:pickup_points:list' %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
