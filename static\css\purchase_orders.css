/* Purchase Orders CSS */

/* Custom Variables */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

/* General Styles */
body {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
}

/* Card Styles */
.card {
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}

.card-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    padding: 1rem;
}

.card-body {
    padding: 1.25rem;
}

/* Dashboard Stats */
.stat-card {
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

/* Table Styles */
.table th {
    font-weight: 600;
    background-color: rgba(0, 0, 0, 0.03);
}

.table-hover tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* Status Badges */
.badge {
    padding: 0.5em 0.75em;
    font-weight: 500;
}

/* Form Styles */
.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Button Styles */
.btn {
    border-radius: 0.375rem;
    padding: 0.375rem 0.75rem;
    font-weight: 500;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

/* Action Buttons */
.action-buttons .btn {
    margin-right: 0.25rem;
}

/* RTL Specific Styles */
[dir="rtl"] .me-1, 
[dir="rtl"] .me-2, 
[dir="rtl"] .me-3 {
    margin-right: 0 !important;
}

[dir="rtl"] .me-1 {
    margin-left: 0.25rem !important;
}

[dir="rtl"] .me-2 {
    margin-left: 0.5rem !important;
}

[dir="rtl"] .me-3 {
    margin-left: 1rem !important;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .card-title {
        font-size: 1.25rem;
    }
    
    .table-responsive {
        overflow-x: auto;
    }
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    body {
        font-size: 12pt;
    }
}

/* Custom Animations */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Custom Components */
.avatar-circle {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    background-color: var(--primary-color);
    color: white;
}

/* Custom Utilities */
.border-dashed {
    border-style: dashed !important;
}

.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}
