body {
    font-family: 'Cairo', sans-serif;
    direction: rtl;
    text-align: right;
    background-color: #f8f9fa;
}

.card {
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn {
    border-radius: 8px;
}

/* Add hover effects for buttons */
.btn:hover {
    opacity: 0.9;
    transform: scale(1.02);
    transition: all 0.3s ease-in-out;
}

.table th, .table td {
    vertical-align: middle;
}

h2 {
    color: #343a40;
    margin-bottom: 1.5rem;
}

/* Enhance navigation bar */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    background-color: #343a40;
    border-bottom: 2px solid #ffc107;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.nav-link {
    color: #ffffff !important;
    font-size: 1.1rem;
}

.nav-link:hover {
    color: #ffc107 !important;
}

.alert {
    border-radius: 8px;
}

/* Improve form input styles */
input[type="text"], input[type="date"], select, textarea {
    border: 1px solid #ced4da;
    border-radius: 5px;
    padding: 10px;
    font-size: 1rem;
}

input[type="text"]:focus, input[type="date"]:focus, select:focus, textarea:focus {
    border-color: #80bdff;
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
    outline: none;
}

/* Footer styles */
footer {
    background-color: #343a40;
    color: #ffffff;
    padding: 20px 0;
    text-align: center;
    margin-top: 20px;
}

footer a {
    color: #ffc107;
    text-decoration: none;
}

footer a:hover {
    text-decoration: underline;
}