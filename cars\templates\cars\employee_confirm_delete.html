{% extends 'cars\base.html' %}

{% block title %}حذف موظف - نظام إدارة نشاط النقل{% endblock %}

{% block header %}تأكيد حذف الموظف{% endblock %}

{% block content %}
    <div class="card">
        <div class="card-body">
            <div class="alert alert-danger">
                <h5 class="alert-heading">تحذير!</h5>
                <p>هل أنت متأكد من حذف الموظف "{{ employee.name }}"؟</p>
                <p>هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            
            <div class="card mb-3">
                <div class="card-header bg-light">
                    <h5 class="mb-0">بيانات الموظف</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>الاسم:</strong> {{ employee.name }}</p>
                            <p><strong>المسمى الوظيفي:</strong> {{ employee.job_title }}</p>
                            <p><strong>رقم الهاتف:</strong> {{ employee.phone }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>البريد الإلكتروني:</strong> {{ employee.email }}</p>
                            <p><strong>العنوان:</strong> {{ employee.address }}</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <form method="post">
                {% csrf_token %}
                <div class="d-flex justify-content-between">
                    <a href="{% url 'cars:employee_list' %}" class="btn btn-secondary">
                        <i class="bi bi-x-circle"></i> إلغاء
                    </a>
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash"></i> تأكيد الحذف
                    </button>
                </div>
            </form>
        </div>
    </div>
{% endblock %}